const packageJson = require('../package.json');
const { getSignConfig } = require('corporate-services/lib/signature-config');

const PROJECT_NAME = process.env.PROJECT_NAME || packageJson['alfa:meta'].name;
const APP_NAME = `${PROJECT_NAME}-ui`;
const APP_HOST = process.env.HOST || 'localhost';
const APP_PORT = 3000;
const CONTEXT_ROOT = process.env.NODE_ENV === 'production' ? process.env.CONTEXT_ROOT : '';
const AUTH_JWT = process.env.AUTH_JWT || 'http://corp-gateway-test/infra-auth-proxy';
const AUTH_PAGE = process.env.AUTH_PAGE || 'https://testlink.alfabank.ru/webclient/pages';
const AUTH_DEV_ACCESS = process.env.AUTH_DEV_ACCESS !== '0';
const EXTERNAL_SYSTEM_CODE = process.env.EXTERNAL_SYSTEM_CODE || 'ECOB01';
const ALBO_EXTERNAL_SYSTEM_CODE = process.env.ALBO_EXTERNAL_SYSTEM_CODE || 'LKB01';
const AUTHORIZED_APPLICATION_ID = process.env.AUTHORIZED_APPLICATION_ID || 'credit-products';
const K8S_APP = process.env.K8S_APP == '1';
const APP_MOCKS = process.env.APP_MOCKS == '1';
const ECO_REDIRECT_PROTOCOL = process.env.ECO_REDIRECT_PROTOCOL || 'http';
const GATEWAY_HOST =
    process.env.GATEWAY_HOST || (AUTH_DEV_ACCESS ? 'corp-gateway-test' : 'corp-gateway-prod');
const AK_HOST = process.env.AK_HOST || '/ak';
const AK_MODULE_HOST = '/ak';

/* Reactive Clients OAuth */
const REACTIVE_CLIENTS_OAUTH = {
    'client-id': process.env.REACTIVE_CLIENTS_OAUTH_CLIENT_ID || 'nib-corp-credit-products-ui',
    'client-secret':
        process.env.REACTIVE_CLIENTS_OAUTH_CLIENT_SECRET || 'nib-corp-credit-products-ui-secret',
    'token-uri':
        process.env.REACTIVE_CLIENTS_OAUTH_TOKEN_URI ||
        `http://${GATEWAY_HOST}.moscow.alfaintra.net/corp-credit-products-gateway/public/keycloak/auth/realms/corporate/protocol/openid-connect/token`,
};

/* Sign Online */
const SIGN_ONLINE_HOST =
    process.env.SIGN_ONLINE_HOST ||
    (AUTH_DEV_ACCESS ? 'testjmb.alfabank.ru' : 'anketa.alfabank.ru');
const SIGN_ONLINE_ENDPOINT = process.env.SIGN_ONLINE_ENDPOINT || '/ONLC/rb-sign-flow-api';
const SIGN_ONLINE_PROTOCOL = process.env.SIGN_ONLINE_PROTOCOL || 'https';
const SIGN_ONLINE_LOGIN = process.env.SIGN_ONLINE_LOGIN || '';
const SIGN_ONLINE_PASSWORD = process.env.SIGN_ONLINE_PASSWORD || '';

/* Sign Module */
const SIGN_MODULE_HOST = process.env.SIGN_MODULE_HOST || GATEWAY_HOST;
const SIGN_MODULE_PROTOCOL = process.env.SIGN_MODULE_PROTOCOL || 'https';
const SIGN_MODULE_HASH = process.env.SIGN_MODULE_HASH || '';
const SIGN_MODULE_HASH_TEST =
    process.env.SIGN_MODULE_HASH_TEST || 'Y29ycC1jcmVkaXQtcHJvZHVjdHM6Y29ycC1jcmVkaXQtcHJvZHVjdHM=';
const SIGN_MODULE_ENV = process.env.SIGN_MODULE_ENV === 'production';

/* Corporate-Logger */
const TRACE_ID_HEADER_NAME = 'X-B3-TraceId';
const LOGSTASH_PORT = process.env.LOGSTASH_PORT || 5959;
const LOGSTASH_SYSLOG_TAGS = process.env.LOGSTASH_SYSLOG_TAGS || '';

const SUPPORT_PHONE_FOR_MOSCOW_AND_ABROAD =
    process.env.SUPPORT_PHONE_FOR_MOSCOW_AND_ABROAD || '+74957555858';
const SUPPORT_PHONE_FOR_ANY_CITY_IN_RUSSIA =
    process.env.SUPPORT_PHONE_FOR_ANY_CITY_IN_RUSSIA || '+78001007733';

const DEADLINE_FOR_ACCEPTING_APPLICATIONS_MOSCOW_TIME =
    process.env.DEADLINE_FOR_ACCEPTING_APPLICATIONS_MOSCOW_TIME || '19:01';

const SERVICES = {
    albo: {
        name: 'albo-api',
    },
    creditOffers: {
        name: 'credit-offers-api',
    },
    creditProducts: {
        name: 'credit-products-api',
    },
    corpLoanStatements: {
        name: 'loan-statements-api',
        endpoint: 'api',
        protocol: 'http',
        port: '',
    },
    creditProductsV2: {
        name: 'core-credit-products-api-v2',
    },
    creditProductsRestV2: {
        name: 'credit-products-api-v2',
        mksAuthConfig: {
            projectId: 'corp-credit-products-ui',
            clientId: 'nib-corp-credit-products-ui',
            gatewayUrl: `http://${GATEWAY_HOST}.moscow.alfaintra.net/corp-credit-products-gateway/public/keycloak/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    coreCreditProductsRestV2: {
        name: 'core-credit-products-api-v2',
        endpoint: 'api',
        protocol: 'http',
        port: '',
        mksAuthConfig: {
            projectId: 'corp-credit-products-ui',
            clientId: 'nib-corp-credit-products-ui',
            gatewayUrl: `http://${GATEWAY_HOST}.moscow.alfaintra.net/corp-credit-products-gateway/public/keycloak/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    overdraft: {
        name: 'overdraft-api',
        endpoint: '/tapi/overdraft',
        protocol: 'http',
        port: '',
    },
    earlyRepaymentRest: {
        name: 'early-repayment-api',
        protocol: 'http',
        port: '',
    },
    corpCreditRequestRest: {
        name: 'credit-request-api',
        endpoint: 'api',
        protocol: 'http',
        port: '',
        mksAuthConfig: {
            projectId: 'corp-credit-products-ui',
            clientId: 'nib-corp-credit-products-ui',
            gatewayUrl: `http://${GATEWAY_HOST}.moscow.alfaintra.net/corp-credit-products-gateway/public/keycloak/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    creditDocumentCirculation: {
        name: 'credit-document-circulation-api',
    },
    corpCreditDocumentCirculation: {
        name: 'credit-document-circulation-api',
    },
    productionCalendar: {
        name: 'production-calendar-api',
    },
    creditApplication: {
        name: 'credit-processing-api',
        project: 'corp-credit-application',
    },
    creditRequestDocuments: {
        name: 'request-documents-api',
        project: 'corp-credit-application',
    },
    creditRequestDocumentsRest: {
        name: 'request-documents-api',
        project: 'corp-credit-application',
        endpoint: 'api',
    },
    corpLoanWatchlist: {
        name: 'loan-watchlist-api',
    },
    loanStatements: {
        name: 'loan-statements-api',
    },
    dadata: {
        name: 'dadata-api',
        project: 'corp-core',
    },
    credits: {
        name: 'credit-requests-api',
        project: 'corp-credit-application',
    },
    sharedUI: {
        name: 'shared-ui',
        host: GATEWAY_HOST,
        protocol: ECO_REDIRECT_PROTOCOL,
        port: '',
        endpoint: '/corp-shared-ui',
    },
    correspondence: {
        name: 'corp-messages-api',
        host: GATEWAY_HOST,
        port: '',
    },
    creditRequest: {
        name: 'corp-new-credit-api',
        projectId: 'credit-products',
        host: GATEWAY_HOST,
        port: '',
    },
    csi: {
        name: 'corp-dashboard-customer-satisfaction-index-api',
        host: `${GATEWAY_HOST}/corp-dashboard-customer-satisfaction-index-api`,
        port: '',
        endpoint: '/tapi/csi',
    },

    /* МКС */
    mksGlobalFeatures: {
        name: 'mks-global-features-api',
        host: GATEWAY_HOST,
        endpoint: '/mks-gateway/secure/mks-global-features-api/api',
        port: '',
        mksAuthConfig: {
            projectId: 'credit-products',
            clientId: 'nib-credit-products',
            gatewayUrl: `http://${GATEWAY_HOST}/mks-gateway/public/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    mksCustomers: {
        name: 'mks-customers-api',
        host: GATEWAY_HOST,
        endpoint: '/mks-gateway/secure/mks-customers-api/api',
        port: '',
        mksAuthConfig: {
            projectId: 'credit-products',
            clientId: 'nib-credit-products',
            gatewayUrl: `http://${GATEWAY_HOST}/mks-gateway/public/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    mksCustomersUI: {
        name: 'mks-customers-api',
        host: GATEWAY_HOST,
        endpoint: '/mks-gateway/secure/mks-customers-api/api',
        port: '',
        mksAuthConfig: {
            projectId: 'corp-credit-products-ui',
            clientId: 'nib-corp-credit-products-ui',
            gatewayUrl: `http://${GATEWAY_HOST}/mks-gateway/public/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    mksUsers: {
        name: 'mks-users-api',
        host: GATEWAY_HOST,
        endpoint: '/mks-gateway/secure/mks-users-api/api',
        port: '',
        mksAuthConfig: {
            projectId: 'corp-credit-products-ui',
            clientId: 'nib-corp-credit-products-ui',
            gatewayUrl: `http://${GATEWAY_HOST}/mks-gateway/public/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    mksRoles: {
        name: 'mks-roles-api',
        host: GATEWAY_HOST,
        endpoint: '/corp-role-model-gateway/secure/mks-roles-api/api',
        port: '',
        mksAuthConfig: {
            projectId: 'corp-credit-products-ui',
            clientId: 'nib-corp-credit-products-ui',
            gatewayUrl: `http://${GATEWAY_HOST}/corp-role-model-gateway/public/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    mksAccounts: {
        name: 'mks-accounts-api',
        host: GATEWAY_HOST,
        endpoint: '/mks-gateway/secure/mks-accounts-api/api',
        port: '',
        mksAuthConfig: {
            projectId: 'credit-products',
            clientId: 'nib-credit-products',
            gatewayUrl: `http://${GATEWAY_HOST}/mks-gateway/public/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    mksPermissions: {
        name: 'mks-permissions-api',
        host: GATEWAY_HOST,
        endpoint: '/corp-role-model-gateway/secure/mks-permissions-api/api',
        port: '',
        mksAuthConfig: {
            projectId: 'credit-products',
            clientId: 'nib-credit-products',
            gatewayUrl: `http://${GATEWAY_HOST}/corp-role-model-gateway/public/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    mksProxies: {
        name: 'mks-proxies-api',
        host: GATEWAY_HOST,
        endpoint: '/corp-role-model-gateway/secure/mks-proxies-api/api',
        port: '',
        mksAuthConfig: {
            projectId: 'credit-products',
            clientId: 'nib-credit-products',
            gatewayUrl: `http://${GATEWAY_HOST}/corp-role-model-gateway/public/auth/realms/corporate/protocol/openid-connect/token`,
        },
    },
    ufrAkCorpGatewayRest: {
        name: 'ufr-ak-corp-gateway-service-api',
        host: 'ak-dev-sys.moscow.alfaintra.net',
        endpoint: '/ak-internal/ufr-ak-corp-gateway-service-api',
        protocol: 'https',
        mksAuthConfig: {
            projectId: 'corp-credit-products-ui',
            clientId: 'nib-corp-credit-products-ui',
            gatewayUrl:
                'https://idp-test-api.alfaintra.net/auth/realms/ak/protocol/openid-connect/token',
        },
    },
    statusModel: {
        name: 'crmmb-status-model-core-api',
        host: `crmmbdev.moscow.alfaintra.net`,
        endpoint: '/crmmb-status-model-gateway/secure/crmmb-status-model-core-api',
        mksAuthConfig: {
            projectId: 'corp-credit-products-ui',
            clientId: 'corp-credit-products',
            gatewayUrl:
                'https://idp-api-test.alfaintra.net/auth/realms/credit-application/protocol/openid-connect/token',
        },
    },
};

const config = {
    server: {
        port: APP_PORT,
        host: APP_HOST,
        shouldUseMocks: APP_MOCKS,
    },

    buildConfig: {
        targetDir: '.build',
        assetsDir: 'assets',
    },

    proxyAssets: {
        host: 'localhost',
        port: 9090,
    },

    proxySharedAssets: { host: APP_HOST, sharedAssetsContextRoot: 'corp-shared-ui' },

    devtools: true,

    csp: {},

    app: {
        pageTitle: 'Кредитные продукты',
        alfaMetricsId: PROJECT_NAME,
        projectName: PROJECT_NAME,
        authPage: AUTH_PAGE,
        contextRoot: CONTEXT_ROOT,
        devAccess: AUTH_DEV_ACCESS,
        version: packageJson.version,
        tranchesPerPage: 10,
        navigationPaneSettings: {
            label: '',
            linkText: '',
            onClick: null,
            isVisible: false,
        },
        externalRedirect: {
            rublePayment: 'https://link.alfabank.ru/ruble-payment/self',
            alfaCreditRequestGuarantee: `${AK_HOST}/request/guarantee`,
            alfaCreditRequestDocument: `${AK_HOST}/request/document`,
            alfaCreditRequestCredit: `${AK_HOST}/request/credit`,
            alfaCreditRequestBank: `${AK_HOST}/request/bank`,
            alfaCreditDocumentQuery: `${AK_HOST}/request/document-query`,
            alfaCreditHost: AK_MODULE_HOST,
            creditsb: '/creditsb',
        },
        redirect: {
            dashboard: '/dashboard',
            paylist: '/paylist',
            rpay: '/payment',
            tariffs: '/tariffs',
            overdrafts: '/overdrafts',
            landings: '/credit-products-landings',
            credits: '/credits',
            overformAgreementContextRoot: '/credit-overform',
            creditTrancheApp: '/credit-tranche',
            creditFormsApp: '/credit-forms',
        },
        digitalSalesLandings: {
            businessCreditRefinancing: 'cb8cf894-d2e9-41c2-a2cc-f10eb430a542',
            businessAutoCredit: 'cd4e1082-1b10-4bc9-8ca1-3864f97a36d4',
            businessMortgage: 'e56665da-3ee9-447d-8ffe-94934fe1befc',
            businessCreditWithStateSupportMMB: 'b40df631-4018-408a-ace8-eb8c65edf9b3',
            overdraft: 'ca53867b-c6bf-45cc-aa33-dd83aea6a161',
            refillableCreditLine: 'da6bafc4-aa0c-499a-a85c-cc2de090e353',
            overdraftWithProlongation: '02a6bd43-b44f-47ff-91d7-ddcb4bc014d1',
            refillableCreditLineWithProlongation: '5c76e19a-1bb2-498d-b1b3-c07765b1894b',
            leasing: '5201ead2-a3d7-4052-9f80-1c5eba908ff3',
        },
        enableEarlyRepaymentAutomatedProcessing: true,
        supportPhoneForMoscowAndAbroad: SUPPORT_PHONE_FOR_MOSCOW_AND_ABROAD,
        supportPhoneForAnyCityInRussia: SUPPORT_PHONE_FOR_ANY_CITY_IN_RUSSIA,
        authorizedApplicationId: AUTHORIZED_APPLICATION_ID,
        deadlineForAcceptingApplicationsMoscowTime: DEADLINE_FOR_ACCEPTING_APPLICATIONS_MOSCOW_TIME,
        timeAfterDeadlineError: false,
        currentTime: '',
        error: '',
        signSource: 'LOAN_MB',
        signMethod: 'ses',
        gatewayHost: GATEWAY_HOST,
    },

    auth: {
        tokenCookie: 'token',
        tokenParam: 'token',
        devAccessParam: 'profileId',
        devAccessCookie: 'profileId',
        devAccess: AUTH_DEV_ACCESS,
        jwt: AUTH_JWT,
    },

    logger: {
        local: true,
        app_id: APP_NAME,
        app_host: APP_HOST,
        app_port: APP_PORT,
        rsyslog_host: APP_HOST,
        rsyslog_port: LOGSTASH_PORT,
        rsyslog_tag: LOGSTASH_SYSLOG_TAGS,
        trace_id_header_name: TRACE_ID_HEADER_NAME,
        json: K8S_APP,
    },

    services: Object.keys(SERVICES).reduce((result, serviceName) => {
        const service = SERVICES[serviceName];
        const project = service.project ? service.project : PROJECT_NAME;
        const port = service.port ? service.port : '';

        let host = service.host ? service.host : `${APP_HOST}/${project}-${service.name}`;

        if (K8S_APP) {
            host = service.host ? service.host : `${service.name}.${project}`;
        }

        if (APP_MOCKS) {
            host = APP_HOST;
        }

        return {
            ...result,
            [serviceName]: {
                ...service,
                host,
                port,
                isOnSeparateHosts: true,
            },
        };
    }, {}),

    externalSystemCode: EXTERNAL_SYSTEM_CODE,
    alboExternalSystemCode: ALBO_EXTERNAL_SYSTEM_CODE,

    'reactive-clients': {
        oauth: REACTIVE_CLIENTS_OAUTH,
    },
};

config.services.signature = getSignConfig({
    protocol: SIGN_MODULE_PROTOCOL,
    host: SIGN_MODULE_HOST,
    hash: SIGN_MODULE_HASH,
    hashTest: SIGN_MODULE_HASH_TEST,
    isProduction: SIGN_MODULE_ENV,
});

config.services.signOnline = {
    protocol: SIGN_ONLINE_PROTOCOL,
    host: SIGN_ONLINE_HOST,
    endpoint: SIGN_ONLINE_ENDPOINT,
    headers: {
        login: SIGN_ONLINE_LOGIN,
        password: SIGN_ONLINE_PASSWORD,
    },
};

module.exports = config;
