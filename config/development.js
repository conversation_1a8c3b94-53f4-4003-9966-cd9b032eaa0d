module.exports = {
    auth: {
        devAccess: true,
    },
    logger: {
        output: 'stdout-formatted',
    },
    csp: {
        'default-src': "'self'",
        'connect-src':
            "'self' *.alfabank.ru alfabank.gcdn.co alfabank.servicecdn.ru wss://*.alfabank.ru https://localhost:24738 http://localhost:24738 https://devlink.alfabank.ru https://testlink.alfabank.ru",
        'script-src':
            "'self' *.alfabank.ru 'unsafe-inline' https://localhost:24738 https://devlink.alfabank.ru https://testlink.alfabank.ru",
        'frame-src': "'self' *.alfabank.ru blob: 'self'",
        'img-src':
            "'self' *.alfabank.ru alfabank.gcdn.co alfabank.servicecdn.ru alfabank.st data: 'self'",
        'font-src': "'self' alfabank.gcdn.co alfabank.servicecdn.ru data: 'self'",
        'object-src': "'none'",
        'style-src':
            "'self' 'unsafe-inline' alfabank.servicecdn.ru click.alfabank.ru link.alfabank.ru",
    },
};
