/* eslint-disable import/no-extraneous-dependencies */
import { type OverrideFile } from 'arui-scripts';

const isIstanbulEnabled = process.env.USE_ISTANBUL === 'enabled';

const overrides: OverrideFile = {
    babelClient: (config) => {
        let { plugins } = config;

        if (isIstanbulEnabled) {
            plugins = [...plugins, ['istanbul']];
        }

        return {
            ...config,
            plugins,
        };
    },
};

export default overrides;
