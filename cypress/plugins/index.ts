/* eslint-disable import/no-extraneous-dependencies */

import codeCoverage from '@cypress/code-coverage/task';

export const addPlugins = (on: Cypress.PluginEvents, config: Cypress.PluginConfigOptions) => {
    codeCoverage(on, config);

    on('before:browser:launch', (browser, launchOptions) => {
        // Отключаем инструменты разработчика в CI для улучшения производительности
        if (browser.name === 'chrome' && browser.isHeadless) {
            launchOptions.args.push('--disable-dev-shm-usage');
            launchOptions.args.push('--disable-gpu');
            launchOptions.args.push('--disable-extensions');
            launchOptions.args.push('--no-sandbox');
            launchOptions.args.push('--disable-software-rasterizer');
        }

        return launchOptions;
    });

    return config;
};
