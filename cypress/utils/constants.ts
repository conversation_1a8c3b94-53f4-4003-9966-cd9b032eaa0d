import { CURRENCY_RUR } from '../../src/server/mocks/data/constants';

const gracePeriodToDate = Date.now().valueOf() / 1000;

export const CREDIT_CARD_1 = {
    docNumber: '123',
    requisites: {
        product: '3',
        productCode: 'OVERNZUC',
        limit: {
            amount: 150000000,
            currency: CURRENCY_RUR,
        },
        toDate: {
            seconds: gracePeriodToDate + 1000000,
        },
    },
    availableAmount: { amount: 150000000, currency: CURRENCY_RUR },
    frontParams: {
        debtStatus: null,
    },
    gracePeriod: {
        gracePeriodActualEndDate: {
            seconds: null,
        },
        gracePeriodToDate: {
            seconds: gracePeriodToDate + 1000000,
        },
    },
};

export const CREDIT_CARD_2 = {
    docNumber: '123',
    requisites: {
        product: '3',
        productCode: 'OVERNZUC',
        toDate: {
            seconds: gracePeriodToDate + 1000000,
        },
    },
    frontParams: {
        debtStatus: 'MIN_DEBT_PAYMENT_CALCULATED',
    },
    debts: {
        loan: {
            minDebtToPay: {
                amount: 100,
            },
            fineDebt: {
                amount: 3000,
            },
        },
    },
    gracePeriod: {
        gracePeriodActualEndDate: {
            seconds: gracePeriodToDate + 1000000,
        },
        gracePeriodToDate: {
            seconds: gracePeriodToDate + 1000000,
        },
    },
    summary: {
        totalFine: {
            amount: 2000,
        },
    },
};

export const CREDIT_CARD_3 = {
    docNumber: '123',
    frontParams: {
        debtStatus: 'TWO_OR_MORE_MIN_DEBT_PAYMENTS_OVERDUE',
    },
    requisites: {
        product: '3',
        productCode: 'OVERNZUC',
        toDate: {
            seconds: gracePeriodToDate + 1000000,
        },
    },
    gracePeriod: {
        gracePeriodActualEndDate: null,
        gracePeriodToDate: {
            seconds: gracePeriodToDate - 100000,
        },
    },
    debts: {
        loan: {
            overdueDebt: {
                amount: 2000,
                currency: {
                    unicodeSymbol: '',
                },
            },
        },
    },
};

// grace period ended
export const CREDIT_CARD_4 = {
    docNumber: '123',
    frontParams: {
        debtStatus: 'MIN_DEBT_PAYMENT_IN_CALCULATION',
    },
    requisites: {
        product: '3',
        productCode: 'OVERNZUC',
        toDate: {
            seconds: gracePeriodToDate + 1000000,
        },
    },
    gracePeriod: {
        gracePeriodActualEndDate: {
            seconds: gracePeriodToDate,
        },
        gracePeriodToDate: {
            seconds: gracePeriodToDate,
        },
    },
    debts: {
        loan: {
            minDebtToPay: {
                amount: 0,
            },
            debtToPay: {
                amount: 1,
            },
            minPayDebtCalculationDate: {
                seconds: gracePeriodToDate,
            },
        },
    },
    summary: {
        totalLoanSumToPay: {
            amount: 1999,
        },
    },
};

export const CREDIT_CARD_5 = {
    docNumber: '123',
    frontParams: {
        debtStatus: 'MIN_DEBT_PAYMENT_IN_CALCULATION',
    },
    requisites: {
        product: '3',
        productCode: 'OVERNZUC',
        toDate: {
            seconds: gracePeriodToDate + 1000000,
        },
    },
    gracePeriod: {
        gracePeriodActualEndDate: {
            seconds: gracePeriodToDate + 1000000,
        },
        gracePeriodToDate: {
            seconds: gracePeriodToDate + 1000000,
        },
    },
    debts: {
        loan: {
            minDebtToPay: {
                amount: 0,
            },
            debtToPay: {
                amount: 1,
            },
            minPayDebtCalculationDate: {
                seconds: gracePeriodToDate + 1000000,
            },
        },
    },
    summary: {
        totalLoanSumToPay: {
            amount: 1999,
        },
    },
};

export const CREDIT_CARD_6 = {
    docNumber: '123',
    frontParams: {
        debtStatus: 'MIN_DEBT_PAYMENT_CALCULATED',
    },
    requisites: {
        product: '3',
        productCode: 'OVERNZUC',
        toDate: {
            seconds: gracePeriodToDate - 1000000,
        },
    },
    gracePeriod: {
        gracePeriodActualEndDate: {
            seconds: gracePeriodToDate,
        },
        gracePeriodToDate: {
            seconds: gracePeriodToDate,
        },
    },
    debts: {
        loan: {
            minDebtToPay: {
                amount: 0,
            },
            debtToPay: {
                amount: 1,
            },
            minPayDebtCalculationDate: {
                seconds: gracePeriodToDate,
            },
        },
    },
    summary: {
        totalToPay: {
            amount: 1999,
        },
    },
};

export const CREDIT_CARD_7 = {
    docNumber: '123',
    frontParams: {
        debtStatus: 'MIN_DEBT_PAYMENT_CALCULATED',
    },
    requisites: {
        product: '3',
        productCode: 'OVERNZUC',
        toDate: {
            seconds: gracePeriodToDate + 100000,
        },
    },
    gracePeriod: {
        gracePeriodActualEndDate: {
            seconds: gracePeriodToDate + 100000,
        },
        gracePeriodToDate: {
            seconds: gracePeriodToDate + 100000,
        },
    },
    debts: {
        loan: {
            minDebtToPay: {
                amount: 0,
            },
            debtToPay: {
                amount: 1,
            },
            minPayDebtCalculationDate: {
                seconds: gracePeriodToDate + 100000,
            },
        },
    },
    summary: {
        totalToPay: {
            amount: 199900,
        },
    },
};

export const CREDIT_CARD_8 = {
    docNumber: '123',
    frontParams: {
        debtStatus: 'MIN_DEBT_PAYMENT_CALCULATED',
    },
    requisites: {
        product: '3',
        productCode: 'OVERNZUC',
        toDate: {
            seconds: gracePeriodToDate - 100000,
        },
    },
    gracePeriod: {
        gracePeriodActualEndDate: {
            seconds: gracePeriodToDate + 100000,
        },
        gracePeriodToDate: {
            seconds: gracePeriodToDate + 100000,
        },
    },
    debts: {
        loan: {
            minDebtToPay: {
                amount: 0,
            },
            debtToPay: {
                amount: 1,
            },
            minPayDebtCalculationDate: {
                seconds: gracePeriodToDate + 100000,
            },
        },
    },
    summary: {
        totalToPay: {
            amount: 1999,
        },
    },
};
