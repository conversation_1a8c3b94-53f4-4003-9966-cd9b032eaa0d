const normalizeText = (text: string) =>
    text
        .replace(/\u00A0/g, ' ') // NBSP
        .replace(/\u202F/g, ' ') // THSP
        .replace(/\s+/g, ' ') // SPACES
        .trim();

const checkNormalizedText = (element: Cypress.Chainable<JQuery<HTMLElement>>, includes: string[]) =>
    element
        .should('be.visible')
        .invoke('text')
        .then(normalizeText)
        .should((normalizedText) => {
            includes.forEach((text) => {
                expect(normalizedText).to.include(text);
            });
        });

const checkNormalizedTextByGet = (className: string, includes: string[], index?: number) => {
    if (index) {
        checkNormalizedText(cy.get(className).eq(index), includes);
    }

    checkNormalizedText(cy.get(className), includes);
};

const checkNormalizedTextByTestId = (testId: string, includes: string[]) =>
    checkNormalizedText(cy.findByTestId(testId), includes);

export {
    normalizeText,
    checkNormalizedText,
    checkNormalizedTextByGet,
    checkNormalizedTextByTestId,
};
