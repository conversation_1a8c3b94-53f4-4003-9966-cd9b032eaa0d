import { getNextYearDate, getTodayDate } from './date-utils';
import { checkNormalizedTextByGet, checkNormalizedTextByTestId } from './text-utils';

const checkDocumentCalendarBlock = (selector: Parameters<typeof cy.get>[0]) => {
    cy.get('.date-range-picker__popover').should(selector);
    cy.findByText('Сегодня').should(selector);
    cy.findByText('Вчера').should(selector);
    cy.findByText('Неделя').should(selector);
    cy.findByText('Месяц').should(selector);
    cy.findByText('Квартал').should(selector);
    cy.findByText('Год').should(selector);
};

const checkDocumentList = ({
    selector,
    listOptions,
    checkSum,
}: {
    selector: Parameters<typeof cy.get>[0];
    listOptions?: Parameters<typeof cy.get>[1];
    checkSum?: boolean;
}) => {
    cy.get('.document-list', listOptions).should('be.visible');

    cy.get('th').contains('дата').should(selector);
    cy.get('th').contains('№ заявления').should(selector);
    cy.get('th').contains('статус').should(selector);

    if (checkSum) {
        cy.get('th').contains('сумма').should(selector);
    }
};

const checkEmptyStateForAkWidget = () => {
    cy.get('body').then(($body) => {
        if ($body.find('.error-state__content').length > 0) {
            cy.get('.error-state__content').within(() => {
                cy.get('h1').should('contain.text', 'Не получилось загрузить');

                cy.get('.error-state__text').should(
                    'contain.text',
                    'Уже исправляем. Попробуйте ещё раз или зайдите позже',
                );

                cy.findByText('Попробовать ещё раз').should('exist');
            });
        }
    });
};

const checkManagerContacts = () => {
    cy.findByTestId('smart-limits-contacts-manager-name')
        .scrollIntoView()
        .should('be.visible')
        .and('include.text', 'Симанова Наталья Александровна');

    cy.get('.contacts-pane__contact-card')
        .first()
        .should('be.visible')
        .within(() => {
            cy.get('.contacts-pane__contact-card-icon').should('exist').and('be.visible');

            cy.get('.contacts-pane__contact-card-contacts')
                .should('exist')
                .and('be.visible')
                .within(() => {
                    cy.contains('+7 (916) 885-33-98').should('be.visible');
                    cy.contains('для звонков и сообщений').should('be.visible');
                });
        });

    cy.get('.contacts-pane__contact-card')
        .last()
        .should('be.visible')
        .within(() => {
            cy.get('.contacts-pane__contact-card-icon').should('exist').and('be.visible');

            cy.get('.contacts-pane__contact-card-contacts')
                .should('exist')
                .and('be.visible')
                .within(() => {
                    cy.contains('<EMAIL>').should('be.visible');
                    cy.contains('для писем').should('be.visible');
                });
        });
};

const checkSuspensiveConditions = (header: string, _link: string) => {
    cy.findByText('Отлагательные условия', { timeout: 25000 }).should('be.visible').click();
    cy.waitForNetworkIdle('*', '*', 1000);

    cy.findByTestId('status-tag-field').should('be.visible').click();
    cy.findAllByTestId('status-tag-option').eq(0).should('be.visible').and('contain.text', 'Все');
    cy.findAllByTestId('status-tag-option')
        .eq(1)
        .should('be.visible')
        .and('contain.text', 'Действующие');
    cy.findAllByTestId('status-tag-option')
        .eq(2)
        .should('be.visible')
        .and('contain.text', 'Выполненные');

    cy.get('.suspensive-conditions-item')
        .should('be.visible')
        .within(() => {
            checkNormalizedTextByGet('.suspensive-conditions-item__header', [header]);

            checkNormalizedTextByGet('.suspensive-conditions-item__title', [
                'Оформление поручительства',
                'Срок истёк 26.08.2024',
            ]);

            cy.findByText('Нарушено').should('be.visible');
        });

    cy.findByTestId('more-button').should('be.visible').click();
    cy.waitForNetworkIdle('*', '*', 1000);
    cy.findByTestId('status').should('be.visible').and('contain.text', 'Нарушено');

    cy.findByTestId('left-chevron').should('be.visible').click();
    cy.findByTestId('status').should('be.visible').and('contain.text', 'Нарушено');
    cy.findByTestId('right-chevron').should('be.visible').click();
    cy.findByTestId('status').should('be.visible').and('contain.text', 'Нарушено');
    cy.findByTestId('type').should('be.visible').and('contain.text', 'Оформление поручительства');
    cy.findByTestId('subTitle').should('be.visible').and('contain.text', header);
    cy.findByTestId('description')
        .should('be.visible')
        .and('contain.text', 'Оформить поручительство с ООО "УРАЛ ЛОГИСТИКА"');

    cy.findByTestId('send-documents').should('be.visible');
    cy.findByTestId('close-button').should('be.visible').click();

    // TODO: разобраться почему зависает тест
    // cy.findByTestId('suspensive-conditions__send-documents').should('be.visible').click();

    // cy.waitForNetworkIdle('*', '*', 2000);
    // cy.url().should('include', link, { timeout: 90000 });
};

const checkDownloadPaymentSchedule = () => {
    cy.get('.payment-schedule').within(() => {
        checkNormalizedTextByGet('.schedule-download-pane__button', ['Скачать график']);
    });

    cy.get('.schedule-download-pane__button').click();
    cy.findByText('PDF').should('be.visible').click();
    cy.waitForNetworkIdle('*', '*', 1000);
    cy.get('[class*="notification__isVisible"]').should('be.visible');

    cy.get('.schedule-download-pane__button').click();
    cy.findByText('XLS').should('be.visible').click();
    cy.waitForNetworkIdle('*', '*', 1000);
    cy.get('[class*="notification__isVisible"]').should('be.visible');
};

const checkCreditPaymentSchedule = () => {
    cy.get('.schedule-common-heading').contains('Всего платежей: 14').should('be.visible');

    cy.get('.payment-schedule').within(() => {
        cy.findByText('Дата платежа').should('be.visible');
        cy.findByText('Сумма платежа').should('be.visible');
        cy.findByText('Основной долг').should('be.visible');
        cy.findByText('Проценты').should('be.visible');
        cy.findByText('Остаток долга').should('be.visible');
    });

    checkNormalizedTextByGet('.payment-schedule__plate', [
        'График платежей на 30 июля 2012',
        'Оплаченные платежи',
    ]);
};

const checkGuarantyPaymentSchedule = () => {
    cy.findByText('График платежей').should('be.visible').click();

    cy.get('.schedule-common-heading').contains('Всего платежей: 7').should('be.visible');

    cy.get('.payment-schedule').within(() => {
        cy.findByText('Дата платежа').should('be.visible');
        cy.findByText('Сумма платежа').should('be.visible');
    });

    checkNormalizedTextByGet('.payment-schedule__plate', ['График платежей на 30 июля 2012']);
};

const checkCreditConditions = () => {
    const date = `${getTodayDate()} - ${getNextYearDate()}`;

    cy.findByTestId('conditions-pane').within(() => {
        checkNormalizedTextByTestId('sum', ['Сумма', '100 000,00 ₽']);
        checkNormalizedTextByTestId('rate', ['Ставка', '16.3% годовых']);
        checkNormalizedTextByTestId('term', ['Действие договора', date]);
    });
};

const checkCreditDocumentation = () => {
    cy.findByText('Кредитная документация').should('be.visible').click();

    checkNormalizedTextByGet('.signed-credit-documents__title', ['Кредитная документация']);

    cy.get('.signed-credit-documents__documents').within(() => {
        const className = '.signed-credit-documents-item';

        checkNormalizedTextByGet(className, ['Кредитный договор'], 0);
        cy.get('.signed-credit-documents-item__download-button-wrapper').first().click();
        cy.readFile('cypress/downloads/Кредитный договор.pdf').should('exist');

        checkNormalizedTextByGet(className, ['Анкета-согласие'], 1);
        cy.get('.signed-credit-documents-item__download-button-wrapper').last().click();
        cy.readFile('cypress/downloads/Анкета-согласие.pdf').should('exist');

        cy.get(className).should('have.length', 2);
    });
};

const checkDownloadStatements = () => {
    cy.get('button').contains('Новая выписка').should('be.visible').click();

    cy.findByText('Выписка по задолженности').should('be.visible');
    cy.get('.tranches-select__tranches')
        .should('be.visible')
        .and('have.attr', 'aria-disabled', 'true');

    cy.get('.print-operations-sidebar__calendar').should('be.visible');

    cy.get('.print-operations-sidebar__calendar').should('be.visible');
    cy.get('[class*="calendar-input__component"]').first().should('be.visible').click();
    cy.get('.cc-calendar').should('be.visible');
    cy.findByText('Выписка по задолженности').click();

    cy.findByText('Неделя').should('be.visible');
    cy.findByText('Месяц').should('be.visible');
    cy.findByText('Квартал').should('be.visible');
    cy.findByText('Год').should('be.visible').click();

    cy.get('.print-operations-sidebar__date-range-error')
        .should('be.visible')
        .contains('Выберите дату начала не раньше дня открытия сделки');

    cy.findByText('PDF').should('be.visible');
    cy.findByText('Excel').should('be.visible');
    cy.findByText('С электронной подписью банка').should('be.visible');

    cy.get('button').contains('Создать выписку').should('be.visible');
};

const checkCreditRequestRow = ({
    index,
    expectedDate,
    expectedProduct,
    expectedStatus,
    expectedDescription,
    expectedAmount,
    shouldHaveLink,
    expectedLink,
}: {
    index: number;
    expectedDate: string;
    expectedProduct: string[];
    expectedStatus: string;
    expectedDescription: string;
    expectedAmount: string;
    shouldHaveLink: boolean;
    expectedLink?: string;
}) => {
    cy.get('.trow')
        .eq(index)
        .within(() => {
            checkNormalizedTextByGet('.tcell', [expectedDate], 0);

            checkNormalizedTextByGet('.tcell', expectedProduct, 1);

            cy.get('.tcell')
                .eq(2)
                .find('span')
                .last()
                .should('have.text', expectedStatus)
                .invoke('attr', 'class')
                .should('include', 'status__ellipsis');

            checkNormalizedTextByGet('.tcell', [expectedDescription], 3);

            if (shouldHaveLink && expectedLink) {
                cy.get('.tcell')
                    .eq(3)
                    .find(`[class*=request-description__link]`)
                    .should('exist')
                    .and('have.text', expectedLink);
            }

            if (expectedAmount) {
                cy.get('.tcell')
                    .eq(4)
                    .find('span')
                    .should('include.text', expectedAmount)
                    .invoke('attr', 'class')
                    .should('include', 'amount__bold');
            } else {
                cy.get('.tcell').eq(4).find('span').should('not.exist');
            }
        });
};

const checkCreditRequestRowAdaptive = ({
    index,
    expectedStatus,
    expectedHeader,
    expectedDescription,
    expectedAmountAndDate,
    shouldHaveLink,
    expectedFooterText,
}: {
    index: number;
    expectedStatus: string;
    expectedHeader: string;
    expectedDescription: string;
    expectedAmountAndDate: string[];
    shouldHaveLink: boolean;
    expectedFooterText?: string;
}) => {
    cy.get('.credit-requests-mobile__row')
        .eq(index)
        .within(() => {
            cy.get('.credit-requests-mobile__status-content span')
                .last()
                .should('have.text', expectedStatus)
                .invoke('attr', 'class')
                .should('include', 'status__ellipsis');

            cy.get('.credit-requests-mobile__row-header')
                .find(`[class*=status-badge__component]`)
                .should(shouldHaveLink ? 'not.exist' : 'be.visible');

            checkNormalizedTextByGet(
                '.credit-requests-mobile__row-content > div',
                [expectedHeader],
                0,
            );
            checkNormalizedTextByGet(
                '.credit-requests-mobile__row-content > div',
                [expectedDescription],
                1,
            );
            checkNormalizedTextByGet(
                '.credit-requests-mobile__row-content > div',
                expectedAmountAndDate,
                2,
            );

            if (shouldHaveLink && expectedFooterText) {
                checkNormalizedTextByGet('.credit-requests-mobile__row-footer', [
                    expectedFooterText,
                ]);
            } else {
                cy.get('.credit-requests-mobile__row-footer').should('not.exist');
            }
        });
};

const checkStageSteps = (
    expectedStages: Array<[string, string | null, string | null, string | null]>,
) => {
    cy.findAllByTestId('stage-step-main').each(($stageContent, index) => {
        const [title, status, description, sla] = expectedStages[index];

        // Проверка названия этапа
        cy.wrap($stageContent)
            .children(`div[class*=progress-bar__step-title]`)
            .should('be.visible')
            .and('have.text', title);

        // Проверка статуса этапа
        if (status) {
            cy.wrap($stageContent)
                .children(`span[class*=progress-bar__step-status]`)
                .should('have.text', status)
                .invoke('attr', 'class')
                .should('include', 'status__rounded');
        }

        // Проверка описания этапа
        if (description) {
            cy.wrap($stageContent)
                .children(`div[class*=progress-bar__step-description]`)
                .should('be.visible')
                .and('have.text', description);
        }

        // Проверка описания SLA
        if (sla) {
            cy.wrap($stageContent)
                .children(`div[class*=progress-bar__step-date]`)
                .should('be.visible')
                .and('include.text', sla);
        }
    });
};

export {
    checkDocumentCalendarBlock,
    checkDocumentList,
    checkEmptyStateForAkWidget,
    checkManagerContacts,
    checkSuspensiveConditions,
    checkDownloadPaymentSchedule,
    checkCreditPaymentSchedule,
    checkGuarantyPaymentSchedule,
    checkCreditConditions,
    checkCreditDocumentation,
    checkDownloadStatements,
    checkCreditRequestRow,
    checkCreditRequestRowAdaptive,
    checkStageSteps,
};
