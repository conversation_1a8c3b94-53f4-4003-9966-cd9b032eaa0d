const formatDate = (date: Date, useTextMonth = false) => {
    const months = [
        'января',
        'февраля',
        'марта',
        'апреля',
        'мая',
        'июня',
        'июля',
        'августа',
        'сентября',
        'октября',
        'ноября',
        'декабря',
    ];

    const day = useTextMonth
        ? date.getDate().toString()
        : date.getDate().toString().padStart(2, '0');
    const month = useTextMonth
        ? months[date.getMonth()]
        : (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();

    return useTextMonth ? `${day} ${month} ${year}` : `${day}.${month}.${year}`;
};

const getDatePairWithTextMonth = (useTextMonth = true) => {
    const todayDate = new Date();
    const nextYearDate = new Date(todayDate);

    nextYearDate.setFullYear(todayDate.getFullYear() + 1);

    return {
        todayDate: formatDate(todayDate, useTextMonth),
        nextYearDate: formatDate(nextYearDate, useTextMonth),
    };
};

const getTodayDate = () => formatDate(new Date());

const getNextYearDate = () => {
    const nextYear = new Date();

    nextYear.setFullYear(nextYear.getFullYear() + 1);

    return formatDate(nextYear);
};

export { getDatePairWithTextMonth, getTodayDate, getNextYearDate };
