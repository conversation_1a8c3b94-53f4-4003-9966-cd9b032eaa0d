import {
    ECompanyNamesMock,
    type ECreditProductsDocNumbersMock,
} from '../../src/server/mocks/data/constants';
import { ECypressProfileIds } from '../constants/fixtures';

const goToCompany = ({
    profileId = ECypressProfileIds.MAIN_USER,
    companyName = ECompanyNamesMock.ALFA_LEASING,
}: {
    profileId?: ECypressProfileIds;
    companyName?: ECompanyNamesMock;
    isMobileAdaptive?: boolean;
} = {}) => {
    cy.setCookie('profileId', profileId);
    cy.visit(profileId);

    cy.waitForNetworkIdle('*', '*', 1000);

    cy.get('.corporate-company-select-new__company-name').should('be.visible').first().click();
    cy.get('div[class^="select__optionsList_"]').should('be.visible').contains(companyName).click();
    cy.waitForNetworkIdle('*', '*', 1000);
};

const goToCompanyMobileAdaptive = ({
    profileId = ECypressProfileIds.MAIN_USER,
    companyName = ECompanyNamesMock.ALFA_LEASING,
}: {
    profileId?: ECypressProfileIds;
    companyName?: ECompanyNamesMock;
} = {}) => {
    cy.viewport('iphone-xr');

    cy.setCookie('profileId', profileId);
    cy.visit(profileId);

    cy.waitForNetworkIdle('*', '*', 1000);

    cy.get('.corporate-company-select-new__company-name').should('be.visible').last().click();
    cy.get('div[class^="select__optionsList_"]').should('be.visible').contains(companyName).click();
    cy.waitForNetworkIdle('*', '*', 1000);
};

const goToCompanyMobile = ({
    profileId = ECypressProfileIds.MAIN_USER,
    companyName = ECompanyNamesMock.EMPTY,
}: {
    profileId?: ECypressProfileIds;
    companyName?: ECompanyNamesMock;
} = {}) => {
    cy.viewport('iphone-xr');

    cy.setCookie('profileId', profileId);
    cy.visit(profileId);

    cy.waitForNetworkIdle('*', '*', 1000);

    cy.get('.corporate-company-select-new__company-name').should('be.visible').last().click();
    cy.get('div[class^="select__optionsList_"]').should('be.visible').contains(companyName).click();
    cy.waitForNetworkIdle('*', '*', 1000);

    cy.visit(profileId, {
        headers: {
            'user-agent': 'iPhone',
        },
    });
    cy.waitForNetworkIdle('*', '*', 1000);
};

const goToProduct = (docNumber: ECreditProductsDocNumbersMock) => {
    cy.findAllByText(`№${docNumber}`, { timeout: 25000 }).first().click();
    cy.waitForNetworkIdle('*', '*', 1000);
};

const goToCreditOffers = () => {
    cy.visit('/credit-offers');
    cy.waitForNetworkIdle('*', '*', 1000);
};

export { goToCompany, goToCompanyMobileAdaptive, goToCompanyMobile, goToProduct, goToCreditOffers };
