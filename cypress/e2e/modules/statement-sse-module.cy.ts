import qs from 'qs';

import { ECreditProductsCodes } from '#/src/constants/credit-products';
import { PATHS } from '#/src/constants/routing';
import {
    NOTIFICATION_STORAGE_KEY,
    PRODUCT_PATH_MAP,
    SHARED_SSE_STATEMENT_CREATED,
} from '#/src/modules/statement-sse/constants';
import { type StoredNotification } from '#/src/modules/statement-sse/hooks/use-notification-storage';
import {
    ECreditProductsDocNumbersMock,
    ECustomersIdsMock,
} from '#/src/server/mocks/data/constants';

import { getEventBus } from '../../utils/modules-utils';
import { goToCompany, goToProduct } from '../../utils/navigation-helpers';

const TEST_PRODUCTS = {
    CREDIT_LINE: {
        code: ECreditProductsCodes.CREDIT_LINE,
        docNumber: ECreditProductsDocNumbersMock.CREDIT_LINE,
    },
    SOPUK: {
        code: ECreditProductsCodes.SOPUK,
        docNumber: ECreditProductsDocNumbersMock.SOPUK,
    },
    OVERDRAFT: {
        code: ECreditProductsCodes.OVERDRAFT,
        docNumber: ECreditProductsDocNumbersMock.OVERDRAFT,
    },
};

const dispatchStatementCreatedEvent = (
    product: string,
    docNumber?: string,
    customerId = ECustomersIdsMock.ALFA_LEASING,
) => {
    getEventBus().then((eventBus) => {
        eventBus.dispatchEvent(SHARED_SSE_STATEMENT_CREATED, {
            product,
            docNumber,
            customerId,
            statementRequestId: 'statementRequestId',
        });
    });
};

const verifyNotificationVisibility = (shouldBeVisible = true) => {
    const assertion = shouldBeVisible ? 'be.visible' : 'not.be.visible';

    cy.get('[data-testid=statement-sse-notification]', { timeout: 10000 }).should(assertion);
};

const verifyNotificationContent = () => {
    cy.get('[data-testid=statement-sse-notification]')
        .should('contain', 'Выписка создана')
        .and('contain', 'Перейти');
};

const verifyRedirectToStatements = (product: ECreditProductsCodes, docNumber?: string) => {
    const basePath = PRODUCT_PATH_MAP[product] ?? PATHS.MAIN_PAGE;
    const queryParams = qs.stringify(
        {
            docNumber,
            customerId: ECustomersIdsMock.ALFA_LEASING,
            tab: 'statementRequests',
        },
        { addQueryPrefix: true },
    );

    cy.get('[data-testid=statement-sse-notification]')
        .find('a')
        .should('have.attr', 'href', `${basePath}${queryParams}`);
};

const verifyStatementCount = (dataTestId: string, count: number) => {
    cy.findByTestId(dataTestId, { timeout: 10000 }).should('be.visible').and('contain.text', count);
};

const mockStatementRequestsResponse = (newStatementsCount: number) => ({
    statusCode: 200,
    body: {
        statementRequests: [],
        pagination: {
            pageNumber: 1,
            pageSize: 10,
            totalElements: 0,
            totalPages: 0,
        },
        newStatementsCount,
    },
});

describe('StatementSSEModule', () => {
    beforeEach(() => {
        goToCompany();
        localStorage.clear();
    });

    describe('Отображение уведомления', () => {
        it('Отображение уведомления при успешной генерации выписки', () => {
            dispatchStatementCreatedEvent(TEST_PRODUCTS.CREDIT_LINE.code);
            verifyNotificationVisibility();
            verifyNotificationContent();
        });

        it('Автоматическое закрытие уведомления после задержки', () => {
            dispatchStatementCreatedEvent(TEST_PRODUCTS.CREDIT_LINE.code);
            verifyNotificationVisibility();
            verifyNotificationVisibility(false);
        });

        it('Не отображать уведомление для невалидного продукта', () => {
            dispatchStatementCreatedEvent('INVALID_PRODUCT');
            verifyNotificationVisibility(false);
        });
    });

    describe('Взаимодействие с уведомлением', () => {
        it('Перенаправление на страницу выписок при клике на кнопку "Перейти"', () => {
            dispatchStatementCreatedEvent(
                TEST_PRODUCTS.CREDIT_LINE.code,
                TEST_PRODUCTS.CREDIT_LINE.docNumber,
            );
            verifyNotificationVisibility();
            cy.get('[data-testid=statement-sse-notification]').find('a').click();
            verifyRedirectToStatements(
                TEST_PRODUCTS.CREDIT_LINE.code,
                TEST_PRODUCTS.CREDIT_LINE.docNumber,
            );
        });

        it('Закрытие уведомления при клике на кнопку закрытия', () => {
            dispatchStatementCreatedEvent(TEST_PRODUCTS.CREDIT_LINE.code);
            verifyNotificationVisibility();
            cy.get('[data-testid=statement-sse-notification]').find('button').click();
            verifyNotificationVisibility(false);
        });
    });

    describe('Обработка множественных событий', () => {
        it('Обработка нескольких событий успешной генерации и перенаправление на последний продукт', () => {
            dispatchStatementCreatedEvent(
                TEST_PRODUCTS.CREDIT_LINE.code,
                TEST_PRODUCTS.CREDIT_LINE.docNumber,
            );
            dispatchStatementCreatedEvent(TEST_PRODUCTS.SOPUK.code, TEST_PRODUCTS.SOPUK.docNumber);
            verifyNotificationVisibility();
            cy.get('[data-testid=statement-sse-notification]').find('a').click();
            verifyRedirectToStatements(TEST_PRODUCTS.SOPUK.code, TEST_PRODUCTS.SOPUK.docNumber);
        });

        it('Обработка нескольких событий для одного и того же продукта', () => {
            dispatchStatementCreatedEvent(
                TEST_PRODUCTS.CREDIT_LINE.code,
                TEST_PRODUCTS.CREDIT_LINE.docNumber,
            );
            dispatchStatementCreatedEvent(
                TEST_PRODUCTS.CREDIT_LINE.code,
                TEST_PRODUCTS.CREDIT_LINE.docNumber,
            );
            verifyNotificationVisibility();
        });
    });

    describe('Крайние случаи', () => {
        it('Обработка события успешной генерации с неизвестным продуктом', () => {
            dispatchStatementCreatedEvent('UNKNOWN_PRODUCT', 'UNKNOWN_DOC_NUMBER');
            verifyNotificationVisibility();
            verifyNotificationContent();
            cy.get('[data-testid=statement-sse-notification]').find('a').click();
            verifyRedirectToStatements(
                'UNKNOWN_PRODUCT' as ECreditProductsCodes,
                'UNKNOWN_DOC_NUMBER',
            );
        });

        it('Обработка события успешной генерации без docNumber', () => {
            dispatchStatementCreatedEvent(TEST_PRODUCTS.CREDIT_LINE.code);
            verifyNotificationVisibility();
            verifyNotificationContent();
        });
    });

    describe('Обновление счетчика выписок', () => {
        beforeEach(() => {
            cy.intercept('GET', '**/statement-requests*', mockStatementRequestsResponse(1)).as(
                'getStatementRequests',
            );
            goToProduct(TEST_PRODUCTS.SOPUK.docNumber);
        });

        it('Обновление счетчика при получении нового события SSE', () => {
            dispatchStatementCreatedEvent(TEST_PRODUCTS.SOPUK.code, TEST_PRODUCTS.SOPUK.docNumber);

            cy.wait('@getStatementRequests');
            verifyStatementCount('sopuk-line-tab__statementRequests-toggle', 1);
        });

        it('Обновление счетчика для разных продуктов', () => {
            dispatchStatementCreatedEvent(TEST_PRODUCTS.SOPUK.code, TEST_PRODUCTS.SOPUK.docNumber);
            dispatchStatementCreatedEvent(
                TEST_PRODUCTS.OVERDRAFT.code,
                TEST_PRODUCTS.OVERDRAFT.docNumber,
            );

            cy.wait('@getStatementRequests');
            verifyStatementCount('sopuk-line-tab__statementRequests-toggle', 1);
        });
    });

    describe('Проверка вызовов API', () => {
        beforeEach(() => {
            goToProduct(TEST_PRODUCTS.SOPUK.docNumber);
        });

        it('Вызов getStatementRequests при совпадении docNumber и customerId', () => {
            cy.intercept('GET', '**/statement-requests*', mockStatementRequestsResponse(1)).as(
                'getStatementRequests',
            );

            dispatchStatementCreatedEvent(TEST_PRODUCTS.SOPUK.code, TEST_PRODUCTS.SOPUK.docNumber);

            cy.wait('@getStatementRequests');
            verifyNotificationVisibility();
        });

        it('Не вызывать getStatementRequests при несовпадении docNumber', () => {
            cy.intercept('GET', '**/statement-requests*', mockStatementRequestsResponse(1)).as(
                'getStatementRequests',
            );

            dispatchStatementCreatedEvent(
                TEST_PRODUCTS.SOPUK.code,
                TEST_PRODUCTS.CREDIT_LINE.docNumber,
            );

            cy.get('@getStatementRequests.all').should('have.length', 0);
            verifyNotificationVisibility(false);
        });

        it('Не вызывать getStatementRequests при несовпадении customerId', () => {
            cy.intercept('GET', '**/statement-requests*', mockStatementRequestsResponse(1)).as(
                'getStatementRequests',
            );

            dispatchStatementCreatedEvent(
                TEST_PRODUCTS.SOPUK.code,
                TEST_PRODUCTS.SOPUK.docNumber,
                ECustomersIdsMock.GOOGLE,
            );

            cy.get('@getStatementRequests.all').should('have.length', 0);
            verifyNotificationVisibility(false);
        });

        it('Не вызывать getStatementRequests при несовпадении docNumber и customerId', () => {
            cy.intercept('GET', '**/statement-requests*', mockStatementRequestsResponse(1)).as(
                'getStatementRequests',
            );

            dispatchStatementCreatedEvent(
                TEST_PRODUCTS.SOPUK.code,
                TEST_PRODUCTS.CREDIT_LINE.docNumber,
                ECustomersIdsMock.GOOGLE,
            );

            cy.get('@getStatementRequests.all').should('have.length', 0);
            verifyNotificationVisibility(false);
        });
    });

    describe('Проверка работы с localStorage', () => {
        it('Не показывать уведомление, если оно уже было показано', () => {
            const task: StoredNotification = {
                statementRequestId: 'statementRequestId',
            };

            localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(task));

            dispatchStatementCreatedEvent(
                TEST_PRODUCTS.CREDIT_LINE.code,
                TEST_PRODUCTS.CREDIT_LINE.docNumber,
            );

            verifyNotificationVisibility(false);
        });

        it('Корректно обрабатывать некорректные данные в localStorage', () => {
            localStorage.setItem(NOTIFICATION_STORAGE_KEY, 'invalid-json');

            dispatchStatementCreatedEvent(
                TEST_PRODUCTS.CREDIT_LINE.code,
                TEST_PRODUCTS.CREDIT_LINE.docNumber,
            );

            verifyNotificationVisibility();
        });
    });
});
