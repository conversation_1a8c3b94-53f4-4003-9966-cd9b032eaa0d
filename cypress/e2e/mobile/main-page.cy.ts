import { goToCompanyMobile } from '../../utils/navigation-helpers';

describe('Главная страница', () => {
    it('Проверка переключения вкладок', () => {
        goToCompanyMobile();

        cy.findByTestId('offers').should('be.visible');
        cy.get('.credit-offer-container').first().should('be.visible');

        cy.findByTestId('products').should('be.visible').click();
        cy.get('.credit-product-card').first().should('be.visible');

        cy.findByTestId('active-credit-case-notification').should('be.visible');
    });
});
