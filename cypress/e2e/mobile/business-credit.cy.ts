import { goToCompanyMobile } from '../../utils/navigation-helpers';

describe('Бизнес кредит', () => {
    it('Проверка отображения информации о кредитной линии', () => {
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findAllByText('Кредит для бизнеса').first().should('be.visible').click();
        cy.findByTestId('status').should('be.visible');

        cy.findByTestId('total-to-pay').should('be.visible');
        cy.findByTestId('total-to-pay-button').should('be.visible').click();
        cy.findByTestId('fine').should('be.visible');
        cy.findByTestId('tota-interest').should('be.visible');
        cy.findByTestId('total-debt').should('be.visible');
        cy.findAllByText('Понятно').should('be.visible').click();

        cy.findByTestId('total').should('be.visible');
        cy.findByTestId('interest').should('be.visible');
        cy.findByTestId('debt').should('be.visible');

        cy.findAllByText('Условия').should('be.visible').click();

        cy.findByTestId('debt-amount').should('be.visible');
        cy.findByTestId('rate').should('be.visible');
        cy.findByTestId('term').should('be.visible');
        cy.findByTestId('doc-number').should('be.visible');

        cy.findByTestId('payments-schedule-button').should('be.visible').click();
        cy.get('.payments-schedule__payment').first().should('be.visible');
    });
});
