import { goToCompanyMobile } from '../../utils/navigation-helpers';

describe('Овердрафт', () => {
    it('Проверка отображения информации об овердрафте без траншей', () => {
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findAllByText('Овердрафт').first().should('be.visible').click();

        cy.findByTestId('available-amount').should('be.visible');
        cy.findByTestId('debt-to-pay').should('be.visible');
        cy.findByTestId('to-date').should('be.visible');
        cy.findByTestId('interest').should('be.visible');
        cy.findByTestId('total').should('be.visible');
        cy.findByTestId('term').should('be.visible');

        cy.findByTestId('total-loan-button').should('be.visible').click();
        cy.findAllByText('Понятно').should('be.visible').click();

        cy.findByTestId('pay-till-button').should('be.visible').click();
        cy.findAllByText('Понятно').should('be.visible').click();

        cy.findByTestId('total-button').should('be.visible').click();
        cy.findAllByText('Понятно').should('be.visible').click();

        cy.findAllByText('Условия').should('be.visible').click();

        cy.findByTestId('limit').should('be.visible');
        cy.findByTestId('rate').should('be.visible');
        cy.findByTestId('pay-date').should('be.visible');
        cy.findByTestId('term').should('be.visible');
        cy.findByTestId('type').should('be.visible');

        cy.findByTestId('type-button').should('be.visible').click();
        cy.findAllByText('Понятно').should('be.visible').click();
    });

    it('Проверка отображения информации об овердрафте с траншами', () => {
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findAllByText('Овердрафт').eq(1).should('be.visible').click();

        cy.findByTestId('available-amount').should('be.visible');
        cy.findByTestId('debt-to-pay').should('be.visible');

        cy.findByTestId('debt-to-pay-button').should('be.visible').click();
        cy.findAllByText('Понятно').should('be.visible').click();

        cy.get('.tranche-card').first().should('be.visible').click();
        cy.findByTestId('tranche-debt').should('be.visible');
        cy.findByTestId('tranche-interest').should('be.visible');
        cy.findByTestId('tranche-total').should('be.visible');
        cy.findByTestId('tranche-total-interest').should('be.visible');
        cy.findByTestId('tranche-rate').should('be.visible');
        cy.findByTestId('tranche-sum').should('be.visible');
        cy.findByTestId('tranche-date').should('be.visible');

        cy.findByTestId('tranche-type-button').should('be.visible').click();
        cy.findAllByText('Понятно').should('be.visible').click();
    });
});
