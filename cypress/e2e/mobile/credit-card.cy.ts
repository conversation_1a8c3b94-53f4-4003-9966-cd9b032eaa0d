import {
    CREDIT_CARD_1,
    CREDIT_CARD_2,
    CREDIT_CARD_3,
    CREDIT_CARD_4,
    CREDIT_CARD_5,
    CREDIT_CARD_6,
    CREDIT_CARD_7,
    CREDIT_CARD_8,
} from '../../utils/constants';
import { goToCompanyMobile } from '../../utils/navigation-helpers';

describe('Кредитная карта', () => {
    it('Проверка отображения информации о кредитной карте', () => {
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findByText('Кредитная карта').first().should('be.visible').click();
        cy.findByTestId('available-amount').should('be.visible');
        cy.findByTestId('progress').should('be.visible');
        cy.findByTestId('payment-button').should('be.visible');
        cy.findByTestId('payment-min').should('be.visible');
        cy.findByTestId('grace-period').should('be.visible');
        cy.findByTestId('min-payment-calculated').should('be.visible');

        cy.findByTestId('conditions-button').should('be.visible').click();

        cy.findByTestId('rate').should('be.visible');
        cy.findByTestId('grace').should('be.visible');
        cy.findByTestId('term').should('be.visible');
        cy.findByTestId('cash').should('be.visible');
        cy.findByTestId('transfer').should('be.visible');

        cy.findByTestId('tarrifs-button').should('be.visible');
    });

    it('Проверка отображения debtStatus = null', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [CREDIT_CARD_1],
            },
        );
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findByText('Кредитная карта').first().should('be.visible').click();
        cy.findByTestId('available-amount').should('be.visible');
    });

    it('Проверка отображения agreementEnd = true', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [CREDIT_CARD_8],
            },
        );
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findByText('Кредитная карта').first().should('be.visible').click();
        cy.findByTestId('available-amount').should('be.visible');
    });

    it('Проверка отображения payment-notification Минимальный платеж', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [CREDIT_CARD_2],
            },
        );
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findByText('Кредитная карта').first().should('be.visible').click();
        cy.findByText('Ещё 12 дней').should('be.visible');

        cy.findByTestId('payment-min-to-pay').should('be.visible');
        cy.findByTestId('min-payment-calculated').should('be.visible').click();
        cy.findByTestId('min-payment-modal').contains('Минимальный платёж').should('be.visible');
        cy.get(`[aria-label="закрыть"]`).click();

        cy.findByText('Неустойка').should('be.visible').click();
        cy.findByTestId('fine-debt-modal').contains('Что входит в неустойку').should('be.visible');
        cy.get(`[aria-label="закрыть"]`).click();
    });

    it('Проверка отображения payment-notification Задолженность', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [CREDIT_CARD_3],
            },
        );
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findByText('Кредитная карта').first().should('be.visible').click();

        cy.findByTestId('payment-debt').contains('Задолженность').should('be.visible');

        cy.findByTestId('overdue').should('be.visible').click();
        cy.findByTestId('overdue-modal').contains('Из чего состоит просрочка').should('be.visible');
        cy.get(`[aria-label="закрыть"]`).click();
    });

    it('Проверка отображения payment-notification Общая задолженность', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [CREDIT_CARD_4],
            },
        );
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findByText('Кредитная карта').first().should('be.visible').click();
        cy.findByText('Закончился').should('be.visible');

        cy.findByTestId('payment-total').contains('Общая задолженность').should('be.visible');
    });

    it('Проверка отображения payment-notification Осталось выплатить', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [CREDIT_CARD_5],
            },
        );
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findByText('Кредитная карта').first().should('be.visible').click();
        cy.findByText('Ещё 12 дней').should('be.visible');

        cy.findByTestId('payment-left-to-pay').contains('Осталось выплатить').should('be.visible');
    });

    it('Проверка отображения условий Погасите общую задолженность', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [CREDIT_CARD_6],
            },
        );
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findByText('Кредитная карта').first().should('be.visible').click();
        cy.findByTestId('conditions-button').should('be.visible').click();
        cy.findByText('Погасите задолженность').should('be.visible').click();
        cy.findByTestId('overdue-debt-modal').should('be.visible');
        cy.get(`[aria-label="закрыть"]`).click();
    });

    it('Проверка отображения условий Погасите задолженность', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [CREDIT_CARD_7],
            },
        );
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findByText('Кредитная карта').first().should('be.visible').click();
        cy.findByTestId('conditions-button').should('be.visible').click();
        cy.findByTestId('debt-notification').should('be.visible').click();
        cy.findByTestId('debt-modal').should('be.visible');
        cy.get(`[aria-label="закрыть"]`).click();
    });
});
