import { goToCompanyMobile } from '../../utils/navigation-helpers';
import { CREDIT_LINE, TRANCHES } from '../data-mocks';

describe('Кредитная линия', () => {
    it('Проверка отображения информации о кредитной линии', () => {
        cy.intercept(
            {
                method: 'POST',
                pathname: '/api/v3/getCreditProducts',
            },
            {
                statusCode: 200,
                body: CREDIT_LINE,
            },
        );

        cy.intercept(
            {
                method: 'POST',
                pathname: '/api/CreditProductsApiV2Service/getTrancheCreditProductsV2',
            },
            {
                statusCode: 200,
                body: TRANCHES,
            },
        );

        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();

        cy.findByText('Кредитная линия').first().should('be.visible').click();
        cy.findByTestId('available-amount').should('be.visible');
        cy.findByTestId('left-to-pay').should('be.visible');
        cy.findByTestId('left-to-pay-button').should('be.visible').click();
        cy.findByTestId('total-to-pay').should('be.visible');
        cy.findByTestId('total').should('be.visible');
        cy.findByTestId('interest').should('be.visible');
        cy.findByTestId('close-button').should('be.visible').click();

        cy.findByTestId('conditions').should('be.visible').click();

        cy.findByTestId('rate').should('be.visible');
        cy.findByTestId('from-date').should('be.visible');
        cy.findByTestId('to-date').should('be.visible');
        cy.findByTestId('deadline').should('be.visible');
        cy.findByTestId('account-number').should('be.visible');
        cy.findByTestId('type').should('be.visible');
        cy.findByTestId('how-it-work-button').should('be.visible').click();
        cy.findByTestId('credit-line-info-page').should('be.visible');

        cy.findAllByText('Понятно').should('be.visible').click();

        cy.findByTestId('tranches').should('be.visible').click();
        cy.findAllByTestId('tranche-payment').first().should('be.visible');
        cy.findAllByTestId('tranche-date').first().should('be.visible');

        cy.get('.tranche-card').first().should('be.visible').click();
        cy.findByTestId('total-debt').should('be.visible');
        cy.findByTestId('tranche-number').should('be.visible');
        cy.findByTestId('payments').should('be.visible');
        cy.findByTestId('payments-schedule-button').should('be.visible');
        cy.findByTestId('early-repayment-button').should('be.visible');

        cy.findByTestId('total-debt-button').should('be.visible').click();
        cy.findByText('Общая задолженность').should('be.visible').click();
        cy.findByText('Основной долг').should('be.visible').click();
        cy.findAllByText('Проценты').eq(0).should('be.visible').click();
        cy.findAllByText('Проценты').eq(1).should('be.visible').click();
        cy.findByText('Сумма транша').should('be.visible').click();
        cy.findByText('Номер транша').should('be.visible').click();
        cy.findByText('Дата открытия транша').should('be.visible').click();
        cy.findByText('Срок окончания транша').should('be.visible').click();
        cy.findByText('Счет кредитной линии').should('be.visible').click();

        cy.findAllByText('Понятно').should('be.visible').click();

        cy.findByTestId('payments-schedule-button').should('be.visible').click();
        cy.get('.payments-schedule__payment').first().should('be.visible');
    });

    it('Проверка empty state', () => {
        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();
        cy.findByText('Кредитная линия').first().should('be.visible').click();
        cy.intercept(
            {
                method: 'POST',
                pathname: '/api/CreditProductsApiV2Service/getTrancheCreditProductsV2',
            },
            {
                statusCode: 404,
                body: [],
            },
        );

        cy.findByText('Не получилось загрузить').should('be.visible');
        cy.intercept(
            {
                method: 'POST',
                pathname: '/api/CreditProductsApiV2Service/getTrancheCreditProductsV2',
            },
            {
                statusCode: 200,
                body: [],
            },
        );
        cy.findByText('Попробовать ещё раз').should('be.visible').click();

        cy.findByTestId('empty-state').should('be.visible');
        cy.findByText('Новый транш').should('be.visible').click();
        cy.url().should('include', 'credit-tranche?docNumber=');
    });
});
