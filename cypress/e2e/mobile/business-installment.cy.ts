import { goToCompanyMobile } from '../../utils/navigation-helpers';
import { BUSINESS_INSTALLMENT } from '../data-mocks';

describe('Бизнес рассрочка', () => {
    it('Проверка отображения информации о бизнес-рассрочке', () => {
        cy.intercept(
            {
                method: 'POST',
                pathname: '/api/v3/getCreditProducts',
            },
            {
                statusCode: 200,
                body: BUSINESS_INSTALLMENT,
            },
        );

        goToCompanyMobile();

        cy.findByTestId('products').should('be.visible').click();
        cy.findAllByText('Кредит для бизнеса').first().should('be.visible').click();

        cy.findByText('Бизнес-рассрочка').should('be.visible');
        cy.findByTestId('status').should('be.visible');

        cy.findByTestId('total-to-pay').should('be.visible');

        cy.findByTestId('total-to-pay-button').should('be.visible').click();
        cy.findByText('Проценты').should('not.exist');
        cy.findByTestId('total-debt').should('be.visible');
        cy.findAllByText('Понятно').should('be.visible').click();

        cy.findByTestId('debt').should('be.visible');
        cy.findByText('Проценты').should('not.exist');
        cy.findByTestId('total').should('not.exist');

        cy.findByText('Условия').should('be.visible').click();
        cy.findByTestId('rate').should('be.visible');
        cy.findByTestId('debt-amount').should('be.visible');
        cy.findByTestId('term').should('be.visible');

        cy.findByText('Кредитный договор').should('be.visible');
        cy.findByText('Анкета-согласие').should('be.visible');
        cy.findByTestId('payments-schedule-button').should('be.visible').click();
        cy.get('.payments-schedule__payment').first().should('be.visible');
    });
});
