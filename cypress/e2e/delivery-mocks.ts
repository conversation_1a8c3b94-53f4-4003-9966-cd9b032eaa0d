import { type TActiveCreditCaseResponse } from 'thrift-services/services/credit_application_processing';

import { DeliveryDocumentStatus } from '#/src/ducks/credit-processing/types';

const TWO_DAYS_IN_SECONDS = 172800;
const MILLISECONDS_TO_SECONDS = 1000;
const CURRENT_DATE = Math.floor(Date.now() / MILLISECONDS_TO_SECONDS);

const BASE_ACTIVE_CREDIT_CASE_MOCK = {
    stage: 'fillingForm',
    loanMBId: 'MB-111',
    productCode: 'BP11',
    preferredProduct: 'UC04',
    isTotalOffer: false,
    isOnlineSigningAvailable: false,
    loanAmount: '1000000',
    productName: 'Кредит для бизнеса',
    scoringExpiryDate: {
        seconds: 1651266000,
    },
    platformId: 'SFA',
};

// Назначенную встречу можно перенести
export const ACTIVE_CREDIT_CASE_WITH_APPOINTMENT_MOCK: TActiveCreditCaseResponse = {
    ...BASE_ACTIVE_CREDIT_CASE_MOCK,
    deliveryInfo: {
        status: DeliveryDocumentStatus.WAITING_FOR_DOCUMENTS_SIGNING,
        dateOfSigning: { seconds: CURRENT_DATE + TWO_DAYS_IN_SECONDS },
        address: '603009, Нижегородская обл, г Нижний Новгород, пр-кт Гагарина, д 35Д',
        slotFrom: '09:00',
        slotTo: '10:00',
    },
};

// Назначенную встречу нельзя перенести
export const ACTIVE_CREDIT_CASE_WITH_APPOINTMENT_LOCKED_MOCK: TActiveCreditCaseResponse = {
    ...BASE_ACTIVE_CREDIT_CASE_MOCK,
    deliveryInfo: {
        status: DeliveryDocumentStatus.WAITING_FOR_DOCUMENTS_SIGNING,
        dateOfSigning: { seconds: CURRENT_DATE },
        address: '603009, Нижегородская обл, г Нижний Новгород, пр-кт Гагарина, д 35Д',
        slotFrom: '09:00',
        slotTo: '10:00',
    },
};

export const ACTIVE_CREDIT_CASE_WITH_CANCELLED_MOCK: TActiveCreditCaseResponse = {
    ...BASE_ACTIVE_CREDIT_CASE_MOCK,
    deliveryInfo: {
        status: DeliveryDocumentStatus.CANCELLED,
        dateOfSigning: { seconds: CURRENT_DATE },
        address: '603009, Нижегородская обл, г Нижний Новгород, пр-кт Гагарина, д. 35Д',
        slotFrom: '09:00',
        slotTo: '10:00',
    },
};

export const ACTIVE_CREDIT_CASE_WITH_COMPLETED_MOCK: TActiveCreditCaseResponse = {
    ...BASE_ACTIVE_CREDIT_CASE_MOCK,
    deliveryInfo: {
        status: DeliveryDocumentStatus.COMPLETED,
        dateOfSigning: { seconds: CURRENT_DATE },
        address: '603009, Нижегородская обл, г Нижний Новгород, пр-кт Гагарина, д. 35Д',
        slotFrom: '09:00',
        slotTo: '10:00',
    },
};
