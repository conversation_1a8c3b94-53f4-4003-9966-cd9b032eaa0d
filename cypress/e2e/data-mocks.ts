const CURRENT_DATE = new Date().valueOf() / 1000;

const createCreditProduct = ({
    docNumber,
    productType,
    productCode,
}: {
    docNumber: string;
    productType: string;
    productCode: string;
}) => ({
    docNumber,
    requisites: {
        sum: {
            amount: ********,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        product: productType,
        productCode,
        limit: {
            amount: 20000000000000,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        fromDate: {
            seconds: **********,
        },
        issueDate: {
            seconds: CURRENT_DATE - 100000,
        },
        insuranceDate: {
            seconds: **********,
        },
        toDate: {
            seconds: **********,
        },
        account: '40802810838090004842',
        trancheDeadline: 0,
        beneficiary: 'ООО Кампания',
        maxLimit: {
            amount: ********0000,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        clientLimit: {
            amount: ********,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        dealStatus: 'A',
        trancheGettingDeadline: {
            seconds: **********,
        },
        repaymentAccount: '40802810838090004842',
        dateDealClose: {
            seconds: **********,
        },
        trancheStatus: 'ACTIVE',
        businessBlock: 'Block',
        commissions: [
            {
                percentType: 'PercentType_CommSubscriptionMB',
                rate: 1,
            },
        ],
        creditAmountsByCurrency: [
            {
                amount: ********,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
        ],
        mainDebtShare: 10,
        repaymentType: 'repaymentType',
        daysBeforeAdvancedRepay: 5,
        suspensiveConditionsDeadlineInfo: {
            suspensiveConditionsDeadlineStatus: 'APPROACHING',
            notificationsCount: 2,
        },
        notifyAboutAdvancedRepay: true,
        trancheBegin: {
            seconds: **********,
        },
        trancheEnd: {
            seconds: 1538321600,
        },
        paymentStatus: 'ACTIVE',
        isAnnuityScheduleType: null,
    },
    gracePeriod: {
        gracePeriodDeadLine: 0,
        gracePeriodToDate: {
            seconds: -62135605817,
        },
        gracePeriodInterestAccrued: 0,
        gracePeriodInterestAccruedAmount: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        gracePeriodActualEndDate: {
            seconds: -62135605817,
        },
    },
    rate: {
        debtRate: 16.3,
        feeRate: 0,
        overdueDebtRate: 5,
        overdueFeeRate: 5,
        overdueInterestRate: 5,
        withdrawalRate: 10,
        overdueIncreasedRate: 5,
        debtRateDaily: null,
    },
    availableAmount: {
        amount: 150000000,
        currency: {
            code: 810,
            mnemonicCode: 'RUR',
            minorUnits: 100,
            unicodeSymbol: '₽',
            fullName: 'Российский рубль',
        },
    },
    debts: {
        loan: {
            debt: {
                amount: 0,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            debtToPay: {
                amount: 40000000,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            payDebtTillDate: {
                seconds: 1491944400,
            },
            overdueDebt: {
                amount: 0,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            fineDebt: {
                amount: 0,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            minDebtToPay: {
                amount: 40000000,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            minPayDebtTillDate: {
                seconds: 1491944400,
            },
            minPayDebtCalculationDate: {
                seconds: 1491944400,
            },
            daysOverdue: 60,
            overdueMinPayDate: {
                seconds: 1491944400,
            },
            blockLimitDate: null,
        },
        interest: {
            interest: {
                amount: 40000,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            interestToPay: {
                amount: 40000,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            payInterestTillDate: {
                seconds: 1491944400,
            },
            overdueInterest: {
                amount: 0,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            fineInterest: {
                amount: 0,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            minInterestToPay: {
                amount: 0,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            overdueInterestDate: {
                seconds: 1491944400,
            },
        },
        fee: {
            feeToPay: {
                amount: 0,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            payFeeTillDate: {
                seconds: 1491944400,
            },
            overdueFee: {
                amount: 0,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            fineFee: {
                amount: 0,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
        },
        guaranteeRequirements: {
            demandToPay: null,
            payDemandTillDate: null,
            overdueDemand: null,
            fineDemand: null,
        },
    },
    summary: {
        dailyFine: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        overdueDays: 0,
        totalFine: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalToPay: {
            amount: 40040000,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalDebt: {
            amount: 40040000,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalTrancheSum: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalLoanSum: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalLoanSumToPay: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalInterestOverdue: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalInterestSumToPay: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalOverdue: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalLoanAndFine: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalLoanToPayAndFine: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalInterestAndFine: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalInterestToPayAndFine: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalOverdueAndFine: {
            amount: 0,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        totalToPayWithoutOverdueAndFine: {
            amount: 40040000,
            currency: {
                code: 810,
                mnemonicCode: 'RUR',
                minorUnits: 100,
                unicodeSymbol: '₽',
                fullName: 'Российский рубль',
            },
        },
        sopukSummary: null,
    },
    actualDate: {
        seconds: **********,
    },
    frontParams: {
        hasPaymentSchedule: false,
        hasClientLimit: true,
        hasDealDocs: false,
        fault: false,
        errorMessage: null,
        hasCreditHolidaysFZ: false,
        hasPrepaymentAvailability: false,
        debtStatus: 'MIN_DEBT_PAYMENT_CALCULATED',
        isNotReadyScheduleOfPromoDeal: false,
    },
    productDocs: [
        {
            uuid: '123',
            requestId: '123',
            docType: 'testType',
        },
    ],
    servicingAccounts: [
        {
            number: '**********',
            code: '**********',
        },
    ],
    actions: [
        {
            action: 'getCredit',
        },
        {
            action: 'getDetails',
        },
        {
            action: 'getStatement',
        },
        {
            action: 'getTranche',
        },
        {
            action: 'getTrancheMMB',
        },
    ],
    dealId: '123',
});

export const CREDIT_LINE = [
    createCreditProduct({
        docNumber: '001H9V0C40',
        productType: '2',
        productCode: 'VKLRUVMS1',
    }),
];

export const TRANCHES = Array.from(Array(6).keys()).map((item) =>
    createCreditProduct({
        docNumber: `001H9V0C4${item}`,
        productType: '3',
        productCode: 'OVER',
    }),
);

export const BUSINESS_INSTALLMENT = [
    createCreditProduct({
        docNumber: '002M7Q6D5',
        productType: '1',
        productCode: 'UMCRRUBS01',
    }),
];
