import { ECreditProductsDocNumbersMock } from '../../../src/server/mocks/data/constants';
import { goToCompany, goToProduct } from '../../utils/navigation-helpers';

describe('Вкладка "Выписки"', () => {
    it('Проверка вкладки "Выписки"', () => {
        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.CREDIT_LINE);

        cy.findByTestId('credit-line-tab__statementRequests-toggle').click();
        cy.get('.product-page-grid__section').should('be.visible');

        // Проверка заголовков таблицы
        const headers = ['Дата', 'Транши', 'Период', 'Статус'];

        headers.forEach((text, index) => {
            cy.get('th')
                .eq(index + 1)
                .should('have.text', text);
        });

        // Ожидаемые строки таблицы
        const rows = [
            { tranches: '07XW2T005, 07XW2T006', status: 'Готово' },
            { tranches: '07XW2T006', status: 'Готово' },
            { tranches: '07XW2T001, 07XW2T002, 07XW2T003', status: 'Готово' },
            { tranches: '07XW2T001, 07XW2T002, 07XW2T003', status: 'В работе' },
            { tranches: '07XW2T001, 07XW2T002, 07XW2T003', status: 'Ошибка' },
        ];

        rows.forEach((row, index) => {
            cy.get('tr')
                .eq(index + 1)
                .children()
                .then((cells) => {
                    expect(cells.eq(1)).to.contain('28.03.2025');
                    expect(cells.eq(2)).to.contain(row.tranches);
                    expect(cells.eq(3)).to.contain('24.03.2025');
                    expect(cells.eq(3)).to.contain('27.03.2025');
                    expect(cells.eq(4)).to.contain(row.status);
                });
        });

        // Проверка нотификации при скачивании
        cy.get('button[data-test-id="statement-download"]').eq(1).should('be.visible').click();
        cy.findByText('Не получилось скачать выписку').should('be.visible');

        // Проверка тултипа
        cy.get('[data-test-id="statement-indicator"]').first().click();
        cy.findByText('07XW2T004, 07XW2T005, 07XW2T006').should('be.visible');

        // Проверка отображения статусов
        cy.get('.statement-requests__status-cell')
            .first()
            .should('be.visible')
            .and('have.text', 'Готово');

        ['В работе', 'Ошибка'].forEach((status) => {
            cy.findByText(status).should('be.visible');
        });
    });
});
