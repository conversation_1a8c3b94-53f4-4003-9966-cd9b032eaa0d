import { ECreditProductsDocNumbersMock } from '#/src/server/mocks/data/constants';

import { goToCompany, goToCompanyMobileAdaptive } from '../../utils/navigation-helpers';

describe('Фильтры на главной странице', () => {
    it('Проверка бокового меню со всеми фильтрами', () => {
        goToCompany();

        cy.findByTestId('company-filter').should('be.visible').click();
        cy.findAllByTestId('company-filter-option').eq(0).click();
        cy.findAllByTestId('company-filter-option').eq(2).click();
        cy.findAllByTestId('company-filter-option').eq(3).click();

        cy.findByTestId('company-filter-options-list-apply').click();

        cy.findByTestId('products-filter-select').should('be.visible').click();
        cy.findAllByTestId('products-filter-select-option').each((element, index) => {
            const expectedTexts = [
                'Кредитная линия',
                'СОПУК',
                'Гарантийная линия',
                'Гарантия',
                'Овердрафт',
                'Кредит',
            ];

            cy.wrap(element).should('be.visible').and('include.text', expectedTexts[index]);
        });

        cy.findAllByTestId('products-filter-select-option').eq(0).click();
        cy.findAllByTestId('products-filter-select-option').eq(1).click();
        cy.findAllByTestId('products-filter-select-option').eq(2).click();
        cy.findAllByTestId('products-filter-select-option').eq(4).click();
        cy.findAllByTestId('products-filter-select-option').eq(5).click();
        cy.findByTestId('products-filter-select-options-list-apply').click();

        cy.findByTestId('products-filter-doc-number').should('be.visible').click();
        cy.findByTestId('products-filter-select-search').type(
            ECreditProductsDocNumbersMock.OVERDRAFT_WITH_TRANCHE,
        );
        cy.findByTestId('products-filter-select-options-list-apply').should('be.visible').click();

        cy.findByTestId('products-filter-status-select').should('be.visible');

        cy.findByTestId('products-filter-sorting').should('be.visible').click();
        cy.findAllByTestId('products-filter-sorting-option').eq(1).click();

        cy.findByTestId('product-view-overdraft').click();
        cy.go('back');

        cy.findByTestId('products-filter-doc-number').should(
            'contain.text',
            ECreditProductsDocNumbersMock.OVERDRAFT_WITH_TRANCHE,
        );
        cy.findByTestId('products-filter-doc-number').should(
            'contain.text',
            ECreditProductsDocNumbersMock.OVERDRAFT_WITH_TRANCHE,
        );

        cy.findByTestId('products-filter-select').should('contain.text', 'выбрано 5');
        cy.findByTestId('company-filter').should('contain.text', 'выбраны все');

        cy.findByTestId('products-filter-doc-number').should('be.visible').click();
        cy.findByTestId('products-filter-select-options-list-clear').click();

        cy.findAllByTestId('product-view-businessCredit')
            .eq(0)
            .within(() => {
                cy.findByTestId('product-view-pane-title-businessCredit').should(
                    'contain.text',
                    '064Y4K',
                );
            });

        cy.findByTestId('products-filter-pagination').within(() => {
            cy.get('button').eq(2).click();
        });

        cy.findAllByTestId('product-view-creditLine')
            .eq(0)
            .within(() => {
                cy.findByTestId('product-view-pane-title-creditLine')
                    .should('contain.text', '27F78L')
                    .click();
            });

        cy.go('back');

        cy.findByTestId('products-filter-doc-number').should('contain.text', 'договора');
        cy.findByTestId('products-filter-sorting').should('contain.text', 'По компаниям');
        cy.findByTestId('products-filter-select').should('contain.text', 'выбрано 5');
        cy.findByTestId('company-filter').should('contain.text', 'выбраны все');

        cy.findByTestId('products-filter-pagination').within(() => {
            cy.get('button').eq(3).should('be.disabled');
            cy.get('button').eq(1).click();
        });

        cy.findAllByTestId('product-view-businessCredit')
            .eq(0)
            .within(() => {
                cy.findByTestId('product-view-pane-title-businessCredit').should(
                    'contain.text',
                    '064Y4K',
                );
            });
    });

    it('Проверка бокового меню со всеми фильтрами в адаптиве', () => {
        goToCompanyMobileAdaptive();
        cy.findByTestId('company-filter').should('be.visible').click();
        cy.findByTestId('company-filter-options-list-apply').should('be.visible').click();
        cy.findByTestId('general-filter-tag-tag').should('be.visible').click();

        cy.findByTestId('product-option-2').should('be.visible').click();

        cy.findByTestId('general-filter-tag-cancel').should('be.visible').click();
        cy.findByTestId('general-filter-tag-apply').should('be.visible').click();
    });
});
