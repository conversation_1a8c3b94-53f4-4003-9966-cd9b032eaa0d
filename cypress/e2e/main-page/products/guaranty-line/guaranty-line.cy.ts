import {
    ECompanyNamesMock,
    ECreditProductsDocNumbersMock,
    ECustomersIdsMock,
} from '../../../../../src/server/mocks/data/constants';
import { getNextYearDate, getTodayDate } from '../../../../utils/date-utils';
import { checkSuspensiveConditions } from '../../../../utils/interface-checkers';
import { goToCompany, goToProduct } from '../../../../utils/navigation-helpers';
import { checkNormalizedTextByGet } from '../../../../utils/text-utils';

describe('Гарантийная линия', () => {
    beforeEach(() => {
        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.GUARANTY_LINE);
    });

    it('Проверка верхней части', () => {
        cy.get('.common-heading__heading').within(() => {
            checkNormalizedTextByGet('.title-view__title', [
                `Гарантийная линия № ${ECreditProductsDocNumbersMock.GUARANTY_LINE}`,
            ]);
            checkNormalizedTextByGet('.title-view__subtitle', [ECompanyNamesMock.ALFA_LEASING]);
        });
    });

    it('Проверка основного блока', () => {
        cy.get('.guaranty-line-debt-pane').within(() => {
            checkNormalizedTextByGet('div', [
                'Вам доступно',
                '1 500 000,00 ₽',
                'из *********** 000,00 ₽',
            ]);

            cy.get('button').contains('Новая гарантия').should('be.visible').click();
            cy.url().should(
                'include',
                `/ak/request/guarantee?customerId=${ECustomersIdsMock.ALFA_LEASING}&dealDocNumber=0RGZ6R&dealId=123`,
            );
        });
    });

    it('Проверка вкладки "Гарантии"', () => {
        cy.get('button').contains('Все').should('be.visible');
        cy.get('button').contains('Действующие').should('be.visible');
        cy.get('button').contains('Закрытые').should('be.visible');

        cy.get('.guaranty-line-tranche-preview')
            .should('be.visible')
            .first()
            .within(() => {
                const classNameHeader = '.guaranty-line-tranche-preview__header-wrapper';
                const classNameAmount = '.guaranty-line-tranche-preview__amount-wrapper';

                checkNormalizedTextByGet(classNameHeader, ['№001H9V0C40']);
                checkNormalizedTextByGet(classNameAmount, ['Сумма', '100 000,00 ₽']);

                cy.get('.guaranty-line-tranche-preview__footer-wrapper').within(() => {
                    const date = `${getTodayDate()} - ${getNextYearDate()}`;
                    const classNameItem = '.guaranty-line-tranche-preview__info-item';

                    checkNormalizedTextByGet(classNameItem, ['Бенефициар', 'ООО Кампания'], 0);
                    checkNormalizedTextByGet(classNameItem, ['Действие договора', date], 1);

                    cy.get(classNameItem).should('have.length', 2);

                    cy.get('.guaranty-line-tranche-preview__button').contains('Подробнее');
                });
            });
    });

    it('Проверка вкладки "Условия договора"', () => {
        cy.findByText('Условия договора').should('be.visible').click();

        cy.get('.guaranty-line-conditions').within(() => {
            const className = '.guaranty-line-conditions__info-container-item';
            const date = `${getTodayDate()} - ${getNextYearDate()}`;

            checkNormalizedTextByGet(className, ['Договор', '№ 0RGZ6R'], 0);
            checkNormalizedTextByGet(className, ['Действие договора', date], 1);

            cy.get(className).should('have.length', 2);
        });
    });

    it('Проверка вкладки "Заявки"', () => {
        cy.findByText('Заявки').should('be.visible').click();

        cy.get('.filters-block').should('be.visible');
        cy.findByTestId('documents-list-table').should('be.visible');
    });

    it('Проверка вкладки "Отлагательные условия"', () => {
        checkSuspensiveConditions(
            '№0RGZ6R',
            `/ak/request/document?customerId=${ECustomersIdsMock.ALFA_LEASING}&dealId=123&dealDocNumber=0RGZ6R`,
        );
    });
});
