import {
    ECompanyNamesMock,
    ECreditProductsDocNumbersMock,
} from '../../../../../src/server/mocks/data/constants';
import { getDatePairWithTextMonth } from '../../../../utils/date-utils';
import { checkGuarantyPaymentSchedule } from '../../../../utils/interface-checkers';
import { goToCompany, goToProduct } from '../../../../utils/navigation-helpers';
import { checkNormalizedTextByGet } from '../../../../utils/text-utils';

describe('Транш гарантийной линии', () => {
    beforeEach(() => {
        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.GUARANTY_LINE);

        cy.findAllByText('Подробнее').first().click();
        cy.waitForNetworkIdle('*', '*', 1000);
    });

    it('Проверка верхней части', () => {
        cy.get('.common-heading__heading').within(() => {
            checkNormalizedTextByGet('.title-view__title', ['Гарантия № 001H9V0C40']);
            checkNormalizedTextByGet('.title-view__subtitle', [
                `По договору № ${ECreditProductsDocNumbersMock.GUARANTY_LINE},`,
                ECompanyNamesMock.ALFA_LEASING,
            ]);
        });
    });

    it('Проверка вкладки "Условия договора"', () => {
        cy.findByText('Условия договора').should('be.visible').click();
        const { todayDate } = getDatePairWithTextMonth(false);
        const { nextYearDate } = getDatePairWithTextMonth(false);

        cy.findByTestId('conditions-pane').within(() => {
            checkNormalizedTextByGet('.guaranty__condition-item', ['Сумма', '100 000,00 ₽'], 0);
            checkNormalizedTextByGet('.guaranty__condition-item', ['Дата выдачи', todayDate], 1);
            checkNormalizedTextByGet(
                '.guaranty__condition-item',
                ['Действие гарантии', `${todayDate} - ${nextYearDate}`],
                2,
            );
            checkNormalizedTextByGet(
                '.guaranty__condition-item',
                ['Бенефициар', 'ООО Кампания'],
                3,
            );
        });
    });

    it('Проверка вкладки "График платежей"', () => checkGuarantyPaymentSchedule());
});
