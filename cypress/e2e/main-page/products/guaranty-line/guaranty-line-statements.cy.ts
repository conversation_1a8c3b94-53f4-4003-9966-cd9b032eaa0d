import { ECreditProductsDocNumbersMock } from '../../../../../src/server/mocks/data/constants';
import { getNextYearDate, getTodayDate } from '../../../../utils/date-utils';
import {
    checkDocumentCalendarBlock,
    checkDocumentList,
} from '../../../../utils/interface-checkers';
import { goToCompany, goToProduct } from '../../../../utils/navigation-helpers';

describe('Вкладка "Заявки на гарантию" в гарантийной линии', () => {
    before(() => {
        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.GUARANTY_LINE);

        cy.findByText('Заявки').click();
    });

    it('Выбор периода', () => {
        const checkDocumentElement = () => {
            cy.get('.document-list__docnumber-cell')
                .contains('Заявление на банковскую гарантию')
                .should('be.visible');
        };

        cy.get('.date-range-picker').click();
        checkDocumentCalendarBlock('be.visible');

        cy.findByText('Сегодня').click();
        cy.findByText('Сегодня').should('be.visible');
        checkDocumentElement();

        cy.findByText('Сегодня').click();
        cy.findByText('Вчера').click();
        cy.findByText('Вчера').should('be.visible');
        checkDocumentElement();
        cy.findByText('Вчера').click();
        cy.findByText('Неделя').click();
        checkDocumentElement();

        cy.findByText('Месяц').click();
        cy.findByText('Применить').click();
        cy.get('.date-range-picker')
            .invoke('text')
            .then((text) => {
                const regexPattern = /[а-яА-Я]+/;

                expect(text).to.match(regexPattern);
            });

        cy.get('.date-range-picker').click(10, 10);
        cy.findByText('Квартал').click();
        cy.findByText('Применить').click();
        cy.get('.date-range-picker')
            .invoke('text')
            .then((text) => {
                const regexPattern = /\d{1} квартал \d{4}/;

                expect(text).to.match(regexPattern);
            });
        checkDocumentElement();

        cy.get('.date-range-picker').click();
        cy.findByText('Год').click();
        cy.findByText('Применить').click();
        cy.get('.date-range-picker')
            .invoke('text')
            .then((text) => {
                const regexPattern = /\d{4} год/;

                expect(text).to.match(regexPattern);
            });
        checkDocumentElement();
    });

    it('Ввод периода вручную', () => {
        cy.get('.date-range-picker').click();
        cy.findByText('Сбросить').click();
        cy.get('.date-range-picker').click();

        cy.findAllByPlaceholderText('ДД.ММ.ГГГГ').then((elements) => {
            const [firstInput, secondInput] = elements;

            cy.wrap(firstInput).type(getTodayDate());
            cy.wrap(secondInput).type(getNextYearDate());
        });

        cy.findByText('Применить').click();
        checkDocumentCalendarBlock('not.exist');

        checkDocumentList({ selector: 'be.visible', checkSum: true });
    });

    it('Поиск по заявке', () => {
        cy.findByPlaceholderText('Номер заявления').type('013I3RRF2');
        checkDocumentList({
            selector: 'be.visible',
            checkSum: true,
        });
    });

    it('Проверка фильтрации по статусам', () => {
        cy.findByText('Статус').click();
        cy.get('.filter-select__options-list-popover').should('be.visible');

        cy.findByText('На подпись').click();
        cy.get('button').contains('Сбросить').should('be.visible');

        cy.get('button').contains('Применить').click();
        cy.get('.filter-select__options-list-popover').should('not.exist');

        checkDocumentList({ selector: 'be.visible', checkSum: true });

        cy.findByText('На подпись').first().click();
        cy.get('.filter-select__options-list-popover').should('be.visible');

        cy.findByText('Сбросить').click();
        checkDocumentList({ selector: 'be.visible', checkSum: true });
    });

    it('Сброс фильтрации по статусам', () => {
        cy.findByText('Статус').click();
        cy.get('.filter-select__options-list-popover').should('be.visible');

        cy.findByText('На подпись').first().click();
        cy.findByText('Сбросить').should('be.visible');

        cy.findByText('Применить').click();
        cy.get('.filter-select__options-list-popover').should('not.exist');

        checkDocumentList({ selector: 'be.visible', checkSum: true });

        cy.get('div[role="button"]').last().should('be.visible').click();

        checkDocumentList({ selector: 'be.visible' });
    });
});
