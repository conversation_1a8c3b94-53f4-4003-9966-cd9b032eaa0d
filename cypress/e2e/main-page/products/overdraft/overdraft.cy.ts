import {
    ECompanyNamesMock,
    ECreditProductsDocNumbersMock,
    ECustomersIdsMock,
} from '../../../../../src/server/mocks/data/constants';
import { getDatePairWithTextMonth } from '../../../../utils/date-utils';
import {
    checkCreditDocumentation,
    checkSuspensiveConditions,
} from '../../../../utils/interface-checkers';
import { goToCompany, goToProduct } from '../../../../utils/navigation-helpers';
import { checkNormalizedTextByGet } from '../../../../utils/text-utils';

describe('Овердрафт', () => {
    const goToGoogleOverdraft = () => {
        goToCompany({ companyName: ECompanyNamesMock.GOOGLE });
        goToProduct(ECreditProductsDocNumbersMock.OVERDRAFT);
    };

    const goToEmptyOverdraftWithTranche = () => {
        goToCompany({ companyName: ECompanyNamesMock.EMPTY });
        goToProduct(ECreditProductsDocNumbersMock.OVERDRAFT_WITH_TRANCHE);
    };

    it('Переход на страницу без docNumber query параметра', () => {
        goToCompany({ companyName: ECompanyNamesMock.GOOGLE });
        cy.visit('/overdraft');
        cy.waitForNetworkIdle('*', '*', 1000);

        cy.findByText('Не получилось загрузить').should('be.visible');
        cy.findByText('Уже исправляем. Попробуйте ещё раз или зайдите позже').should('be.visible');
    });

    it('Проверка верхней части', () => {
        goToGoogleOverdraft();

        cy.get('.common-heading__heading').within(() => {
            checkNormalizedTextByGet('.title-view__title', [
                `Овердрафт № ${ECreditProductsDocNumbersMock.OVERDRAFT}`,
            ]);
            checkNormalizedTextByGet('.title-view__subtitle', [
                'К счёту ••4842,',
                ECompanyNamesMock.GOOGLE,
            ]);
        });

        cy.get('button').contains('Новая выписка').should('be.visible');
    });

    it('Проверка основного блока', () => {
        goToGoogleOverdraft();

        cy.get('.available-amount-panel').within(() => {
            checkNormalizedTextByGet('.available-amount-panel__label', ['Доступно']);
            checkNormalizedTextByGet('.corporate-amount', ['1 500 000,00 ₽']);
            checkNormalizedTextByGet('.available-amount-panel__available-limit', [
                'из *********** 000,00 ₽',
            ]);
        });

        cy.get('.pane-group')
            .first()
            .within(() => {
                checkNormalizedTextByGet('.pane-group__item', ['Задолженность', '400 400,00 ₽'], 0);
                checkNormalizedTextByGet(
                    '.pane-group__item',
                    ['Начисленные проценты', '400 400,00 ₽'],
                    1,
                );
                checkNormalizedTextByGet(
                    '.pane-group__item',
                    ['Неустойка за просрочку', '400 400,00 ₽'],
                    2,
                );
                checkNormalizedTextByGet(
                    '.pane-group__item',
                    ['Ближайшая сумма к погашению', '800 400,00 ₽'],
                    3,
                );

                cy.get('.pane-group__item').should('have.length', 4);

                cy.get('[class*=tooltip__target]').should('be.visible').click();
            });

        checkNormalizedTextByGet('.pane-item__tooltip-text', [
            '400 000,00 ₽ — задолженность',
            'ближайшая к погашению',
            '400 400,00 ₽ — проценты',
            'на всю задолженность',
        ]);
    });

    it('Проверка вкладки "Условия овердрафта"', () => {
        goToGoogleOverdraft();

        cy.findByText('Условия овердрафта').should('be.visible').click();

        cy.get('.condition-tab__pane-group-container').within(() => {
            checkNormalizedTextByGet('.pane-item', ['Лимит', '*********** 000 ₽'], 0);
            checkNormalizedTextByGet('.pane-item', ['Процентная ставка', '16.3 % годовых'], 1);
            checkNormalizedTextByGet('.pane-item', ['Дата закрытия', '30 мая 2012 г.'], 2);

            cy.get('.pane-item').should('have.length', 3);
        });
    });

    it('Проверка вкладки "Настройки"', () => {
        goToGoogleOverdraft();

        cy.findByText('Настройки').should('be.visible').click();
        cy.get('.overdraft-setting-pane').should('be.visible');

        checkNormalizedTextByGet(
            '.overdraft-setting-pane__content-text',
            ['Персональный лимит — это сумма, которая будет доступна вам из лимита овердрафта.'],
            0,
        );
        cy.get('.overdraft-setting-pane__header-button').eq(0).should('be.visible').click();

        cy.findByText('Персональный лимит').should('be.visible');
        cy.get('.overdraft-limit-modal__text')
            .should('be.visible')
            .contains('Персональный лимит — это персональное ограничение');
        cy.findByText('Сохранить').should('be.visible');
        cy.findByText('Отменить').should('be.visible').click();

        checkNormalizedTextByGet(
            '.overdraft-setting-pane__content-text',
            ['Овердрафт погашается автоматически, когда на расчётный счёт приходят деньги.'],
            1,
        );

        cy.get('.overdraft-setting-pane__header-button').eq(1).should('be.visible').click();

        checkNormalizedTextByGet('.overdraft-repayment-modal__repayment-item', ['Из оборота'], 0);
        checkNormalizedTextByGet('.overdraft-repayment-modal__repayment-item', ['В конце дня'], 1);

        checkNormalizedTextByGet('.overdraft-repayment-modal__alert', [
            'Заявка на изменение типа погашения в работе',
        ]);

        cy.get('div[class^="modal__footer"]').within(() => {
            cy.get('button').eq(0).should('have.text', 'Сохранить');
            cy.get('button').eq(1).should('have.text', 'Отменить').click();

            cy.get('button').should('have.length', 2);
        });
    });

    it('Проверка на ошибки при валидации во вкладке "Настройки"', () => {
        goToGoogleOverdraft();

        cy.findByText('Настройки').click();
        cy.get('.overdraft-setting-pane').should('be.visible');
        cy.get('.overdraft-setting-pane__header-button').eq(0).should('be.visible').click();

        cy.findByText('Сохранить').click();
        cy.get('[class*="typography__negative"]').should('be.visible');

        cy.findByText('Отменить').click();
    });

    it('Проверка овердрафта с траншами', () => {
        goToEmptyOverdraftWithTranche();

        cy.get('.common-heading__heading').contains('Овердрафт').should('be.visible');
        cy.get('.common-heading__heading').within(() => {
            checkNormalizedTextByGet('.title-view__title', [`Овердрафт № M38AIV`]);
            checkNormalizedTextByGet('.title-view__subtitle', [
                'К счёту ••4842,',
                ECompanyNamesMock.EMPTY,
            ]);
        });

        cy.get('button').contains('Новая выписка').should('be.visible');

        cy.get('.available-amount-panel').within(() => {
            checkNormalizedTextByGet('.available-amount-panel__label', ['Доступно']);
            checkNormalizedTextByGet('.corporate-amount', ['1 500 000,00 ₽']);
            checkNormalizedTextByGet('.available-amount-panel__available-limit', [
                'из *********** 000,00 ₽',
            ]);
        });

        cy.get('.pane-group')
            .first()
            .within(() => {
                checkNormalizedTextByGet('.pane-group__item', ['Задолженность', '400 400,00 ₽'], 0);
                checkNormalizedTextByGet(
                    '.pane-group__item',
                    ['Начисленные проценты', '400 400,00 ₽'],
                    1,
                );
                checkNormalizedTextByGet(
                    '.pane-group__item',
                    ['Неустойка за просрочку', '400 400,00 ₽'],
                    2,
                );
                checkNormalizedTextByGet('.pane-group__item', ['Ближайшая сумма к погашению'], 3);

                cy.get('.pane-group__item').should('have.length', 4);

                cy.get('[class*=tooltip__target]').should('be.visible').click();
            });

        checkNormalizedTextByGet('.pane-item__tooltip-text', [
            '400 400,00 ₽ — проценты',
            'на всю задолженность',
        ]);

        cy.get('.tranches__tranche')
            .first()
            .within(() => {
                cy.get('.tranche-preview__title').within(() => {
                    checkNormalizedTextByGet('.tranches_info', ['ВЕРНУТЬ ДО 12 АПРЕЛЯ']);
                    checkNormalizedTextByGet('.tranches__tranche-title', [
                        '400 400 ₽',
                        'из них проценты 0 ₽',
                    ]);
                });

                cy.findByText('Подробнее').click();
                cy.waitForNetworkIdle('*', '*', 1000);

                cy.get('.tranche-detail__inner-inner').within(() => {
                    const cn = '.pane-group__item';
                    const date = getDatePairWithTextMonth()
                        .todayDate.split(' ')
                        .slice(0, 2)
                        .join(' ');

                    checkNormalizedTextByGet(cn, ['Общая задолженность', '400 400,00 ₽'], 0);
                    checkNormalizedTextByGet(cn, ['Основной долг', '0,00 ₽'], 1);
                    checkNormalizedTextByGet(cn, ['Начислено процентов', '0,00 ₽'], 2);
                    checkNormalizedTextByGet(cn, ['Первоначальный транш', '100 000,00 ₽'], 3);
                    checkNormalizedTextByGet(cn, ['Дата открытия', date], 4);
                    checkNormalizedTextByGet(cn, ['Ставка', '16.3 % годовых'], 5);
                    checkNormalizedTextByGet(cn, ['Условия неустойки', '5 % в день'], 6);
                    checkNormalizedTextByGet(cn, ['Дата оплаты', '12 апреля'], 7);

                    cy.get(cn).should('have.length', 8);
                });

                cy.findByText(
                    'Погашение транша произойдёт автоматически при поступлении средств на счёт ••4842.',
                ).should('be.visible');

                cy.findByText('Скрыть').click();
            });
    });

    it('Проверка вкладки "Кредитная документация"', () => {
        goToGoogleOverdraft();
        checkCreditDocumentation();
    });

    it('Проверка вкладки "Отлагательные условия"', () => {
        goToGoogleOverdraft();
        checkSuspensiveConditions(
            '№037A4V',
            `/ak/request/document?customerId=${ECustomersIdsMock.GOOGLE}&dealId=123&dealDocNumber=037A4V`,
        );
    });
});
