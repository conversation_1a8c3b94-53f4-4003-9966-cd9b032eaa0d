import { ECreditProductsDocNumbersMock } from '../../../../src/server/mocks/data/constants';
import { checkDocumentCalendarBlock, checkDocumentList } from '../../../utils/interface-checkers';
import { goToCompany, goToProduct } from '../../../utils/navigation-helpers';

const products = [
    { name: 'Кредит', docNumber: ECreditProductsDocNumbersMock.BUSINESS_CREDIT },
    { name: 'Кредитная линия', docNumber: ECreditProductsDocNumbersMock.CREDIT_LINE },
    { name: 'Гарантийная линия', docNumber: ECreditProductsDocNumbersMock.GUARANTY_LINE },
    { name: 'СОПУК', docNumber: ECreditProductsDocNumbersMock.SOPUK },
];

describe('Вкладка "Отправка документов"', () => {
    products.forEach((product) => {
        describe(product.name, () => {
            before(() => {
                goToCompany();
                goToProduct(product.docNumber);

                cy.findByText('Отправка документов').click();
            });

            it('Отображение вкладки', () => {
                cy.get('button').contains('Отправить документы в банк').should('be.visible');
                cy.findByText('Не выбрано').should('be.visible');
                cy.findByPlaceholderText('Номер заявления').should('be.visible');
                cy.findByText('Статус').should('be.visible');
                cy.findByTestId('documents-list-table').should('be.visible');
            });

            it('Выбор периода', () => {
                cy.get('.date-range-picker').click();
                checkDocumentCalendarBlock('be.visible');

                cy.findByText('Сегодня').click();
                cy.findByText('Сегодня').should('be.visible');

                cy.findByText('Сегодня').click();
                cy.findByText('Вчера').click();
                cy.findByText('Вчера').should('be.visible');

                cy.findByText('Вчера').click();
                cy.findByText('Неделя').click();

                cy.findByText('Месяц').click();
                cy.findByText('Применить').click();
                cy.get('.date-range-picker')
                    .invoke('text')
                    .then((text) => {
                        const regexPattern = /[а-яА-Я]+/;

                        expect(text).to.match(regexPattern);
                    });

                cy.get('.date-range-picker').click(10, 10);
                cy.findByText('Квартал').click();
                cy.findByText('Применить').click();
                cy.get('.date-range-picker')
                    .invoke('text')
                    .then((text) => {
                        const regexPattern = /\d{1} квартал \d{4}/;

                        expect(text).to.match(regexPattern);
                    });

                cy.get('.date-range-picker').click();
                cy.findByText('Год').click();
                cy.findByText('Применить').click();
                cy.get('.date-range-picker')
                    .invoke('text')
                    .then((text) => {
                        const regexPattern = /\d{4} год/;

                        expect(text).to.match(regexPattern);
                    });
            });

            it('Ввод периода вручную', () => {
                cy.get('.date-range-picker').click();
                cy.findByText('Сбросить').click(10, 10);
                cy.get('.date-range-picker').click();

                checkDocumentCalendarBlock('be.visible');

                cy.findAllByPlaceholderText('ДД.ММ.ГГГГ').then((elements) => {
                    const [firstInput, secondInput] = elements;

                    cy.wrap(firstInput).type('24.08.2022');
                    cy.wrap(secondInput).type('25.08.2022');
                });

                cy.findByText('Применить').click();

                checkDocumentCalendarBlock('not.exist');

                cy.get('.date-range-picker')
                    .invoke('text')
                    .then((text) => {
                        const regexPattern = /\d{2}\.\d{2}\.\d{4} - \d{2}\.\d{2}\.\d{4}/;

                        expect(text).to.match(regexPattern);
                    });

                checkDocumentList({ selector: 'be.visible' });
            });

            it('Проверка фильтрации по статусам', () => {
                cy.findByText('Статус').click();
                cy.get('.filter-select__options-list-popover').should('be.visible');

                cy.findByText('На подпись').click();
                cy.findByText('Сбросить').should('be.visible');

                cy.findByText('Применить').click();
                cy.get('.filter-select__options-list-popover').should('not.exist');

                checkDocumentList({ selector: 'be.visible' });

                cy.findByText('На подпись').click();
                cy.get('.filter-select__options-list-popover').should('be.visible');
                cy.findByText('Сбросить').click();
                checkDocumentList({ selector: 'be.visible' });
            });

            it('Сброс фильтрации по статусам', () => {
                cy.findByText('Статус').click();
                cy.get('.filter-select__options-list-popover').should('be.visible');

                cy.findByText('На подпись').click();
                cy.findByText('Сбросить').should('be.visible');

                cy.findByText('Применить').click();
                cy.get('.filter-select__options-list-popover').should('not.exist');

                checkDocumentList({ selector: 'be.visible' });

                cy.get('div[role="button"]').last().should('be.visible').click();

                checkDocumentList({ selector: 'be.visible' });
            });
        });
    });
});
