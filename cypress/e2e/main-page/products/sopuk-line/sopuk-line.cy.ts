import {
    ECompanyNamesMock,
    ECreditProductsDocNumbersMock,
    ECustomersIdsMock,
} from '../../../../../src/server/mocks/data/constants';
import {
    SOPUK_MOCK,
    SOPUK_MOCK_WITH_OVERDUE_SUMMARY_ONLY_YUAN,
    SOPUK_MOCK_WITH_OVERDUE_SUMMARY_RUB_YUAN,
    SOPUK_MOCK_WITHOUT_SUMMARY,
} from '../../../../../src/server/mocks/data/products';
import { getNextYearDate, getTodayDate } from '../../../../utils/date-utils';
import { checkSuspensiveConditions } from '../../../../utils/interface-checkers';
import {
    goToCompany,
    goToCompanyMobileAdaptive,
    goToProduct,
} from '../../../../utils/navigation-helpers';
import {
    checkNormalizedTextByGet,
    checkNormalizedTextByTestId,
} from '../../../../utils/text-utils';

describe('СОПУК', () => {
    beforeEach(() => {
        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.SOPUK);
    });

    it('Проверка верхней части', () => {
        cy.get('.common-heading__heading').within(() => {
            checkNormalizedTextByGet('.title-view__title', [
                `Рамочный договор № ${ECreditProductsDocNumbersMock.SOPUK}`,
            ]);

            checkNormalizedTextByGet('.title-view__subtitle', [ECompanyNamesMock.ALFA_LEASING]);
        });
    });

    it('Проверка основного блока', () => {
        cy.get('.sopuk-line-debt-pane').within(() => {
            cy.get('.sopuk-line-amount-pane').within(() => {
                checkNormalizedTextByTestId('amount', ['Использовано', 'в рублях', '400 400,00 ₽']);
                checkNormalizedTextByGet('button', ['Новый кредит', 'Новая выписка']);
            });

            cy.get('.sopuk-line-amount-pane__picker-button').click();
        });

        cy.findByText('в евро').should('be.visible').click();

        cy.get('.sopuk-line-debt-pane').within(() => {
            const date = `${getTodayDate()} - ${getNextYearDate()}`;

            checkNormalizedTextByTestId('debt', ['Осталось выплатить', '400 400,00 €']);
            checkNormalizedTextByTestId('loan', ['Основной долг', '400 400,00 €']);
            checkNormalizedTextByTestId('interest', ['Проценты', '400 400,00 €']);
            checkNormalizedTextByTestId('fine', ['Неустойка', '400 400,00 €']);
            checkNormalizedTextByTestId('term', ['Действие договора', date]);
        });
    });

    it('Проверка кнопки "Новый кредит"', () => {
        cy.get('button').contains('Новый кредит').should('be.visible').click();
        cy.url().should(
            'include',
            `/ak/request/credit?customerId=${ECustomersIdsMock.ALFA_LEASING}&dealDocNumber=1543&dealId=123`,
        );
    });

    it('Проверка вкладки "Кредиты"', () => {
        cy.get('button').contains('Все').should('be.visible');
        cy.get('button').contains('Действующие').should('be.visible');
        cy.get('button').contains('Закрытые').should('be.visible');

        cy.findByTestId('tranche-pane-SOPH9V0C40').within(() => {
            checkNormalizedTextByTestId('tranche-doc-number', ['№SOPH9V0C40']);
            checkNormalizedTextByTestId('tranche-summary-totalDebt', [
                'Осталось выплатить',
                '400 400,00 ₽',
            ]);

            const date = `${getTodayDate()} - ${getNextYearDate()}`;

            checkNormalizedTextByTestId('payment', ['Платеж 12.04.2017', '400 400,00 ₽']);
            checkNormalizedTextByTestId('tranche-debt-rate', ['Ставка', '16.3% годовых']);
            checkNormalizedTextByTestId('tranche-requisites-term', ['Действие договора', date]);

            cy.findByTestId('suspensive-conditions-status')
                .findByText('Посмотреть условия')
                .click();
        });
    });

    it('Проверка вкладки "Заявления на кредиты"', () => {
        cy.findByText('Заявления на кредиты').should('be.visible').click();

        cy.get('.filters-block').should('be.visible');
        cy.findByTestId('documents-list-table').should('be.visible');
    });

    it('Проверка вкладки "Отлагательные условия"', () => {
        checkSuspensiveConditions(
            'Рамочный договор (СОПУК) №1543',
            `/ak/request/document?customerId=${ECustomersIdsMock.ALFA_LEASING}&dealId=123&dealDocNumber=1543`,
        );
    });
});

describe('СОПУК, таблица просрочки', () => {
    it('Проверка таблицы просрочки', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [SOPUK_MOCK],
            },
        );

        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.SOPUK);

        checkNormalizedTextByGet('.overdue', [
            'Просрочено',
            '400 400 ₽',
            '400 400 $',
            '400 400 €',
            '400 400 Ұ',
        ]);
        cy.get('.overdue').click();
        checkNormalizedTextByGet('h2', ['Из чего состоит просрочка']);
        cy.get('table').should('be.visible');
        cy.get('thead')
            .get('th')
            .each((element, index) => {
                const expectedTexts = ['Валюта', 'Основной долг', 'Проценты', 'Неустойка', 'Всего'];

                cy.wrap(element).should('be.visible').and('include.text', expectedTexts[index]);
            });
        cy.get('button').contains('Понятно').should('be.visible').click();
        cy.findByText('Из чего состоит просрочка').should('not.exist');
    });

    it('Проверка модалки просрочки в мобильном адаптиве', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [SOPUK_MOCK],
            },
        );

        goToCompanyMobileAdaptive();
        goToProduct(ECreditProductsDocNumbersMock.SOPUK);

        cy.get('.overdue').should('be.visible').and('include.text', 'Просрочено').click();

        cy.get('.sopuk-line-overdue-mobile-modal__title')
            .contains('Из чего состоит просрочка')
            .should('be.visible');
        cy.get('button[role="tab"]').should('be.visible');

        cy.get('.sopuk-line-overdue-mobile-modal__content').should('be.visible');
        cy.get('button').contains('Понятно').should('be.visible').click();
        cy.findByText('Из чего состоит просрочка').should('not.exist');
    });

    it('Проверка таблицы просрочки (из валют - рубль и юань)', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [SOPUK_MOCK_WITH_OVERDUE_SUMMARY_RUB_YUAN],
            },
        );

        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.SOPUK);

        checkNormalizedTextByGet('.overdue', ['Просрочено', '400 400 ₽', '400 400 Ұ']);
        cy.get('.overdue').click();
        checkNormalizedTextByGet('h2', ['Из чего состоит просрочка']);
        cy.get('table').should('be.visible');
        cy.get('thead')
            .get('th')
            .each((element, index) => {
                const expectedTexts = ['Валюта', 'Основной долг', 'Проценты', 'Неустойка', 'Всего'];

                cy.wrap(element).should('be.visible').and('include.text', expectedTexts[index]);
            });
        cy.get('button').contains('Понятно').should('be.visible').click();
        cy.findByText('Из чего состоит просрочка').should('not.exist');
    });

    it('Проверка таблицы просрочки (из валют - юань)', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [SOPUK_MOCK_WITH_OVERDUE_SUMMARY_ONLY_YUAN],
            },
        );

        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.SOPUK);

        checkNormalizedTextByGet('.overdue', ['Просрочено', '400 400 Ұ']);
        cy.get('.overdue').click();
        checkNormalizedTextByGet('h2', ['Из чего состоит просрочка']);
        cy.get('table').should('be.visible');
        cy.get('thead')
            .get('th')
            .each((element, index) => {
                const expectedTexts = ['Валюта', 'Основной долг', 'Проценты', 'Неустойка', 'Всего'];

                cy.wrap(element).should('be.visible').and('include.text', expectedTexts[index]);
            });
        cy.get('button').contains('Понятно').should('be.visible').click();
        cy.findByText('Из чего состоит просрочка').should('not.exist');
    });

    it('Таблицы и блока не должно быть', () => {
        cy.intercept(
            {
                pathname: '/api/v3/getCreditProducts',
                method: 'POST',
            },
            {
                statusCode: 200,
                body: [SOPUK_MOCK_WITHOUT_SUMMARY],
            },
        );

        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.SOPUK);

        cy.get('.overdue').should('not.exist');
    });
});
