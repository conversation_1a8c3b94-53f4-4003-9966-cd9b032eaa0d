import { EARLY_REPAYMENT_PROCESSING_TYPE_MOCK } from '#/src/server/mocks/data/data';

import { ECreditProductsDocNumbersMock } from '../../../../../src/server/mocks/data/constants';
import { getTodayDate } from '../../../../utils/date-utils';
import { goToCompany, goToProduct } from '../../../../utils/navigation-helpers';
import { checkNormalizedTextByGet } from '../../../../utils/text-utils';
import { CREDIT_LINE, TRANCHES } from '../../../data-mocks';

describe('Досрочное погашение у транша КЛ', () => {
    beforeEach(() => {
        goToCompany();
        cy.intercept(
            {
                method: 'POST',
                pathname: '/api/v3/getCreditProducts',
            },
            {
                statusCode: 200,
                body: CREDIT_LINE,
            },
        );
        cy.intercept(
            {
                method: 'POST',
                pathname: '/api/CreditProductsApiV2Service/getTrancheCreditProductsV2',
            },
            {
                statusCode: 200,
                body: TRANCHES,
            },
        );
        cy.intercept(
            {
                method: 'GET',
                pathname: '**/api/early-repayment/type**',
            },
            {
                statusCode: 200,
                body: EARLY_REPAYMENT_PROCESSING_TYPE_MOCK,
            },
        );
        goToProduct(ECreditProductsDocNumbersMock.CREDIT_LINE);

        cy.findByTestId('tranche-pane-001H9V0C40').within(() => {
            cy.get('button').contains('Подробнее').should('be.visible').click();
        });
    });

    it('Проверка сайдбара частичного погашения', () => {
        cy.get('button').contains('Погасить досрочно').should('be.visible').click();
        cy.waitForNetworkIdle('*', '*', 1000);

        cy.get('h3').contains('Досрочное погашение транша').should('be.visible');
        cy.findByText('№001H9V0C40').should('be.visible');

        cy.findByText('Тип досрочного погашения').should('be.visible');
        cy.get('button').contains('Частичное').should('be.visible').click();

        cy.findByText('Счет списания').should('be.visible');

        cy.findByPlaceholderText('Дата погашения').should('be.visible');
        cy.findByText(
            'Погашение текущей датой возможно в рабочие дни до 19:00 по московскому времени',
        ).should('be.visible');

        cy.get('input[aria-label="Сумма процентов"]').should('not.exist');
        cy.get('input[aria-label="Сумма основного долга"]').should('be.visible');

        cy.contains('Приоритет — у плановых платежей по кредитам и овердрафта').should(
            'be.visible',
        );
        cy.contains(
            'Штраф за досрочное погашение будет рассчитан согласно условиям договора.',
        ).should('be.visible');

        cy.get('button').contains('Подписать и отправить').should('be.visible').click();

        cy.findByText('Необходимо заполнить поле');
        cy.findByText('Максимальная сумма досрочного погашения должна быть менее 0 ₽');

        cy.findByPlaceholderText('Дата погашения').click();
        cy.get('button[class*="calendar__today"]').should('be.visible').click();
        cy.get('input[aria-label="Сумма основного долга"]').type('10000');
        checkNormalizedTextByGet('.early-pay-sidebar__amount', ['10 000 ₽']);
    });

    it('Проверка сайдбара полного погашения"', () => {
        cy.get('button').contains('Погасить досрочно').should('be.visible').click();
        cy.waitForNetworkIdle('*', '*', 1000);

        cy.get('h3').contains('Досрочное погашение транша').should('be.visible');
        cy.findByText('№001H9V0C40').should('be.visible');

        cy.findByText('Тип досрочного погашения').should('be.visible');
        cy.get('button').contains('Полное').should('be.visible').click();

        cy.findByText('Счет списания').should('be.visible');

        cy.get(`input[value="${getTodayDate()}"]`).should('be.visible');
        cy.findByText(
            'Погашение текущей датой возможно в рабочие дни до 19:00 по московскому времени',
        ).should('be.visible');

        cy.get('input[aria-label="Сумма процентов"]').should('be.visible');
        cy.get('input[aria-label="Сумма основного долга"]').should('be.visible');

        cy.contains('Приоритет — у плановых платежей по кредитам и овердрафта').should(
            'be.visible',
        );
        cy.contains(
            'Штраф за досрочное погашение будет рассчитан согласно условиям договора.',
        ).should('be.visible');
        cy.findByText('Подписать и отправить').should('be.visible').click();
    });
});
