import {
    ECompanyNamesMock,
    ECreditProductsDocNumbersMock,
    ECustomersIdsMock,
} from '../../../../../src/server/mocks/data/constants';
import { getNextYearDate, getTodayDate } from '../../../../utils/date-utils';
import {
    checkCreditDocumentation,
    checkSuspensiveConditions,
} from '../../../../utils/interface-checkers';
import { goToCompany, goToProduct } from '../../../../utils/navigation-helpers';
import {
    checkNormalizedTextByGet,
    checkNormalizedTextByTestId,
} from '../../../../utils/text-utils';

describe('Кредитная линия', () => {
    beforeEach(() => {
        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.CREDIT_LINE);
    });

    it('Проверка верхней части', () => {
        cy.get('.common-heading__heading').within(() => {
            checkNormalizedTextByGet('.title-view__title', [
                `Кредитная линия № ${ECreditProductsDocNumbersMock.CREDIT_LINE}`,
            ]);
            checkNormalizedTextByGet('.title-view__subtitle', [ECompanyNamesMock.ALFA_LEASING]);
        });
    });

    it('Проверка основного блока', () => {
        cy.findByTestId('debt-pane').within(() => {
            checkNormalizedTextByTestId('available-amount-header', ['Вам доступно']);

            cy.get('.credit-line-debt-pane__available-amount-container').within(() => {
                checkNormalizedTextByTestId('available-amount', [
                    '1 500 000,00 ₽ из *********** 000,00 ₽',
                ]);

                cy.get('.credit-line-debt-pane__buttons').within(() => {
                    cy.get('button').contains('Новый транш').should('be.visible');
                    cy.get('button').contains('Новая выписка').should('be.visible');
                });
            });

            const date = `${getTodayDate()} – ${getNextYearDate()}`;

            checkNormalizedTextByTestId('total-debt', ['Осталось выплатить', '400 400,00 ₽']);
            checkNormalizedTextByTestId('total-loan-sum', ['Основной долг', '0,00 ₽']);
            checkNormalizedTextByTestId('total-interest', ['Проценты', '0,00 ₽']);
            checkNormalizedTextByTestId('tranche_term', ['Действие договора', date]);
            checkNormalizedTextByTestId('tranche_date', [
                'Выдача транша доступна',
                'До 30.09.2018',
            ]);
        });
    });

    it('Проверка кнопки "Новый транш"', () => {
        cy.get('button').contains('Новый транш').should('be.visible').click();
        cy.url().should('include', '/credit-tranche?docNumber=07F78L');
    });

    it('Проверка вкладки "Транши"', () => {
        cy.get('button').contains('Все').should('be.visible');
        cy.get('button').contains('Действующие').should('be.visible');
        cy.get('button').contains('Закрытые').should('be.visible');

        cy.get('.credit-line-tranche-view')
            .should('be.visible')
            .first()
            .within(() => {
                const date = `${getTodayDate()} - ${getNextYearDate()}`;

                checkNormalizedTextByTestId('tranche-doc-number', ['№001H9V0C40']);
                checkNormalizedTextByTestId('tranche-summary-totalDebt', [
                    'Осталось выплатить',
                    '400 400,00 ₽',
                ]);

                checkNormalizedTextByTestId('payment', ['Платеж 12.04.2017', '400 400,00 ₽']);
                checkNormalizedTextByTestId('tranche-debt-rate', ['Ставка', '16.3% годовых']);
                checkNormalizedTextByTestId('tranche-requisites-term', ['Действие договора', date]);

                cy.findByTestId('tranche-about-button').contains('Подробнее');

                cy.findByTestId('suspensive-conditions-status')
                    .findByText('Посмотреть условия')
                    .click();
            });
    });

    it('Проверка вкладки "Кредитная документация"', () => checkCreditDocumentation());

    it('Проверка вкладки "Заявления на транши"', () => {
        cy.findByText('Заявления на транши').should('be.visible').click();

        cy.get('.filters-block').should('be.visible');
        cy.findByTestId('documents-list-table').should('be.visible');
    });

    it('Проверка вкладки "Договоры"', () => {
        cy.findByText('Договоры').should('be.visible').click();

        cy.get('.filters-block').should('be.visible');
        cy.findByTestId('documents-list-table').should('be.visible');
    });

    it('Проверка вкладки "Отлагательные условия"', () => {
        checkSuspensiveConditions(
            'Кредитная линия №07F78L',
            `/ak/request/document?customerId=${ECustomersIdsMock.ALFA_LEASING}&dealId=123&dealDocNumber=07F78L`,
        );
    });
});
