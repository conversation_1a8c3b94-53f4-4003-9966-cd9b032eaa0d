import {
    ECompanyNamesMock,
    ECreditProductsDocNumbersMock,
    ECustomersIdsMock,
} from '../../../../../src/server/mocks/data/constants';
import { getDatePairWithTextMonth } from '../../../../utils/date-utils';
import {
    checkCreditDocumentation,
    checkGuarantyPaymentSchedule,
    checkSuspensiveConditions,
} from '../../../../utils/interface-checkers';
import { goToCompany, goToProduct } from '../../../../utils/navigation-helpers';
import { checkNormalizedTextByGet } from '../../../../utils/text-utils';

describe('Гарантия', () => {
    beforeEach(() => {
        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.GUARANTY);
    });

    it('Проверка верхней части', () => {
        cy.get('.common-heading__heading').within(() => {
            checkNormalizedTextByGet('.title-view__title', [
                `Гарантия № ${ECreditProductsDocNumbersMock.GUARANTY}`,
            ]);

            checkNormalizedTextByGet('.title-view__subtitle', [ECompanyNamesMock.ALFA_LEASING]);
        });
    });

    it('Проверка вкладки "Условия договора"', () => {
        cy.findByText('Условия договора').should('be.visible').click();
        const { todayDate } = getDatePairWithTextMonth(false);
        const { nextYearDate } = getDatePairWithTextMonth(false);

        cy.findByTestId('conditions-pane').within(() => {
            checkNormalizedTextByGet('.guaranty__condition-item', ['Сумма', '100 000,00 ₽'], 0);
            checkNormalizedTextByGet('.guaranty__condition-item', ['Дата выдачи', todayDate], 1);
            checkNormalizedTextByGet(
                '.guaranty__condition-item',
                ['Действие гарантии', `${todayDate} - ${nextYearDate}`],
                2,
            );
            checkNormalizedTextByGet(
                '.guaranty__condition-item',
                ['Бенефициар', 'ООО Кампания'],
                3,
            );
        });
    });

    it('Проверка вкладки "График платежей"', () => checkGuarantyPaymentSchedule());

    it('Проверка вкладки "Кредитная документация"', () => checkCreditDocumentation());

    it('Проверка вкладки "Отлагательные условия"', () => {
        checkSuspensiveConditions(
            '№03BW4Q',
            `/ak/request/document?customerId=${ECustomersIdsMock.ALFA_LEASING}&dealId=123&dealDocNumber=03BW4Q`,
        );
    });
});
