import {
    ECompanyNamesMock,
    ECreditProductsDocNumbersMock,
    ECustomersIdsMock,
} from '../../../../../src/server/mocks/data/constants';
import {
    checkCreditConditions,
    checkCreditDocumentation,
    checkCreditPaymentSchedule,
    checkDownloadPaymentSchedule,
    checkSuspensiveConditions,
} from '../../../../utils/interface-checkers';
import { goToCompany, goToProduct } from '../../../../utils/navigation-helpers';
import {
    checkNormalizedTextByGet,
    checkNormalizedTextByTestId,
} from '../../../../utils/text-utils';

describe('Кредит', () => {
    beforeEach(() => {
        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.BUSINESS_CREDIT);
    });

    it('Проверка верхней части', () => {
        cy.get('.common-heading__heading').within(() => {
            checkNormalizedTextByGet('.title-view__title', [
                `Кредит № ${ECreditProductsDocNumbersMock.BUSINESS_CREDIT}`,
            ]);
            checkNormalizedTextByGet('.title-view__subtitle', [
                'К счёту ••4842',
                ECompanyNamesMock.ALFA_LEASING,
            ]);
        });
    });

    it('Проверка блока "Платежи" у кредита с одинаковыми датами погашения', () => {
        cy.get('.credit-dates-pane').within(() => {
            cy.findByText('Платежи').should('be.visible');

            cy.findByTestId('bill-card-Просрочено').within(() => {
                cy.findByText('Просрочено').should('be.visible');

                checkNormalizedTextByTestId('bill-card-text', ['400 400,00 ₽']);
                checkNormalizedTextByTestId('bill-card-subtext', ['Пополните счёт']);

                cy.findByTestId('bill-card-text').children().last().click();
            });
        });

        checkNormalizedTextByTestId('bill-card-tooltip', [
            '0,00 ₽ — основной долг',
            '0,00 ₽ — проценты',
            '400 400,00 ₽ — неустойка',
        ]);

        cy.get('body').click();

        cy.get('.credit-dates-pane').within(() => {
            cy.findByTestId('bill-card-Ближайший').within(() => {
                cy.findByText('Ближайший').should('be.visible');

                checkNormalizedTextByTestId('bill-card-text', ['400 400,00 ₽']);
                checkNormalizedTextByTestId('bill-card-subtext', ['12 апреля 2017']);

                cy.findByTestId('bill-card-text').children().last().click();
            });
        });

        checkNormalizedTextByTestId('bill-card-tooltip', [
            '400 000,00 ₽ — основной долг',
            '400,00 ₽ — проценты',
        ]);
    });

    it('Проверка блока "Платежи" у кредита с разными датами погашения', () => {
        goToCompany();
        goToProduct(ECreditProductsDocNumbersMock.BUSINESS_CREDIT_DIFFERENT_DATES);

        cy.get('.credit-dates-pane').within(() => {
            cy.findByText('Платежи').should('be.visible');

            cy.findByTestId('bill-card-Просрочено').within(() => {
                cy.findByText('Просрочено').should('be.visible');

                checkNormalizedTextByTestId('bill-card-text', ['400 400,00 ₽']);
                checkNormalizedTextByTestId('bill-card-subtext', ['Пополните счёт']);

                cy.findByTestId('bill-card-text').children().last().click();
            });
        });

        checkNormalizedTextByTestId('bill-card-tooltip', [
            '0,00 ₽ — основной долг',
            '0,00 ₽ — проценты',
            '400 400,00 ₽ — неустойка',
        ]);

        cy.get('body').click();

        cy.get('.credit-dates-pane').within(() => {
            cy.findByTestId('bill-card-По основному долгу').within(() => {
                cy.findByText('По основному долгу').should('be.visible');

                checkNormalizedTextByTestId('bill-card-text', ['400 000,00 ₽']);
                checkNormalizedTextByTestId('bill-card-subtext', ['12 апреля 2017']);
            });

            cy.findByTestId('bill-card-По процентам').within(() => {
                cy.findByText('По процентам').should('be.visible');

                checkNormalizedTextByTestId('bill-card-text', ['400,00 ₽']);
                checkNormalizedTextByTestId('bill-card-subtext', ['12 июня 2020']);
            });
        });
    });

    it('Проверка основного блока', () => {
        cy.findByTestId('debt-pane').within(() => {
            checkNormalizedTextByTestId('total-debt', ['Осталось выплатить', '400 400,00 ₽']);

            cy.get('button').contains('Погасить досрочно').should('be.visible');
            cy.get('button').contains('Новая выписка').should('be.visible');

            checkNormalizedTextByTestId('overdue-debt', ['Основной долг', '400 400,00 ₽']);
            checkNormalizedTextByTestId('overdue-interest', ['Проценты', '400 400,00 ₽']);
            checkNormalizedTextByTestId('total-fine', ['Неустойка', '400 400,00 ₽']);
        });
    });

    it('Проверка вкладки "Условия договора"', () => {
        cy.findByText('Условия договора').should('be.visible').click();
        checkCreditConditions();
    });

    it('Проверка вкладки "График платежей"', () => {
        cy.findByText('График платежей').should('be.visible').click();
        cy.waitForNetworkIdle('*', '*', 1000);

        checkDownloadPaymentSchedule();
        checkCreditPaymentSchedule();
    });

    it('Проверка вкладки "Кредитная документация"', () => checkCreditDocumentation());

    it('Проверка вкладки "Договоры"', () => {
        cy.findByText('Договоры').should('be.visible').click();
        cy.waitForNetworkIdle('*', '*', 1000);

        cy.get('.filters-block').should('be.visible');
        cy.findByTestId('documents-list-table').should('be.visible');
    });

    it('Проверка вкладки "Отлагательные условия"', () => {
        cy.findByTestId('credit-line-tab__suspensiveConditions-toggle').click();

        checkSuspensiveConditions(
            'Кредит №064Y4K',
            `/ak/request/document?customerId=${ECustomersIdsMock.ALFA_LEASING}&dealId=123&dealDocNumber=064Y4K`,
        );
    });
});
