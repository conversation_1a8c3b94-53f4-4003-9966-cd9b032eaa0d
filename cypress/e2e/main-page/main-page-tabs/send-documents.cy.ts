import { checkEmptyStateForAkWidget } from '../../../utils/interface-checkers';
import { goToCompany } from '../../../utils/navigation-helpers';

describe('Вкладка "Отправка документов"', () => {
    it('Проверка отображения вкладки', () => {
        goToCompany();
        cy.findByText('Отправка документов').click();
        cy.waitForNetworkIdle('*', '*', 1000);
        checkEmptyStateForAkWidget();
    });
});
