import { checkCreditRequestRow } from '../../../utils/interface-checkers';
import { goToCompany } from '../../../utils/navigation-helpers';
import { checkNormalizedTextByGet } from '../../../utils/text-utils';

describe('Модалка "Альфа-Старт — овердрафт"', () => {
    beforeEach(() => {
        goToCompany();
        cy.findByTestId('main-page-tab__requests-toggle').click();
    });

    it('Проверка заявки', () => {
        cy.findByTestId('request-filter-status-select').click();
        cy.findAllByTestId('request-filter-status-select-option').eq(1).click();

        checkCreditRequestRow({
            index: 0,
            expectedDate: '26.03.2025',
            expectedProduct: ['Альфа-Старт — овердрафт', 'С‐**2ef3'],
            expectedStatus: 'Отклонена',
            expectedDescription: 'Овердрафт подключён к счёту',
            expectedAmount: '19 999 942 ₽',
            shouldHaveLink: false,
        });
    });

    it('Проверка модалки Альфа-Старт — овердрафт', () => {
        cy.findByTestId('request-filter-status-select').click();
        cy.findAllByTestId('request-filter-status-select-option').eq(1).click();
        cy.get(`#3184056b-6793-4641-924c-ea311da22ef3`).click();

        // Header
        checkNormalizedTextByGet('.status-modal__header-content', [
            'Заявка на Альфа-Старт — овердрафт',
            '26.03.2025',
            'С‐**2ef3',
        ]);

        // Проверка статуса
        cy.get('.status-modal__content')
            .find('span')
            .eq(1)
            .should('have.text', 'Отклонена')
            .invoke('attr', 'class')
            .should('include', 'status__ellipsis');

        // Footer
        cy.get('[class*="modal__footer"]').should('be.visible');
        cy.get('[class*="modal__footer"] > button')
            .eq(0)
            .should('have.text', 'Создать новую')
            .invoke('attr', 'class')
            .should('include', 'button__primary');

        cy.get('[class*="modal__footer"] > button')
            .eq(1)
            .should('have.text', 'Удалить')
            .invoke('attr', 'class')
            .should('include', 'button__secondary');
    });
});
