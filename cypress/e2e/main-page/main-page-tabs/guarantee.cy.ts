import { checkEmptyStateForAkWidget } from '../../../utils/interface-checkers';
import { goToCompany } from '../../../utils/navigation-helpers';

describe('Вкладка "Гарантии"', () => {
    it('Проверка отображения вкладки', () => {
        goToCompany();
        cy.findByText('Гарантии').click();
        cy.waitForNetworkIdle('*', '*', 1000);
        cy.findByTestId('main-page-tab__contracts-toggle').should('be.visible');
        checkEmptyStateForAkWidget();
    });
});
