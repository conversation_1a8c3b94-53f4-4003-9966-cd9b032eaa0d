import { ECompanyNamesMock } from '../../../../src/server/mocks/data/constants';
import {
    getCreditRequestList,
    getCreditRequestProgressStages,
    getCreditRequestProgressStagesWithLastStage,
    getCreditRequestSingleProgressStage,
} from '../../../../src/server/mocks/data/credit-request-mocks';
import {
    checkCreditRequestRow,
    checkCreditRequestRowAdaptive,
    checkStageSteps,
} from '../../../utils/interface-checkers';
import { goToCompany, goToCompanyMobileAdaptive } from '../../../utils/navigation-helpers';
import { checkNormalizedTextByGet, checkNormalizedTextByTestId } from '../../../utils/text-utils';

describe('Вкладка "Заявки"', () => {
    describe('Вкладка "Заявки" на десктопе', () => {
        beforeEach(() => {
            cy.intercept('GET', '**/credit-requests?**', getCreditRequestList).as(
                'getCreditRequests',
            );
            goToCompany();
            cy.findByTestId('main-page-tab__requests-toggle').click();
        });

        it('Проверка отображения вкладки', () => {
            cy.get('.credit-requests-table').should('be.visible');
            cy.findByTestId('main-page-tab__requests-toggle').should('be.visible');
            cy.findByTestId('main-page-tab__requests').should('be.visible');

            // Фильтры
            cy.findByTestId('request-filter-main-menu').should('be.visible');
            checkNormalizedTextByTestId('requests-filter-company-select', [
                ECompanyNamesMock.ALFA_LEASING,
            ]);
            checkNormalizedTextByTestId('request-filter-status-select', ['Действующие']);
            checkNormalizedTextByTestId('request-filter-product-select', ['Продукт']);

            // Navbar
            checkNormalizedTextByTestId('credit-requests-topbar', ['Найдено', '21']);
            checkNormalizedTextByTestId('credit-requests__new-request', ['Новая заявка']);
            checkNormalizedTextByTestId('credit-requests-topbar-settings-button', ['Настройка']);
            cy.findByTestId('credit-requests-topbar-settings-button').click();
            checkNormalizedTextByTestId('credit-requests-topbar-toolbar-grouping-menu-field', [
                'Без группировки',
            ]);
            cy.findAllByTestId('credit-requests-topbar-toolbar-column-display-option').each(
                (toolbarFilterLabel, index) => {
                    const expectedTexts = ['Дата', 'Продукт', 'Статус', 'Описание', 'Сумма'];

                    cy.wrap(toolbarFilterLabel)
                        .should('be.visible')
                        .and('include.text', expectedTexts[index]);
                },
            );

            // Header таблицы
            cy.get('.thead-cell').each((header, index) => {
                const expectedTexts = ['Дата', 'Продукт', 'Статус', 'Описание', 'Сумма'];

                cy.wrap(header).should('be.visible').and('include.text', expectedTexts[index]);
            });

            // Footer таблицы
            cy.get(`[class*=credit-requests-table__pagination]`).should('be.visible');
        });

        it('Проверка отображения заявок при сортировки', () => {
            checkCreditRequestRow({
                index: 0,
                expectedDate: '08.03.2025',
                expectedProduct: ['Заявка на кредит', 'С‐**826c'],
                expectedStatus: 'Черновик',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmount: '20 000 000 ₽',
                shouldHaveLink: true,
                expectedLink: 'заполните её',
            });

            cy.findByTestId('sort-icon-createDt').click();

            checkCreditRequestRow({
                index: 0,
                expectedDate: '08.03.2025',
                expectedProduct: ['Заявка на кредит', 'С‐**826c'],
                expectedStatus: 'Черновик',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmount: '20 000 000 ₽',
                shouldHaveLink: true,
                expectedLink: 'заполните её',
            });
            cy.findByTestId('sort-icon-createDt').click();

            checkCreditRequestRow({
                index: 0,
                expectedDate: '07.11.2024',
                expectedStatus: 'В РАБОТЕ',
                expectedProduct: ['Заявка на кредит', 'М‐**db99'],
                expectedDescription:
                    'Заявка принята – вам позвонят из банка. Чтобы узнать решение сразу, заполните данные сами',
                shouldHaveLink: false,
                expectedAmount: '',
            });
        });

        it('Проверка отображения заявок при группировке', () => {
            checkCreditRequestRow({
                index: 0,
                expectedDate: '08.03.2025',
                expectedProduct: ['Заявка на кредит', 'С‐**826c'],
                expectedStatus: 'Черновик',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmount: '20 000 000 ₽',
                shouldHaveLink: true,
                expectedLink: 'заполните её',
            });
            cy.get('.trow').should('have.length', 10);

            cy.findByTestId('credit-requests-topbar-settings-button').click();
            checkNormalizedTextByTestId('credit-requests-topbar-toolbar-grouping-menu-field', [
                'Без группировки',
            ]);

            cy.findByTestId('credit-requests-topbar-toolbar-grouping-menu-field').click();
            cy.findAllByTestId('credit-requests-topbar-toolbar-grouping-menu-option').eq(1).click();

            cy.get('.grouped-trow__preview-row').should('have.length', 3);
            cy.get('.grouped-trow-title').each((groupedTitle, index) => {
                const expectedTexts = ['Заявка на кредит', 'Кредитная карта', 'Бизнес-кредит'];

                cy.wrap(groupedTitle)
                    .should('be.visible')
                    .and('include.text', expectedTexts[index]);
            });
        });

        it('Проверка переключения фильтров', () => {
            checkCreditRequestRow({
                index: 0,
                expectedDate: '08.03.2025',
                expectedProduct: ['Заявка на кредит', 'С‐**826c'],
                expectedStatus: 'Черновик',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmount: '20 000 000 ₽',
                shouldHaveLink: true,
                expectedLink: 'заполните её',
            });

            cy.findByTestId('request-filter-product-select').click();
            cy.findAllByTestId('request-filter-product-select-option').eq(2).click();
            cy.findByTestId('request-filter-product-select-options-list-apply').click();

            checkNormalizedTextByTestId('request-filter-product-select', [
                'Продукт:',
                'Бизнес-кредит',
            ]);
            checkNormalizedTextByTestId('credit-requests-topbar', ['Найдено', '3']);

            checkCreditRequestRow({
                index: 0,
                expectedDate: '10.02.2025',
                expectedProduct: ['Бизнес-кредит', 'М‐**8859'],
                expectedStatus: 'НА ПОДПИСЬ',
                expectedDescription: 'Подпишите документы удобным способом, чтобы получить кредит',
                expectedAmount: '69 000 ₽ ₽',
                shouldHaveLink: true,
                expectedLink: 'Подпишите документы',
            });

            cy.findByTestId('sort-icon-createDt').click();
            cy.findByTestId('sort-icon-createDt').click();

            checkCreditRequestRow({
                index: 0,
                expectedDate: '26.12.2024',
                expectedProduct: ['Бизнес-кредит', 'М‐**0837'],
                expectedStatus: 'ЧЕРНОВИК',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmount: '',
                shouldHaveLink: false,
            });

            cy.findByTestId('request-filter-product-select').click();
            cy.findAllByTestId('request-filter-product-select-option').eq(2).click();
            cy.findAllByTestId('request-filter-product-select-option').eq(1).click();
            cy.findByTestId('request-filter-product-select-options-list-apply').click();

            checkNormalizedTextByTestId('credit-requests-topbar', ['Найдено', '0']);
            cy.get('.documents-list__error-state').should('be.visible');

            checkNormalizedTextByGet('.documents-list__error-state', [
                'Ничего не нашлось',
                'Попробуйте сбросить фильтры и поискать ещё раз',
                'Сбросить фильтры',
            ]);

            cy.get('.error-state__button').click();

            checkNormalizedTextByTestId('requests-filter-company-select', [
                ECompanyNamesMock.ALFA_LEASING,
            ]);
            checkNormalizedTextByTestId('request-filter-status-select', ['Действующие']);
            cy.findByTestId('request-filter-product-select')
                .should('exist')
                .should('have.text', 'Продукт');
            checkNormalizedTextByTestId('credit-requests-topbar', ['Найдено', '21']);

            cy.findByTestId('sort-icon-createDt').click();

            checkCreditRequestRow({
                index: 0,
                expectedDate: '08.03.2025',
                expectedProduct: ['Заявка на кредит', 'С‐**826c'],
                expectedStatus: 'Черновик',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmount: '20 000 000 ₽',
                shouldHaveLink: true,
                expectedLink: 'заполните её',
            });

            cy.findByTestId('request-filter-status-select').click();
            cy.findAllByTestId('request-filter-status-select-option').eq(1).click();

            checkNormalizedTextByTestId('credit-requests-topbar', ['Найдено', '6']);

            checkCreditRequestRow({
                index: 1,
                expectedDate: '13.02.2025',
                expectedProduct: ['Заявка на кредит', 'М‐**c06e'],
                expectedStatus: 'В АРХИВЕ',
                expectedDescription: 'Заявка закрыта',
                expectedAmount: '',
                shouldHaveLink: false,
            });
        });

        it('Проверка заявок в таблице заявок', () => {
            checkCreditRequestRow({
                index: 0,
                expectedDate: '08.03.2025',
                expectedProduct: ['Заявка на кредит', 'С‐**826c'],
                expectedStatus: 'Черновик',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmount: '20 000 000 ₽',
                shouldHaveLink: true,
                expectedLink: 'заполните её',
            });

            checkCreditRequestRow({
                index: 4,
                expectedDate: '10.02.2025',
                expectedProduct: ['Бизнес-кредит', 'М‐**8859'],
                expectedStatus: 'НА ПОДПИСЬ',
                expectedDescription: 'Подпишите документы удобным способом, чтобы получить кредит',
                expectedAmount: '69 000 ₽ ₽',
                shouldHaveLink: true,
                expectedLink: 'Подпишите документы',
            });

            cy.get('.credit-requests-table__pagination [class*=table__pagination] button')
                .eq(1)
                .click();

            checkCreditRequestRow({
                index: 0,
                expectedDate: '26.12.2024',
                expectedProduct: ['Бизнес-кредит', 'М‐**0837'],
                expectedStatus: 'ЧЕРНОВИК',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmount: '',
                shouldHaveLink: false,
            });

            cy.findByTestId('request-filter-status-select').click();
            cy.findAllByTestId('request-filter-status-select-option').eq(1).click();

            checkCreditRequestRow({
                index: 0,
                expectedDate: '25.02.2025',
                expectedProduct: ['Заявка на кредит', 'М‐**6251'],
                expectedStatus: 'ОТКЛОНЕНА',
                expectedDescription: 'Действие заявки закончилось. Вы можете оформить новую заявку',
                expectedAmount: '15 759 000 ₽ ₽',
                shouldHaveLink: false,
            });
        });

        it('Проверка модалки ммб заявки c отсутствующим прогресс баром в ответе', () => {
            cy.intercept('GET', '**/progress/stages/**', getCreditRequestSingleProgressStage).as(
                'getCreditRequestSingleProgressStage',
            );

            cy.get(`#287dd699-8d41-415f-9305-18320a5ca6e5`).click();
            cy.findByTestId('status-modal').should('be.visible');
            cy.waitForNetworkIdle('*', '*', 1000);

            // Header
            checkNormalizedTextByGet('.status-modal__header-content', [
                'Заявка на кредит',
                '06.02.2025',
                'М‐**a6e5',
            ]);

            // Проверка информация о заявке
            cy.findByTestId('request-payment-info').should('be.visible');
            checkNormalizedTextByTestId('to-date', ['Срок', '12 месяцев']);
            checkNormalizedTextByTestId('sum', ['Сумма', '0,00']);
            checkNormalizedTextByTestId('rate', ['Ставка', '26.5%', 'годовых']);

            // Проверка прогресс баров
            cy.get('.progress-bar__status-content')
                .find('span')
                .should('have.text', 'В архиве')
                .invoke('attr', 'class')
                .should('include', 'status__ellipsis');

            checkNormalizedTextByGet('.status-modal__step-wrapper > div', ['Заявка закрыта']);

            // Footer
            cy.findByTestId('status-modal-footer').should('be.visible');
        });

        it('Проверка модалки ммб заявки c 404 ответом', () => {
            const requestId = 'a8d1b218-b866-4fac-aaa4-14ef51158859';

            cy.intercept('GET', '**/progress/stages/**', {
                statusCode: 404,
                body: {
                    code: 'NOT_FOUND',
                    message: `Not found application with id: ${requestId}`,
                },
            }).as('getCreditRequestSingleProgressStage');

            cy.get(`#${requestId}`).click();
            cy.findByTestId('status-modal').should('be.visible');
            cy.waitForNetworkIdle('*', '*', 1000);

            // Header
            checkNormalizedTextByGet('.status-modal__header-content', [
                'Заявка на кредит',
                '10.02.2025',
                'М‐**8859',
            ]);

            // Проверка информация о заявке
            cy.findByTestId('request-payment-info').should('be.visible');
            checkNormalizedTextByTestId('to-date', ['Срок', '1 месяц']);
            checkNormalizedTextByTestId('sum', ['Сумма', '69 000,00', '₽']);
            checkNormalizedTextByTestId('rate', ['Ставка', '99%', 'в день']);

            // Проверка прогресс баров
            cy.get('.progress-bar__status-content')
                .find('span')
                .should('have.text', 'На подпись')
                .invoke('attr', 'class')
                .should('include', 'status__ellipsis');

            checkNormalizedTextByGet('.status-modal__step-wrapper > div', [
                'Подпишите документы удобным способом, чтобы получить кредит',
            ]);

            // Footer
            cy.findByTestId('status-modal-footer').should('be.visible');
            cy.get('[data-test-id="status-modal-footer"] > button')
                .should('have.text', 'Подписать')
                .invoke('attr', 'class')
                .should('include', 'button__primary');
        });

        it('Проверка модалки ммб заявки с прогресс баром', () => {
            cy.intercept('GET', '**/progress/stages/**', getCreditRequestProgressStages).as(
                'getCreditRequestProgressStages',
            );

            cy.get(`#9dd14139-18bd-45ca-8074-86608e39fe07`).click();
            cy.findByTestId('status-modal').should('be.visible');

            cy.waitForNetworkIdle('*', '*', 1000);

            // Header
            checkNormalizedTextByGet('.status-modal__header-content', [
                'Заявка на кредит',
                '17.02.2025',
                'М‐**fe07',
            ]);

            // Проверка информация о заявке
            cy.findByTestId('request-payment-info').should('be.visible');
            checkNormalizedTextByTestId('to-date', ['Срок', '3 месяца']);
            checkNormalizedTextByTestId('sum', ['Сумма', '0,00']);
            checkNormalizedTextByTestId('rate', ['Ставка', '4.5%', 'в месяц']);

            // Проверка прогресс баров
            cy.findByTestId('progress-bar')
                .should('be.visible')
                .children(`div[role=button]`)
                .its('length')
                .should('eq', 6);

            cy.findByTestId('progress-bar').within(() => {
                // Проверка иконок
                cy.get('div[role=button]').each(($stage, index) => {
                    const expectedClasses = [
                        'steps__completed',
                        'steps__completed',
                        'steps__completed',
                        'steps__selected',
                        null,
                        null,
                    ];

                    if (expectedClasses[index]) {
                        cy.wrap($stage)
                            .should('be.visible')
                            .invoke('attr', 'class')
                            .should('include', expectedClasses[index]);
                    } else {
                        cy.wrap($stage).find(`div[class*=steps__checkbox]`).should('be.visible');
                    }
                });

                checkStageSteps([
                    ['Заявка на кредит', null, null, null],
                    ['Рассмотрение заявки', null, null, null],
                    ['Кредитное предложение', null, null, null],
                    [
                        'Подтверждение условий',
                        'В работе',
                        'Заявка принята – вам позвонят из банка. Чтобы узнать решение сразу, заполните данные сами',
                        '29.01.2025',
                    ],
                    ['Подписание', null, null, null],
                    ['Кредит оформлен', null, null, null],
                ]);
            });

            // Footer
            cy.findByTestId('status-modal-footer').should('be.visible');
        });

        it('Проверка модалки ммб заявки с завершенным прогресс баром', () => {
            cy.intercept(
                'GET',
                '**/progress/stages/**',
                getCreditRequestProgressStagesWithLastStage,
            ).as('getCreditRequestProgressStages');

            cy.get(`#1511748e-d69f-4cc0-abae-9f9d0270d169`).click();
            cy.findByTestId('status-modal').should('be.visible');

            cy.waitForNetworkIdle('*', '*', 1000);

            // Header
            checkNormalizedTextByGet('.status-modal__header-content', [
                'Заявка на кредит',
                '26.02.2025',
                'М‐**d169',
            ]);

            // Проверка информация о заявке
            cy.findByTestId('request-payment-info').should('be.visible');
            checkNormalizedTextByTestId('to-date', ['Срок', '4 месяца']);
            checkNormalizedTextByTestId('sum', ['Сумма', '1 000 000,00', '₽']);
            checkNormalizedTextByTestId('rate', ['Ставка', '13.9%', 'в месяц']);

            // Проверка прогресс баров
            cy.findByTestId('progress-bar')
                .should('be.visible')
                .children(`div[role=button]`)
                .its('length')
                .should('eq', 6);

            cy.findByTestId('progress-bar').within(() => {
                // Проверка иконок
                cy.get('div[role=button]').each(($stage, index) => {
                    const expectedClasses = [
                        'steps__completed',
                        'steps__completed',
                        'steps__completed',
                        'steps__completed',
                        'steps__completed',
                        'steps__selected',
                    ];

                    if (expectedClasses[index]) {
                        cy.wrap($stage)
                            .should('be.visible')
                            .invoke('attr', 'class')
                            .should('include', expectedClasses[index]);
                    } else {
                        cy.wrap($stage).find(`div[class*=steps__checkbox]`).should('be.visible');
                    }
                });

                checkStageSteps([
                    ['Заявка на кредит', null, null, null],
                    ['Рассмотрение заявки', null, null, null],
                    ['Кредитное предложение', null, null, null],
                    ['Подтверждение условий', null, null, null],
                    ['Подписание', null, null, null],
                    [
                        'Кредит оформлен',
                        'Одобрена',
                        'Сегодня кредит появится в ваших продуктах — им можно пользоваться сразу',
                        '24.07.2025',
                    ],
                ]);
            });

            // Footer
            cy.findByTestId('status-modal-footer').should('be.visible');
            cy.get('[data-test-id="status-modal-footer"] > button')
                .eq(0)
                .should('have.text', 'Новая заявка')
                .invoke('attr', 'class')
                .should('include', 'button__primary');

            cy.get('[data-test-id="status-modal-footer"] > button')
                .eq(1)
                .should('have.text', 'Мои продукты')
                .invoke('attr', 'class')
                .should('include', 'button__secondary');
        });
    });

    describe('Вкладка "Заявки" на адаптиве', () => {
        beforeEach(() => {
            cy.intercept('GET', '**/credit-requests?**', getCreditRequestList).as(
                'getCreditRequests',
            );
            goToCompanyMobileAdaptive();
            cy.findByTestId('main-page-tab__requests-toggle').click();
        });

        it('Проверка отображения вкладки', () => {
            cy.findByTestId('main-page-tab__requests').should('be.visible');
            cy.findByTestId('main-page-tab__requests-toggle').should('be.visible');
            cy.get('.credit-requests-mobile').should('be.visible');

            // Фильтры
            cy.findByTestId('request-filter-main-menu').should('be.visible');
            checkNormalizedTextByTestId('requests-filter-company-select', [
                ECompanyNamesMock.ALFA_LEASING,
            ]);
            checkNormalizedTextByTestId('request-filter-status-select', ['Действующие']);
            cy.findByTestId('request-filter-product-select')
                .should('exist')
                .and('not.be.visible')
                .should('have.text', 'Продукт');

            // Navbar
            checkNormalizedTextByGet('.credit-requests-mobile__topbar', ['Найдено', '21']);

            checkNormalizedTextByTestId('request-filter-sort-select-field', ['Сортировка']);
            cy.findByTestId('request-filter-sort-select-field').click();
            checkNormalizedTextByTestId('request-filter-sort-select-bottom-sheet-header-title', [
                'Сортировка',
            ]);
            cy.findAllByTestId('request-filter-sort-select-option').each(
                (toolbarFilterLabel, index) => {
                    const expectedTexts = ['Сначала новые', 'Сначала старые'];

                    cy.wrap(toolbarFilterLabel)
                        .should('be.visible')
                        .and('include.text', expectedTexts[index]);
                },
            );

            cy.findByTestId('request-filter-sort-select-bottom-sheet-header-closer').click();

            cy.get('.credit-requests-mobile__row').should('have.length', 10);
            checkNormalizedTextByGet('.credit-requests-mobile__load-more', ['Показать еще']);
            cy.get('.credit-requests-mobile__load-more').click();
            cy.get('.credit-requests-mobile__row').should('have.length', 20);

            // Проверяем кнопку "Новая заявка"
            checkNormalizedTextByGet('.credit-requests-mobile__redirect-wrapper', ['Новая заявка']);
        });

        it('Проверка отображения заявок при сортировки', () => {
            checkCreditRequestRowAdaptive({
                index: 0,
                expectedStatus: 'Черновик',
                expectedHeader: 'Заявка на кредит',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmountAndDate: ['20 000 000,00 ₽', '08.03.2025'],
                shouldHaveLink: true,
                expectedFooterText: 'Заполните её',
            });

            cy.findByTestId('request-filter-sort-select-field').click();
            cy.findAllByTestId('request-filter-sort-select-option').eq(1).click();

            checkCreditRequestRowAdaptive({
                index: 0,
                expectedStatus: 'В работе',
                expectedHeader: 'Заявка на кредит',
                expectedDescription:
                    'Заявка принята – вам позвонят из банка. Чтобы узнать решение сразу, заполните данные сами',
                expectedAmountAndDate: ['07.11.2024'],
                shouldHaveLink: false,
            });
        });

        it('Проверка переключения фильтров', () => {
            checkCreditRequestRowAdaptive({
                index: 0,
                expectedStatus: 'Черновик',
                expectedHeader: 'Заявка на кредит',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmountAndDate: ['20 000 000,00 ₽', '08.03.2025'],
                shouldHaveLink: true,
                expectedFooterText: 'Заполните её',
            });

            cy.findByTestId('request-filter-product-select').click();
            cy.findAllByTestId('request-filter-product-select-option').eq(4).click();
            cy.findByTestId('request-filter-product-select-options-list-apply').click();

            checkNormalizedTextByGet('.credit-requests-mobile__topbar', ['Найдено', '3']);

            checkCreditRequestRowAdaptive({
                index: 0,
                expectedStatus: 'Одобрена',
                expectedHeader: 'Кредитная карта',
                expectedDescription:
                    'Сегодня кредит появится в ваших продуктах — им можно пользоваться сразу',
                expectedAmountAndDate: ['26.02.2025'],
                shouldHaveLink: false,
            });

            cy.findByTestId('request-filter-sort-select-field').click();
            cy.findAllByTestId('request-filter-sort-select-option').eq(1).click();

            checkCreditRequestRowAdaptive({
                index: 0,
                expectedStatus: 'В работе',
                expectedHeader: 'Кредитная карта',
                expectedDescription:
                    'Проверяем заявку. Когда завершим, можно будет продолжить оформление кредита',
                expectedAmountAndDate: ['26.12.2024'],
                shouldHaveLink: false,
            });

            cy.findByTestId('request-filter-status-select').click();
            cy.findAllByTestId('request-filter-status-select-option').eq(1).click();

            cy.get('.documents-list__error-state').should('be.visible');

            checkNormalizedTextByGet('.documents-list__error-state', [
                'Ничего не нашлось',
                'Попробуйте сбросить фильтры и поискать ещё раз',
                'Сбросить фильтры',
            ]);

            cy.get('.error-state__button').click();

            checkNormalizedTextByTestId('requests-filter-company-select', [
                ECompanyNamesMock.ALFA_LEASING,
            ]);
            checkNormalizedTextByTestId('request-filter-status-select', ['Действующие']);
            cy.findByTestId('request-filter-product-select')
                .should('exist')
                .should('have.text', 'Продукт');
            checkNormalizedTextByGet('.credit-requests-mobile__topbar', ['Найдено', '21']);
            checkNormalizedTextByTestId('request-filter-sort-select-field', ['Сначала старые']);

            checkCreditRequestRowAdaptive({
                index: 0,
                expectedStatus: 'В работе',
                expectedHeader: 'Заявка на кредит',
                expectedDescription:
                    'Заявка принята – вам позвонят из банка. Чтобы узнать решение сразу, заполните данные сами',
                expectedAmountAndDate: ['07.11.2024'],
                shouldHaveLink: false,
            });
        });

        it('Проверка заявок в списке заявок', () => {
            // Проверяем заявку СБ
            checkCreditRequestRowAdaptive({
                index: 0,
                expectedStatus: 'Черновик',
                expectedHeader: 'Заявка на кредит',
                expectedDescription: 'Сохранили заявку — заполните её до конца',
                expectedAmountAndDate: ['20 000 000,00 ₽', '08.03.2025'],
                shouldHaveLink: true,
                expectedFooterText: 'Заполните её',
            });

            // Проверяем заявку ММБ
            checkCreditRequestRowAdaptive({
                index: 2,
                expectedStatus: 'Одобрена',
                expectedHeader: 'Кредитная карта',
                expectedDescription:
                    'Сегодня кредит появится в ваших продуктах — им можно пользоваться сразу',
                expectedAmountAndDate: ['1 000 000,00 ₽', '26.02.2025'],
                shouldHaveLink: false,
            });
        });

        it('Проверка модалки ммб заявки c отсутствующим прогресс баром в ответе', () => {
            cy.intercept('GET', '**/progress/stages/**', getCreditRequestSingleProgressStage).as(
                'getCreditRequestSingleProgressStage',
            );

            cy.get(`.credit-requests-mobile__row`).eq(5).click();
            cy.get('.status-modal-mobile').should('be.visible');
            cy.waitForNetworkIdle('*', '*', 1000);

            // Header
            checkNormalizedTextByGet('.status-modal-mobile__header', [
                'Заявка на кредит',
                '06.02.2025',
                'М‐**a6e5',
            ]);

            // Проверка информация о заявке
            cy.findByTestId('request-payment-info').should('be.visible');
            checkNormalizedTextByTestId('to-date', ['Срок', '12 месяцев']);
            checkNormalizedTextByTestId('sum', ['Сумма', '0,00']);
            checkNormalizedTextByTestId('rate', ['Ставка', '26.5%', 'годовых']);

            // Проверка прогресс баров
            cy.get('.progress-bar__status-content')
                .find('span')
                .should('have.text', 'В архиве')
                .invoke('attr', 'class')
                .should('include', 'status__ellipsis');

            checkNormalizedTextByGet('.status-modal-mobile__step-wrapper > div', [
                'Заявка закрыта',
            ]);

            // Footer
            cy.get('.status-modal-mobile__footer-content')
                .should('be.visible')
                .invoke('attr', 'class')
                .should('include', 'modal__sticky');
        });

        it('Проверка модалки ммб заявки с прогресс баром', () => {
            cy.intercept('GET', '**/progress/stages/**', getCreditRequestProgressStages).as(
                'getCreditRequestProgressStages',
            );

            cy.get(`.credit-requests-mobile__row`).eq(3).click();
            cy.get('.status-modal-mobile').should('be.visible');
            cy.waitForNetworkIdle('*', '*', 1000);

            // Header
            checkNormalizedTextByGet('.status-modal-mobile__header', [
                'Заявка на кредит',
                '17.02.2025',
                'М‐**fe07',
            ]);

            // Проверка информация о заявке
            cy.findByTestId('request-payment-info').should('be.visible');
            checkNormalizedTextByTestId('to-date', ['Срок', '3 месяца']);
            checkNormalizedTextByTestId('sum', ['Сумма', '0,00']);
            checkNormalizedTextByTestId('rate', ['Ставка', '4.5%', 'в месяц']);

            // Проверка прогресс баров
            cy.findByTestId('progress-bar')
                .should('be.visible')
                .children(`div[role=button]`)
                .its('length')
                .should('eq', 6);

            cy.findByTestId('progress-bar').within(() => {
                // Проверка иконок
                cy.get('div[role=button]').each(($stage, index) => {
                    const expectedClasses = [
                        'steps__completed',
                        'steps__completed',
                        'steps__completed',
                        'steps__selected',
                        null,
                        null,
                    ];

                    if (expectedClasses[index]) {
                        cy.wrap($stage)
                            .should('be.visible')
                            .invoke('attr', 'class')
                            .should('include', expectedClasses[index]);
                    } else {
                        cy.wrap($stage).find(`div[class*=steps__checkbox]`).should('be.visible');
                    }
                });

                checkStageSteps([
                    ['Заявка на кредит', null, null, null],
                    ['Рассмотрение заявки', null, null, null],
                    ['Кредитное предложение', null, null, null],
                    [
                        'Подтверждение условий',
                        'В работе',
                        'Заявка принята – вам позвонят из банка. Чтобы узнать решение сразу, заполните данные сами',
                        '29.01.2025',
                    ],
                    ['Подписание', null, null, null],
                    ['Кредит оформлен', null, null, null],
                ]);
            });

            // Footer
            cy.get('.status-modal-mobile__footer-content').should('be.visible');
        });

        it('Проверка модалки ммб заявки с завершенным прогресс баром', () => {
            cy.intercept(
                'GET',
                '**/progress/stages/**',
                getCreditRequestProgressStagesWithLastStage,
            ).as('getCreditRequestProgressStages');

            cy.get(`.credit-requests-mobile__row`).eq(2).click();
            cy.get('.status-modal-mobile').should('be.visible');
            cy.waitForNetworkIdle('*', '*', 1000);

            // Header
            checkNormalizedTextByGet('.status-modal-mobile__header', [
                'Заявка на кредит',
                '26.02.2025',
                'М‐**d169',
            ]);

            // Проверка информация о заявке
            cy.findByTestId('request-payment-info').should('be.visible');
            checkNormalizedTextByTestId('to-date', ['Срок', '4 месяца']);
            checkNormalizedTextByTestId('sum', ['Сумма', '1 000 000,00', '₽']);
            checkNormalizedTextByTestId('rate', ['Ставка', '13.9%', 'в месяц']);

            // Проверка прогресс баров
            cy.findByTestId('progress-bar')
                .should('be.visible')
                .children(`div[role=button]`)
                .its('length')
                .should('eq', 6);

            cy.findByTestId('progress-bar').within(() => {
                // Проверка иконок
                cy.get('div[role=button]').each(($stage, index) => {
                    const expectedClasses = [
                        'steps__completed',
                        'steps__completed',
                        'steps__completed',
                        'steps__completed',
                        'steps__completed',
                        'steps__selected',
                    ];

                    if (expectedClasses[index]) {
                        cy.wrap($stage)
                            .should('be.visible')
                            .invoke('attr', 'class')
                            .should('include', expectedClasses[index]);
                    } else {
                        cy.wrap($stage).find(`div[class*=steps__checkbox]`).should('be.visible');
                    }
                });

                checkStageSteps([
                    ['Заявка на кредит', null, null, null],
                    ['Рассмотрение заявки', null, null, null],
                    ['Кредитное предложение', null, null, null],
                    ['Подтверждение условий', null, null, null],
                    ['Подписание', null, null, null],
                    [
                        'Кредит оформлен',
                        'Одобрена',
                        'Сегодня кредит появится в ваших продуктах — им можно пользоваться сразу',
                        '24.07.2025',
                    ],
                ]);
            });

            // Footer
            cy.get('.status-modal-mobile__footer-content').should('be.visible');
            cy.get('.status-modal-mobile__footer-content > button')
                .eq(0)
                .should('have.text', 'Мои продукты')
                .invoke('attr', 'class')
                .should('include', 'button__secondary');

            cy.get('.status-modal-mobile__footer-content > button')
                .eq(1)
                .should('have.text', 'Новая заявка')
                .invoke('attr', 'class')
                .should('include', 'button__primary');
        });
    });
});
