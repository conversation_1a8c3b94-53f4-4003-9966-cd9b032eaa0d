import { checkEmptyStateForAkWidget } from '../../../utils/interface-checkers';
import { goToCompany } from '../../../utils/navigation-helpers';

describe('Вкладка "Договоры"', () => {
    it('Проверка отображения вкладки', () => {
        goToCompany();
        cy.findByText('Договоры').click();
        cy.waitForNetworkIdle('*', '*', 1000);
        cy.findByTestId('main-page-tab__contracts-toggle').should('be.visible');
        checkEmptyStateForAkWidget();
    });
});
