import { goToCompany } from '../../../utils/navigation-helpers';

describe('Вкладка "Заявки"', () => {
    beforeEach(() => {
        goToCompany();
        cy.findByTestId('main-page-tab__requests-toggle').click();
    });

    it('Проверка отображения вкладки', () => {
        cy.findByTestId('main-page-tab__requests').should('be.visible');
        cy.findByTestId('main-page-tab__requests-toggle').should('be.visible');
    });

    it('Проверка заявки', () => {
        cy.get('.thead-cell').each((header, index) => {
            const expectedTexts = ['Дата', 'Продукт', 'Статус', 'Описание', 'Сумма'];

            cy.wrap(header).should('be.visible').and('include.text', expectedTexts[index]);
        });

        cy.get('.trow')
            .first()
            .within(() => {
                cy.get('.tcell').eq(0).should('be.visible').and('contain.text', '11.11.2024');

                cy.get('.tcell').eq(1).should('be.visible').and('contain.text', 'Заявка на кредит');

                cy.get('.tcell')
                    .eq(2)
                    .find('span')
                    .last()
                    .invoke('attr', 'class')
                    .should('include', 'status__ellipsis');

                cy.get('.tcell')
                    .eq(3)
                    .should('be.visible')
                    .and('have.text', 'Сохранили заявку — заполните её до конца');

                cy.get('.tcell')
                    .eq(4)
                    .find('span')
                    .invoke('attr', 'class')
                    .should('include', 'amount__bold');
            });
    });

    it('Проверка переадресации после клика на заявку', () => {
        cy.get('.trow').first().click();
        cy.url({ timeout: 25000 }).then((currentUrl) => {
            expect(currentUrl).to.include('creditsb');
        });
    });

    it('Проверка переадресации после клика на кнопку "Выбрать продукт"', () => {
        cy.get('.tmain');

        cy.findByTestId('credit-requests__new-request').click();
        cy.findByTestId('credit-requests__select-product').click();

        cy.url({ timeout: 25000 }).then((currentUrl) => {
            expect(currentUrl).to.include('creditsb');
        });
    });

    it('Проверка переадресации после клика на кнопку "Пересчитать лимит"', () => {
        cy.get('.tmain');

        cy.findByTestId('credit-requests__new-request').click();
        cy.findByTestId('credit-requests__recalculate-limit').click();

        cy.url({ timeout: 25000 }).then((currentUrl) => {
            expect(currentUrl).to.include('creditsb');
        });
    });
});
