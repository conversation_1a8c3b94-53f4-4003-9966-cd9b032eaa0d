import { checkEmptyStateForAkWidget } from '../../../utils/interface-checkers';
import { goToCompany } from '../../../utils/navigation-helpers';

describe('Вкладка "Транши"', () => {
    it('Проверка отображения вкладки', () => {
        goToCompany();
        cy.findByText('Транши').click();
        cy.waitForNetworkIdle('*', '*', 1000);
        cy.findByTestId('main-page-tab__tranches-toggle').should('be.visible');
        checkEmptyStateForAkWidget();
    });
});
