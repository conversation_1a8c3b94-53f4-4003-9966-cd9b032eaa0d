import {
    ECompanyNamesMock,
    ECreditProductsDocNumbersMock,
} from '../../../src/server/mocks/data/constants';
import { goToCompany } from '../../utils/navigation-helpers';
import { checkNormalizedTextByGet, checkNormalizedTextByTestId } from '../../utils/text-utils';

describe('Продукты на главной странице', () => {
    beforeEach(() => {
        goToCompany();
        cy.get('body').then(($body) => {
            if ($body.find('[class^="filter-tag__clear"]').length > 0) {
                cy.get('[class^="filter-tag__clear"]').click();
            }
        });
        cy.waitForNetworkIdle('*', '*', 1000);
    });

    it('Проверка кредита', () => {
        cy.get('[data-test-id="product-view-businessCredit"]')
            .first()
            .within(() => {
                checkNormalizedTextByTestId('product-view-pane-title-businessCredit', [
                    `№${ECreditProductsDocNumbersMock.BUSINESS_CREDIT}`,
                    ECompanyNamesMock.ALFA_LEASING,
                ]);

                checkNormalizedTextByGet('.product-debt-pane__title', [
                    'Сумма',
                    '***********,00 ₽',
                ]);

                checkNormalizedTextByGet('.pane-item', ['Просрочено', '5 670,00 ₽'], 0);
                checkNormalizedTextByGet(
                    '.pane-item',
                    ['Действие договора', '25.10.2022 - 31.12.2027'],
                    2,
                );

                cy.findByTestId('get-credit__064Y4K').should('contain.text', 'Новый кредит');
                cy.get('.info-pane__picker-button').should('be.visible').click();
            });
    });

    it('Проверка кредитной линии', () => {
        cy.get('[data-test-id="product-view-creditLine"]')
            .first()
            .within(() => {
                checkNormalizedTextByTestId('product-view-pane-title-creditLine', [
                    `№${ECreditProductsDocNumbersMock.CREDIT_LINE}`,
                    ECompanyNamesMock.ALFA_LEASING,
                ]);

                checkNormalizedTextByGet('.product-debt-pane__title', [
                    'Доступно',
                    '14 020,00 ₽',
                    'из',
                    '***********,00 ₽',
                ]);

                checkNormalizedTextByGet('.pane-item', ['Просрочено', '5 670,00 ₽'], 0);
                checkNormalizedTextByGet('.pane-item', ['Задолженность', '66 351 445,75 ₽'], 1);
                checkNormalizedTextByGet(
                    '.pane-item',
                    ['Действие договора', '25.10.2022 - 31.12.2027'],
                    2,
                );

                cy.findByTestId('get-tranche__07F78L').should('contain.text', 'Новый транш');
                cy.get('.info-pane__picker-button').should('be.visible').click();
            });
    });

    it('Проверка гарантии', () => {
        cy.get('[data-test-id="product-view-guaranty"]')
            .first()
            .within(() => {
                checkNormalizedTextByTestId('product-view-pane-title-guaranty', [
                    `№${ECreditProductsDocNumbersMock.GUARANTY}`,
                    ECompanyNamesMock.ALFA_LEASING,
                ]);

                checkNormalizedTextByGet('.product-debt-pane__title', [
                    'Предел обязательств',
                    '***********,00 ₽',
                ]);

                checkNormalizedTextByGet('.pane-item', ['Бенефициар', 'OOO "Компания'], 0);
                checkNormalizedTextByGet('.pane-item', ['Действие гарантии', 'до 31.12.2027'], 1);

                cy.findByTestId('get-guarantee__03BW4Q').should('contain.text', 'Новая гарантия');
                cy.get('.info-pane__picker-button').should('be.visible').click();
            });
    });

    it('Проверка гарантийной линии', () => {
        cy.get('[data-test-id="product-view-guarantyLine"]')
            .first()
            .within(() => {
                checkNormalizedTextByTestId('product-view-pane-title-guarantyLine', [
                    `№${ECreditProductsDocNumbersMock.GUARANTY_LINE}`,
                    ECompanyNamesMock.ALFA_LEASING,
                ]);

                checkNormalizedTextByGet('.product-debt-pane__title', [
                    'Доступно',
                    '14 020,00 ₽',
                    'из',
                    '***********,00 ₽',
                ]);

                checkNormalizedTextByGet(
                    '.pane-item',
                    ['Действие договора', '25.10.2022 - 31.12.2027'],
                    0,
                );

                cy.findByTestId('get-guarantee__0RGZ6R').should('contain.text', 'Новая гарантия');
                cy.get('.info-pane__picker-button').should('be.visible').click();
            });
    });

    it('Проверка рамочного договора (СОПУК)', () => {
        cy.get('[data-test-id="product-view-sopuk"]')
            .first()
            .within(() => {
                checkNormalizedTextByTestId('product-view-pane-title-sopuk', [
                    `№${ECreditProductsDocNumbersMock.SOPUK}`,
                    ECompanyNamesMock.ALFA_LEASING,
                ]);

                checkNormalizedTextByGet('.sopuk-pane-mmb__title', ['Использовано']);
                checkNormalizedTextByTestId('product-view-sopuk-debt-pane', [
                    '***********,00 ₽',
                    '***********,00 €',
                    '***********,00 $',
                    '***********,00 Ұ',
                ]);

                checkNormalizedTextByGet('.pane-item', [
                    'Действие договора',
                    '25.10.2022 - 31.12.2027',
                ]);

                cy.findByTestId('get-credit__1543').should('contain.text', 'Новый кредит');
                cy.get('.info-pane__picker-button').should('be.visible').click();
            });
    });

    it('Проверка овердрафта', () => {
        cy.get('[data-test-id="product-view-overdraft"]')
            .contains(`№${ECreditProductsDocNumbersMock.OVERDRAFT}`)
            .parent()
            .parent()
            .within(() => {
                checkNormalizedTextByTestId('product-view-pane-title-overdraft', [
                    `№${ECreditProductsDocNumbersMock.OVERDRAFT}`,
                    ECompanyNamesMock.GOOGLE,
                ]);

                checkNormalizedTextByGet('.product-debt-pane__title', [
                    'Доступный овердрафт на счете ••',
                    '14 020,00 ₽',
                    'из',
                    '***********,00 ₽',
                ]);

                checkNormalizedTextByGet('.pane-item', ['Просрочено', '3 351 445,75 ₽'], 0);
                checkNormalizedTextByGet('.pane-item', ['Задолженность', '66 351 445,75 ₽'], 1);
                checkNormalizedTextByGet(
                    '.pane-item',
                    ['Действие договора', '25.10.2022 - 31.12.2027'],
                    2,
                );

                cy.findByTestId(`get-tranche__${ECreditProductsDocNumbersMock.OVERDRAFT}`).should(
                    'contain.text',
                    'Новый транш',
                );
                cy.get('.info-pane__picker-button').should('be.visible').click();
            });
    });

    it('Отлагательные условия', () => {
        cy.findAllByTestId('suspensive-conditions-status')
            .eq(1)
            .should('be.visible')
            .and('contain.text', 'Приближается срок выполнения отлагательного условия.');

        cy.findAllByTestId('suspensive-conditions-status')
            .eq(0)
            .should('be.visible')
            .and('contain.text', 'Нарушен срок выполнения отлагательного условия.')
            .click();

        cy.findAllByText(`Кредит №${ECreditProductsDocNumbersMock.BUSINESS_CREDIT}`);
    });
});
