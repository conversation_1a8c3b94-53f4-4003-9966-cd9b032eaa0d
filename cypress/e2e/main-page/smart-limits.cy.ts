import { NBSP } from '../../../src/constants/unicode-symbols';
import { ECompanyNamesMock } from '../../../src/server/mocks/data/constants';
import { checkManagerContacts } from '../../utils/interface-checkers';
import { goToCompany } from '../../utils/navigation-helpers';

describe('Умный виджет лимитов', () => {
    it('Проверка виджета лимитов c КпН статусом', () => {
        goToCompany({ companyName: ECompanyNamesMock.ALFA_LEASING });

        cy.findByTestId('smart-limits-container').should('be.visible');
        cy.findByTestId('smart-limits-icon-container').should('be.visible');
        cy.findByTestId('smart-limits-limit-container').should('be.visible');
        cy.findByTestId('smart-limits-kpn-status-container').should('be.visible');
        cy.findByTestId('smart-limits-buttons-container').should('be.visible');
    });

    it('Проверка модального окна "Связаться с менеджером"', () => {
        goToCompany({ companyName: ECompanyNamesMock.ALFA_LEASING });

        cy.findByTestId('smart-limits-container').should('be.visible');

        cy.findByTestId('smart-limits-main-button')
            .should('be.visible')
            .and('include.text', 'Связаться с менеджером')
            .click();

        checkManagerContacts();
    });

    it('Проверка модального окна "Пересчитать лимит"', () => {
        goToCompany({ companyName: ECompanyNamesMock.ALFA_LEASING });

        cy.findByTestId('smart-limits-container').should('be.visible');

        cy.findByTestId('smart-limits-second-picker-button')
            .should('be.visible')
            .click()
            .then(() => {
                cy.findAllByTestId('smart-limits-second-picker-button-option')
                    .first()
                    .should('be.visible')
                    .and('contain.text', 'Пересчитать лимит')
                    .click();
            });

        cy.contains('Пересчитать лимит?').should('be.visible');
        cy.contains(
            'Придётся заново подавать заявку. Новый лимит может быть меньше, чем действующий',
        ).should('be.visible');

        cy.contains('button', 'Пересчитать').should('be.visible');
        cy.contains('button', 'Отменить').should('be.visible');

        cy.contains('button', 'Пересчитать').click();
        cy.url().should('include', '/creditsb?utm_source=button8');
    });

    it('Проверка модального окна "Узнать о лимите" у УТК', () => {
        goToCompany({ companyName: ECompanyNamesMock.ALFA_LEASING });

        cy.findByTestId('smart-limits-container').should('be.visible');

        cy.findByTestId('smart-limits-second-picker-button')
            .should('be.visible')
            .click()
            .then(() => {
                cy.findAllByTestId('smart-limits-second-picker-button-option')
                    .last()
                    .should('be.visible')
                    .and('contain.text', 'Узнать о лимите')
                    .click();
            });

        cy.contains('h1', 'Кредитный лимит').should('be.visible');
        cy.contains('span', ECompanyNamesMock.ALFA_LEASING).should('be.visible');

        cy.contains('h2', 'Условия и требования').should('be.visible');
        cy.contains('span', 'Обороты').should('be.visible');
        cy.contains('span', 'Финансовый ковенант').should('be.visible');

        cy.contains('h2', 'Одобрено').should('be.visible');
        cy.contains('span', 'Дата решения: 3 июля 2024 года').should('be.visible');
    });

    it('Проверка модального окна "Узнать о лимите" не у УТК', () => {
        goToCompany({ companyName: ECompanyNamesMock.ACE_MILK });

        cy.findByTestId('smart-limits-container').should('be.visible');

        cy.findByTestId('smart-limits-second-picker-button')
            .should('be.visible')
            .click()
            .then(() => {
                cy.findAllByTestId('smart-limits-second-picker-button-option')
                    .last()
                    .should('be.visible')
                    .and('contain.text', 'Узнать о лимите')
                    .click();
            });

        cy.contains('h1', 'Кредитный лимит').should('be.visible');
        cy.contains('span', ECompanyNamesMock.ACE_MILK).should('be.visible');

        cy.contains('h2', 'Одобрено').should('be.visible');
        cy.contains('span', 'Дата решения: 3 июля 2024 года').should('be.visible');

        cy.contains('h2', 'Контакты менеджера').should('be.visible');
        cy.get('.smart-limits-drawer__plate').within(() => {
            checkManagerContacts();
        });
    });

    it('Проверка виджета лимитов без лимита', () => {
        goToCompany({ companyName: ECompanyNamesMock.GOOGLE });

        cy.findByTestId('smart-limits-container').should('exist');

        cy.findByTestId('smart-limits-container').should('be.visible');
        cy.findByTestId('smart-limits-icon-container').should('be.visible');
        cy.findByTestId('smart-limits-texts-container')
            .should('be.visible')
            .within(() => {
                cy.findByTestId('smart-limits-title')
                    .should('be.visible')
                    .and('contain.text', 'Требуются данные');
                cy.findByTestId('smart-limits-subtitle')
                    .should('be.visible')
                    .and(
                        'contain.text',
                        `Создайте новую заявку и${NBSP}приложите финансовые документы компании`,
                    );
            });
        cy.findByTestId('smart-limits-buttons-container').should('be.visible');

        cy.findByTestId('smart-limits-main-button').contains('button', 'Создать заявку').click();
        cy.url().should('include', '/creditsb?utm_source=button8');
    });

    it('Проверка отсутствия виджета лимитов в ИП', () => {
        goToCompany({ companyName: ECompanyNamesMock.EMPTY });

        cy.findByTestId('smart-limits-container').should('not.exist');
    });
});
