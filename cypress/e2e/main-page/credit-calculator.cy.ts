import { ECompanyNamesMock } from '../../../src/server/mocks/data/constants';
import { goToCompany, goToCreditOffers } from '../../utils/navigation-helpers';

describe('Кредитный калькулятор', () => {
    it('Проверка кредитного калькулятора', () => {
        goToCompany({ companyName: ECompanyNamesMock.EMPTY });
        goToCreditOffers();

        cy.get('.main-page__credit-calculator')
            .should('be.visible')
            .find('label')
            .each((element, index) => {
                const expectedTexts = [
                    'Все цели',
                    'Покупка имущества и оборудования, ремонт',
                    'Текущие расходы, пополнение оборота',
                ];

                cy.wrap(element)
                    .should('be.visible')
                    .and('include.text', expectedTexts[index])
                    .click();
            });

        cy.get('.main-page__credit-calculator')
            .find('input')
            .last()
            .scrollIntoView()
            .clear()
            .type('3500000')
            .type('{enter}')
            .clear()
            .type('50000000')
            .type('{enter}')
            .clear()
            .type('130000000')
            .type('{enter}');

        cy.findByText('Сбросить фильтр').click();
        cy.findByText(
            'Расчёты являются предварительными. Предложение не является публичной офертой.',
        ).should('be.visible');

        cy.get('.main-page__credit-offers-container').within(() => {
            cy.get('.main-page__credit-offer').first().should('be.visible');
        });
    });
});
