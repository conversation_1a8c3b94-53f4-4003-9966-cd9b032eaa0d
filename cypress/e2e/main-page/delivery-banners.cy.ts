import { ECompanyNamesMock } from '#/src/server/mocks/data/constants';

import { goToCompany } from '../../utils/navigation-helpers';
import {
    ACTIVE_CREDIT_CASE_WITH_APPOINTMENT_LOCKED_MOCK,
    ACTIVE_CREDIT_CASE_WITH_APPOINTMENT_MOCK,
    ACTIVE_CREDIT_CASE_WITH_CANCELLED_MOCK,
    ACTIVE_CREDIT_CASE_WITH_COMPLETED_MOCK,
} from '../delivery-mocks';

describe('Проверка корректности отображения статусных баннеров в доставке', () => {
    it('должен отображать баннер с назначенной встречей (можно перенести)', () => {
        cy.intercept('POST', '**/getActiveCreditCase', ACTIVE_CREDIT_CASE_WITH_APPOINTMENT_MOCK).as(
            'getActiveCreditCase',
        );

        goToCompany({ companyName: ECompanyNamesMock.EMPTY });

        cy.wait('@getActiveCreditCase');

        cy.get('[data-test-id="delivery-banner"]')
            .should('be.visible')
            .and('contain', 'Встреча с сотрудником банка назначена')
            .and('contain', 'Документы привезут')
            .and(
                'contain',
                'по адресу: 603009, Нижегородская обл, г Нижний Новгород, пр-кт Гагарина, д 35Д',
            );

        cy.get('[data-test-id="delivery-banner"]')
            .find('button')
            .should('be.visible')
            .and('contain', 'Перенести или отменить');
    });

    it('должен отображать баннер с назначенной встречей (можно только отменить)', () => {
        cy.intercept(
            'POST',
            '**/getActiveCreditCase',
            ACTIVE_CREDIT_CASE_WITH_APPOINTMENT_LOCKED_MOCK,
        ).as('getActiveCreditCase');

        goToCompany({ companyName: ECompanyNamesMock.EMPTY });

        cy.wait('@getActiveCreditCase');

        cy.get('[data-test-id="delivery-banner"]')
            .should('be.visible')
            .and('contain', 'Встреча с сотрудником банка назначена')
            .and('contain', 'Документы привезут')
            .and(
                'contain',
                'по адресу: 603009, Нижегородская обл, г Нижний Новгород, пр-кт Гагарина, д 35Д',
            );

        cy.get('[data-test-id="delivery-banner"]')
            .find('button')
            .should('be.visible')
            .and('contain', 'Отменить встречу');
    });

    it('должен отображать баннер с отменённой встречей', () => {
        cy.intercept('POST', '**/getActiveCreditCase', ACTIVE_CREDIT_CASE_WITH_CANCELLED_MOCK).as(
            'getActiveCreditCase',
        );

        goToCompany({ companyName: ECompanyNamesMock.EMPTY });

        cy.wait('@getActiveCreditCase');

        cy.get('[data-test-id="delivery-banner"]')
            .should('be.visible')
            .and('contain', 'Доставка документов по кредиту для бизнеса отменена')
            .and('contain', 'Чтобы получить кредит, назначьте новую встречу и подпишите документы');

        cy.get('[data-test-id="delivery-banner"]')
            .find('button')
            .should('be.visible')
            .and('contain', 'Назначить новую встречу');
    });

    it('должен отображать баннер с отправленной заявкой', () => {
        cy.intercept('POST', '**/getActiveCreditCase', ACTIVE_CREDIT_CASE_WITH_COMPLETED_MOCK).as(
            'getActiveCreditCase',
        );

        goToCompany({ companyName: ECompanyNamesMock.EMPTY });

        cy.wait('@getActiveCreditCase');

        cy.get('[data-test-id="delivery-banner"]')
            .should('be.visible')
            .and('contain', 'Заявка на кредит для бизнеса отправлена')
            .and(
                'contain',
                'Всё проверим за 1–2 рабочих дня. Как будет готово решение — деньги зачислятся на ваш счёт',
            );

        cy.get('[data-test-id="delivery-banner"]').find('button').should('not.exist');
    });
});
