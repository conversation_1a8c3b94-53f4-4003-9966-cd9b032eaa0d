// eslint-disable-next-line import/no-extraneous-dependencies
import { configure } from '@testing-library/cypress';

import './commands';

configure({ testIdAttribute: 'data-test-id' });

Cypress.on('window:before:load', (window: Cypress.AUTWindow & { sp: () => void }) => {
    // eslint-disable-next-line no-param-reassign
    window.sp = cy.stub().as('sp');
});

// Игнорируем ResizeObserver ошибку
Cypress.on(
    'uncaught:exception',
    (err) => !err.message.includes('ResizeObserver loop completed with undelivered notifications'),
);

Cypress.on(
    'uncaught:exception',
    (err) =>
        !err.message.includes(
            'Hydration failed because the initial UI does not match what was rendered on the server.',
        ),
);

Cypress.on(
    'uncaught:exception',
    (err) =>
        !err.message.includes(
            'There was an error while hydrating. Because the error happened outside of a Suspense boundary, the entire root will switch to client rendering.',
        ),
);
