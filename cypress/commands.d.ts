// eslint-disable-next-line spaced-comment
/// <reference types="cypress" />

declare namespace Cypress {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    interface Chainable {
        mockThriftOnce(serviceName: string, method: string, response: unknown): Chainable;
        mockThriftErrorOnce(serviceName: string, method: string, error: unknown): Chainable;
        clearThriftMocks(): Chainable;
        before(cssProperty: string): Chainable;
        getByTestId(testId: string): Chainable;
        getLocalStorage(key: string): Chainable;
        setLocalStorage<T>(key: string, value: T): Chainable;
        removeLocalStorage(key: string): Chainable;
        matchImageSnapshot(key: string, options?: unknown): Chainable;
        waitBeforeScreenshot(time: number): Chainable;
    }

    interface Window {
        __corp_nib_event_bus: {
            dispatchEvent(event: string, data: unknown): void;
            lastEventValues: Record<string, unknown>;
        };
    }
}
