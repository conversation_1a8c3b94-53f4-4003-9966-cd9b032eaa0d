{"extends": "arui-scripts/tsconfig.json", "compilerOptions": {"esModuleInterop": true, "resolveJsonModule": true, "types": ["node", "cypress", "@testing-library/cypress"], "lib": ["ES2019", "DOM"], "module": "commonjs", "target": "ES2019", "jsx": "react", "allowJs": true, "allowSyntheticDefaultImports": true, "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "paths": {"#/src/*": ["../src/*"]}}, "include": ["./**/*", "cypress/constants/", "cypress/utils/"]}