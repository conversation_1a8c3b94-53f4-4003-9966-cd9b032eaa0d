import { type PackageSettings } from 'arui-scripts';

const settings: PackageSettings = {
    presets: 'arui-scripts-corporate-presets',
    baseDockerImage: 'docker-hub.binary.alfabank.ru/alfabankui/arui-scripts:18.20.0-slim',
    dockerRegistry: 'corporate-docker-snapshots.binary.alfabank.ru',
    runFromNonRootUser: true,
    useServerHMR: true,
    keepCssVars: false,
    componentsTheme: './node_modules/@alfalab/core-components/themes/corp.css',
    // Оптимизируем работу для cypress в headless режиме
    devSourceMaps: process.env.DISABLE_SOURCEMAPS ? false : undefined, // undefined - чтобы бралось дефолтное значение
};

export default settings;
