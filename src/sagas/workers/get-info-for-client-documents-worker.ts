import { call, put, select } from 'redux-saga/effects';

import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import {
    getActiveCreditCaseStart,
    getAttachedDocuments,
    getInfoForClientDocumentsError,
    getInfoForClientDocumentsFinish,
    setClientDocumentsRequestStatus,
    setInfoForClientDocuments,
} from '#/src/ducks/attach-documents/actions';
import {
    loanMBIdSelector,
    stageActiveCreditCaseSelector,
} from '#/src/ducks/attach-documents/selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { CLIENT_DOCUMENTS_ATTACH_METRICS } from '#/src/metrics';
import { EClientDocumentsRequestStatuses } from '#/src/types/client-documents';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

import { getActiveCreditCaseWorker } from './get-active-credit-case-worker';

export function* getInfoForClientDocumentsWorker() {
    try {
        yield call(getActiveCreditCaseWorker, getActiveCreditCaseStart());

        const stageActiveCreditCase: ReturnType<typeof stageActiveCreditCaseSelector> =
            yield select(stageActiveCreditCaseSelector);

        if (stageActiveCreditCase) {
            const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
                currentOrganizationEqIdSelector,
            );
            const loanMBId: ReturnType<typeof loanMBIdSelector> = yield select(loanMBIdSelector);

            const infoForClientDocuments: ThenArg<typeof fetchers.getInfoForClientDocuments> =
                yield call(fetchers.getInfoForClientDocuments, { organizationId, loanMBId });

            yield put(setInfoForClientDocuments(infoForClientDocuments));

            const borrowerCode = infoForClientDocuments?.borrowerCode || '';

            yield call(getRequestStatus, { organizationId, loanMBId, borrowerCode });
        }

        yield put(getInfoForClientDocumentsFinish());
    } catch (error) {
        yield put(getInfoForClientDocumentsError(new ServerResponseError(), LOG_LEVEL.ERROR));
    }
}

function* getRequestStatus({
    organizationId,
    loanMBId,
    borrowerCode,
}: {
    organizationId: string;
    loanMBId: string;
    borrowerCode: string;
}) {
    try {
        const status: ThenArg<typeof fetchers.getRequestStatus> = yield call(
            fetchers.getRequestStatus,
            { organizationId, loanMBId, borrowerCode },
        );

        if (status === EClientDocumentsRequestStatuses.INIT) {
            yield put(getAttachedDocuments({ organizationId, loanMBId, borrowerCode }));
        }

        yield put(
            trackAlfaMetrics(CLIENT_DOCUMENTS_ATTACH_METRICS.getClientDocumentsStatus, {
                loanMBId,
                borrowerCode,
                status,
            }),
        );

        yield put(setClientDocumentsRequestStatus(status as EClientDocumentsRequestStatuses));
    } catch {
        yield put(getInfoForClientDocumentsError(new ServerResponseError(), LOG_LEVEL.ERROR));
    }
}
