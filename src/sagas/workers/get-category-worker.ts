import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import {
    getCategoryError,
    getCategoryFinish,
    type getCategoryStart,
} from '#/src/ducks/organization/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { type ThenArg } from '#/src/utils/promise-helper';

import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';

export function* getCategoryWorker(action: ReturnType<typeof getCategoryStart>) {
    const organizationId: string = yield select(currentOrganizationEqIdSelector);

    try {
        const category: ThenArg<typeof fetchers.mksCustomers.getCategory> = yield call(
            fetchers.mksCustomers.getCategory,
            {
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: action.organizationId ?? organizationId,
                },
                urlParams: { customerId: action.organizationId ?? organizationId },
            },
        );

        yield put(getCategoryFinish(category));
    } catch (error) {
        yield put(getCategoryError(new ServerResponseError(getErrorMessage(error))));
    }
}
