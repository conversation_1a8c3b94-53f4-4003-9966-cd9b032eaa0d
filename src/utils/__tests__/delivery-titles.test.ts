import { ECreditOffers } from '#/src/constants/credit-offers';
import { DeliveryDocumentStatus } from '#/src/ducks/credit-processing/types';

import { getDeliveryTitle } from '../delivery-titles';

describe('getDeliveryTitle', () => {
    it('должен возвращать длинный и короткий заголовок для встречи', () => {
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.BUSINESS_CREDIT,
                deliveryStatus: DeliveryDocumentStatus.IN_PROGRESS,
                titleKind: 'full',
            }),
        ).toBe('Встреча с сотрудником банка назначена');
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.BUSINESS_CREDIT,
                deliveryStatus: DeliveryDocumentStatus.IN_PROGRESS,
                titleKind: 'short',
            }),
        ).toBe('Встреча назначена');
    });

    it('должен возвращает правильный заголовок для CANCELLED (Карта Альфа-Бизнес Кредит)', () => {
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
                deliveryStatus: DeliveryDocumentStatus.CANCELLED,
                titleKind: 'full',
            }),
        ).toBe('Доставка документов по карте Альфа-Бизнес Кредит отменена');
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
                deliveryStatus: DeliveryDocumentStatus.CANCELLED,
                titleKind: 'short',
            }),
        ).toBe('Доставка документов отменена');
    });

    it('должен возвращать правильный заголовок для CANCELLED (Кредит для бизнеса)', () => {
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.BUSINESS_CREDIT,
                deliveryStatus: DeliveryDocumentStatus.CANCELLED,
                titleKind: 'full',
            }),
        ).toBe('Доставка документов по кредиту для бизнеса отменена');
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.BUSINESS_CREDIT,
                deliveryStatus: DeliveryDocumentStatus.CANCELLED,
                titleKind: 'short',
            }),
        ).toBe('Доставка документов отменена');
    });

    it('должен возвращать правильный заголовок для CANCELLED (Возобновляемая кредитная линия)', () => {
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.REFILLABLE_CREDIT_LINE,
                deliveryStatus: DeliveryDocumentStatus.CANCELLED,
                titleKind: 'full',
            }),
        ).toBe('Доставка документов по возобновляемой кредитной линии отменена');
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.REFILLABLE_CREDIT_LINE,
                deliveryStatus: DeliveryDocumentStatus.CANCELLED,
                titleKind: 'short',
            }),
        ).toBe('Доставка документов отменена');
    });

    it('должен возвращать правильный заголовок для CANCELLED (Овердрафт)', () => {
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.OVERDRAFT,
                deliveryStatus: DeliveryDocumentStatus.CANCELLED,
                titleKind: 'full',
            }),
        ).toBe('Доставка документов по овердрафту отменена');
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.OVERDRAFT,
                deliveryStatus: DeliveryDocumentStatus.CANCELLED,
                titleKind: 'short',
            }),
        ).toBe('Доставка документов отменена');
    });

    it('должен возвращать правильный заголовок для COMPLETED (Карта Альфа-Бизнес Кредит)', () => {
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
                deliveryStatus: DeliveryDocumentStatus.COMPLETED,
                titleKind: 'full',
            }),
        ).toBe('Заявка на карту Альфа-Бизнес Кредит отправлена');
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
                deliveryStatus: DeliveryDocumentStatus.COMPLETED,
                titleKind: 'short',
            }),
        ).toBe('Заявка отправлена');
    });

    it('должен возвращать правильный заголовок для COMPLETED (Кредит для бизнеса)', () => {
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.BUSINESS_CREDIT,
                deliveryStatus: DeliveryDocumentStatus.COMPLETED,
                titleKind: 'full',
            }),
        ).toBe('Заявка на кредит для бизнеса отправлена');
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.BUSINESS_CREDIT,
                deliveryStatus: DeliveryDocumentStatus.COMPLETED,
                titleKind: 'short',
            }),
        ).toBe('Заявка отправлена');
    });

    it('должен возвращать правильный заголовок для COMPLETED (Возобновляемая кредитная линия)', () => {
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.REFILLABLE_CREDIT_LINE,
                deliveryStatus: DeliveryDocumentStatus.COMPLETED,
                titleKind: 'full',
            }),
        ).toBe('Заявка на возобновляемую кредитную линию отправлена');
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.REFILLABLE_CREDIT_LINE,
                deliveryStatus: DeliveryDocumentStatus.COMPLETED,
                titleKind: 'short',
            }),
        ).toBe('Заявка отправлена');
    });

    it('должен возвращать правильный заголовок для COMPLETED (Овердрафт)', () => {
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.OVERDRAFT,
                deliveryStatus: DeliveryDocumentStatus.COMPLETED,
                titleKind: 'full',
            }),
        ).toBe('Заявка на овердрафт отправлена');
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.OVERDRAFT,
                deliveryStatus: DeliveryDocumentStatus.COMPLETED,
                titleKind: 'short',
            }),
        ).toBe('Заявка отправлена');
    });

    it('возвращает пустую строку для неподдерживаемого статуса', () => {
        expect(
            getDeliveryTitle({
                offerType: ECreditOffers.BUSINESS_CREDIT,
                deliveryStatus: 'UNKNOWN_STATUS' as DeliveryDocumentStatus,
                titleKind: 'full',
            }),
        ).toBe('');
    });
});
