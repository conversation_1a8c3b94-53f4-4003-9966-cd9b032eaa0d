import { sanitizeUrlParameters } from '../url-parameters';

describe(sanitizeUrlParameters.name, () => {
    beforeEach(() => {
        jest.clearAllMocks();
        jest.spyOn(console, 'warn').mockImplementation(() => {});
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('должен корректно обрабатывать примитивные значения', () => {
        const params = {
            stringParam: 'test',
            numberParam: 123,
            booleanParam: true,
            undefinedParam: undefined,
            nullParam: null,
        };

        const result = sanitizeUrlParameters(params);

        expect(result).toEqual({
            stringParam: 'test',
            numberParam: '123',
            booleanParam: 'true',
        });
    });

    it('должен пропускать сложные объекты и логировать предупреждения', () => {
        const complexObject = { nested: { value: 'test' } };
        const circularObject: any = { name: 'circular' };

        circularObject.self = circularObject;

        const params = {
            validParam: 'test',
            complexParam: complexObject,
            circularParam: circularObject,
            arrayParam: [1, 2, 3],
        };

        const result = sanitizeUrlParameters(params);

        expect(result).toEqual({
            validParam: 'test',
        });

        expect(console.warn).toHaveBeenCalledTimes(3);
        expect(console.warn).toHaveBeenCalledWith(
            'Параметр пропущен при формировании URL - содержит сложный объект:',
            complexObject,
        );
        expect(console.warn).toHaveBeenCalledWith(
            'Параметр пропущен при формировании URL - содержит сложный объект:',
            circularObject,
        );
        expect(console.warn).toHaveBeenCalledWith(
            'Параметр пропущен при формировании URL - содержит сложный объект:',
            [1, 2, 3],
        );
    });

    it('должен обрабатывать пустой или null входной параметр', () => {
        expect(sanitizeUrlParameters({})).toEqual({});
        expect(sanitizeUrlParameters(null as any)).toEqual({});
        expect(sanitizeUrlParameters(undefined as any)).toEqual({});
    });

    it('должен преобразовывать числа и булевы значения в строки', () => {
        const params = {
            zero: 0,
            negative: -123,
            float: 3.14,
            trueValue: true,
            falseValue: false,
        };

        const result = sanitizeUrlParameters(params);

        expect(result).toEqual({
            zero: '0',
            negative: '-123',
            float: '3.14',
            trueValue: 'true',
            falseValue: 'false',
        });
    });
});
