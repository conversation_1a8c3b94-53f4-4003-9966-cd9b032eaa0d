/**
 * Безопасно сериализует параметры для URL, исключая циклические ссылки и сложные объекты
 * @param params - объект с параметрами
 * @returns объект с только примитивными значениями
 */
export function sanitizeUrlParameters(
    params: Record<string, unknown>,
): Record<string, string | undefined> {
    const sanitized: Record<string, string | undefined> = {};

    for (const [key, value] of Object.entries(params || {})) {
        if (value === null || value === undefined) {
            // Пропускаем null/undefined значения
        } else if (
            typeof value === 'string' ||
            typeof value === 'number' ||
            typeof value === 'boolean'
        ) {
            // Преобразуем только примитивные типы в строки
            sanitized[key] = String(value);
        } else {
            // Логируем предупреждение о пропуске сложного объекта
            // eslint-disable-next-line no-console
            console.warn(
                'Параметр пропущен при формировании URL - содержит сложный объект:',
                value,
            );
        }
    }

    return sanitized;
}
