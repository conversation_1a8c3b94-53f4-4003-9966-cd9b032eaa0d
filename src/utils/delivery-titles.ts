import { ECreditOffers } from '../constants/credit-offers';
import { DeliveryDocumentStatus } from '../ducks/credit-processing/types';

export type TitleKind = 'full' | 'short';

interface GetDeliveryTitleParams {
    offerType: ECreditOffers;
    deliveryStatus: DeliveryDocumentStatus;
    titleKind: TitleKind;
}

const MEETING_STATUSES: DeliveryDocumentStatus[] = [
    DeliveryDocumentStatus.IN_PROGRESS,
    DeliveryDocumentStatus.WAITING_FOR_DOCUMENTS_SIGNING,
    DeliveryDocumentStatus.AT_CLIENT,
];

const CANCELLED_TITLES_FULL: Partial<Record<ECreditOffers, string>> = {
    [ECreditOffers.ALFA_BUSINESS_CREDIT_CARD]:
        'Доставка документов по карте Альфа-Бизнес Кредит отменена',
    [ECreditOffers.BUSINESS_CREDIT]: 'Доставка документов по кредиту для бизнеса отменена',
    [ECreditOffers.REFILLABLE_CREDIT_LINE]:
        'Доставка документов по возобновляемой кредитной линии отменена',
    [ECreditOffers.OVERDRAFT]: 'Доставка документов по овердрафту отменена',
};

const COMPLETED_TITLES_FULL: Partial<Record<ECreditOffers, string>> = {
    [ECreditOffers.ALFA_BUSINESS_CREDIT_CARD]: 'Заявка на карту Альфа-Бизнес Кредит отправлена',
    [ECreditOffers.BUSINESS_CREDIT]: 'Заявка на кредит для бизнеса отправлена',
    [ECreditOffers.REFILLABLE_CREDIT_LINE]: 'Заявка на возобновляемую кредитную линию отправлена',
    [ECreditOffers.OVERDRAFT]: 'Заявка на овердрафт отправлена',
};

export function getDeliveryTitle({
    offerType,
    deliveryStatus,
    titleKind,
}: GetDeliveryTitleParams): string {
    const isTitleShort = titleKind === 'short';

    if (MEETING_STATUSES.includes(deliveryStatus)) {
        return isTitleShort ? 'Встреча назначена' : 'Встреча с сотрудником банка назначена';
    }

    if (deliveryStatus === DeliveryDocumentStatus.CANCELLED) {
        return isTitleShort
            ? 'Доставка документов отменена'
            : CANCELLED_TITLES_FULL[offerType] || '';
    }

    if (deliveryStatus === DeliveryDocumentStatus.COMPLETED) {
        return isTitleShort ? 'Заявка отправлена' : COMPLETED_TITLES_FULL[offerType] || '';
    }

    return '';
}
