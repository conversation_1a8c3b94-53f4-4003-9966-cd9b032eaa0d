import { useSelector } from 'react-redux';

import { useMatchMedia } from '@alfalab/core-components/mq';

import { type ECreditOffers } from '#/src/constants/credit-offers';
import { ECreditOffersProductType, type EProductCodes } from '#/src/constants/credit-products';
import {
    activeCreditCaseFromSFASelector,
    activeCreditCaseInfoSelector,
    deliveryCaseInfoData,
    productCodeSelector,
} from '#/src/ducks/attach-documents/selectors';
import { type DeliveryInfoData } from '#/src/types/delivery';
import { getDeliveryTitle, type TitleKind } from '#/src/utils/delivery-titles';

interface CreditCaseInfo {
    isEmpty: boolean;
    offerType: ECreditOffers | undefined;
    productCode: string;
    title: React.ReactNode | string;
    description: string | undefined;
    textButton: string | null;
    isDelivery: boolean;
    stage: string | undefined;
    deliveryInfoData: DeliveryInfoData | null;
    isDeliveryInProgress: boolean;
}

export const useCreditCaseInfo = (): CreditCaseInfo => {
    const [isTabletM] = useMatchMedia('--tablet-m');
    const [isDesktopM] = useMatchMedia('--desktop-m');
    const isMediumScreen = isTabletM && !isDesktopM;

    const activeCreditCaseFromSFA = useSelector(activeCreditCaseFromSFASelector);
    const activeCreditCaseInfo = useSelector(activeCreditCaseInfoSelector);
    const productCode = useSelector(productCodeSelector);
    const deliveryInfoData = useSelector(deliveryCaseInfoData);

    const offerType = ECreditOffersProductType[productCode as EProductCodes];
    const deliveryStatus = deliveryInfoData?.deliveryStatus;
    const titleKind: TitleKind = isMediumScreen ? 'short' : 'full';
    const activeCreditCaseInfoData = activeCreditCaseFromSFA?.data || activeCreditCaseInfo?.data;
    const isDelivery = deliveryInfoData?.isDelivery || false;

    const isEmpty = !activeCreditCaseFromSFA && !activeCreditCaseInfo;

    const textButton = isDelivery
        ? deliveryInfoData?.textButton || null
        : activeCreditCaseInfoData?.textButton || null;

    const title =
        isDelivery && offerType && deliveryStatus
            ? getDeliveryTitle({
                  offerType,
                  deliveryStatus,
                  titleKind,
              })
            : activeCreditCaseInfoData?.title;

    const description = isDelivery
        ? deliveryInfoData?.description
        : activeCreditCaseInfoData?.description;

    return {
        isEmpty,
        offerType,
        productCode,
        title,
        description,
        textButton,
        isDelivery,
        stage: activeCreditCaseInfo?.stage,
        deliveryInfoData,
        isDeliveryInProgress: deliveryInfoData?.isDeliveryInProgress || false,
    };
};
