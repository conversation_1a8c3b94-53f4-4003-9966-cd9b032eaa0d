/* This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/. */
@import '@alfalab/core-components/vars';

@font-face {
    font-family: 'Roboto Rouble';
    font-style: normal;
    font-weight: var(--font-weight-light);
    src: url('font/font_roboto-rouble_light.woff');
}

@font-face {
    font-family: 'Roboto Rouble';
    font-style: normal;
    font-weight: var(--font-weight-normal);
    src: url('font/font_roboto-rouble_regular.woff');
}

@font-face {
    font-family: 'Roboto Rouble';
    font-style: normal;
    font-weight: var(--font-weight-medium);
    src: url('font/font_roboto-rouble_medium.woff');
}

@font-face {
    font-family: 'Roboto Rouble';
    font-style: normal;
    font-weight: var(--font-weight-bold);
    src: url('font/font_roboto-rouble_bold.woff');
}

.grid {
    width: 100%;
    box-sizing: border-box;

    max-width: 1188px;
    margin: 0 auto;
    padding: 0 var(--gap-12);
}

@media (min-width: 375px) {
    .grid {
        padding: 0 var(--gap-16);
    }
}

@media (min-width: 412px) {
    .grid {
        padding: 0 var(--gap-20);
    }
}

@media (min-width: 768px) {
    .grid {
        padding: 0 var(--gap-32);
    }
}

@media (min-width: 1280px) {
    .grid {
        padding: 0 var(--gap-48);
    }
}

.grid__row {
    display: -webkit-box;
    display: flex;
    -webkit-box-flex: 0;
    flex: 0 1 auto;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    flex-direction: row;
    flex-wrap: wrap;

    margin: 0 auto;

    box-sizing: border-box;

    margin-left: var(--gap-8-neg);
    margin-right: var(--gap-8-neg);
}

@media (min-width: 1280px) {
    .grid__row {
        margin-left: var(--gap-12-neg);
        margin-right: var(--gap-12-neg);
    }
}

.grid__col {
    -webkit-box-flex: 1;
    flex-grow: 1;
    flex-basis: 0;
    max-width: 100%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__col-1,
.grid__col-1,
.grid__offset-1 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-1 {
    flex-basis: 8.33333%;
    max-width: 8.33333%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-1 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-1 {
    margin-left: 8.33333%;
}

.grid__col-2,
.grid__col-2,
.grid__offset-2 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-2 {
    flex-basis: 16.66666%;
    max-width: 16.66666%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-2 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-2 {
    margin-left: 16.66666%;
}

.grid__col-3,
.grid__col-3,
.grid__offset-3 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-3 {
    flex-basis: 24.99999%;
    max-width: 24.99999%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-3 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-3 {
    margin-left: 24.99999%;
}

.grid__col-4,
.grid__col-4,
.grid__offset-4 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-4 {
    flex-basis: 33.33332%;
    max-width: 33.33332%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-4 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-4 {
    margin-left: 33.33332%;
}

.grid__col-5,
.grid__col-5,
.grid__offset-5 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-5 {
    flex-basis: 41.66665%;
    max-width: 41.66665%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-5 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-5 {
    margin-left: 41.66665%;
}

.grid__col-6,
.grid__col-6,
.grid__offset-6 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-6 {
    flex-basis: 49.99998%;
    max-width: 49.99998%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-6 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-6 {
    margin-left: 49.99998%;
}

.grid__col-7,
.grid__col-7,
.grid__offset-7 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-7 {
    flex-basis: 58.33331%;
    max-width: 58.33331%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-7 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-7 {
    margin-left: 58.33331%;
}

.grid__col-8,
.grid__col-8,
.grid__offset-8 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-8 {
    flex-basis: 66.66664%;
    max-width: 66.66664%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-8 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-8 {
    margin-left: 66.66664%;
}

.grid__col-9,
.grid__col-9,
.grid__offset-9 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-9 {
    flex-basis: 74.99997%;
    max-width: 74.99997%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-9 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-9 {
    margin-left: 74.99997%;
}

.grid__col-10,
.grid__col-10,
.grid__offset-10 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-10 {
    flex-basis: 83.3333%;
    max-width: 83.3333%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-10 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-10 {
    margin-left: 83.3333%;
}

.grid__col-11,
.grid__col-11,
.grid__offset-11 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-11 {
    flex-basis: 91.66663%;
    max-width: 91.66663%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-11 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-11 {
    margin-left: 91.66663%;
}

.grid__col-12,
.grid__col-12,
.grid__offset-12 {
    -webkit-box-flex: 0;
    flex: 0 0 auto;

    box-sizing: border-box;
}

.grid__col-12 {
    flex-basis: 99.99996%;
    max-width: 99.99996%;

    padding-left: var(--gap-8);
    padding-right: var(--gap-8);
}

@media (min-width: 1280px) {
    .grid__col-12 {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }
}

.grid__offset-12 {
    margin-left: 99.99996%;
}

*,
*:before,
*:after {
    box-sizing: border-box;
}

html,
body {
    margin: 0;
    padding: 0;
    height: 100%;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: var(--color-light-text-primary);
    font-family:
        system-ui,
        -apple-system,
        'Segoe UI',
        Roboto,
        Oxygen,
        Ubuntu,
        Cantarell,
        'Open Sans',
        'Helvetica Neue',
        'Roboto Rouble',
        sans-serif;
    font-size: var(--font-size-m);
    font-style: normal;
    font-weight: var(--font-weight-normal);
    letter-spacing: 0;
    background-color: var(--color-light-base-bg-primary);

    @media (--tablet-m) {
        background-color: var(--color-light-bg-secondary);
    }
}

#react-app {
    display: table;
    table-layout: fixed;
    width: 100%;
    height: inherit;
}
