import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    renderDigitalSalesSpecialOffersCreditSlider: {
        category: EventCategory.startPage,
        action: 'Render > Digital-Sales-Special-Offers-Credit-Slider',
        label: 'Отрисовка слайдера кредитных предложений',
        property: null,
        value: null,
        dimensionsMapping: {},
    },
    renderDigitalSalesCreditOffersBanners: {
        category: EventCategory.startPage,
        action: 'Render > Digital-Sales-Credit-Offers-Banners',
        label: 'Отрисовка баннера кредитных предложений',
        property: null,
        value: null,
        dimensionsMapping: {},
    },
} as Record<string, TMetric>;
