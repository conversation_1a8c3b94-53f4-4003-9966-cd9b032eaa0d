import {
    type MksAuthConfig,
    type OpenApiProjectConfig,
    type OpenApiSystemHeadersGetter,
} from 'corporate-services/lib/openapi/open-api-system-headers';

import { remoteServerImitation } from './mock-request';

/**
 * Мок токен для разработки
 */
const MOCK_ACCESS_TOKEN = 'mock-access-token-for-development';

/**
 * Мок refresh токен для разработки
 */
const MOCK_REFRESH_TOKEN = 'mock-refresh-token-for-development';

/**
 * Время жизни мок токена (в секундах)
 */
const MOCK_TOKEN_EXPIRES_IN = 3600;

/**
 * Создает мок ответ для OAuth токена
 */
export function createMockOAuthTokenResponse() {
    return remoteServerImitation({
        access_token: MOCK_ACCESS_TOKEN,
        refresh_token: MOCK_REFRESH_TOKEN,
        expires_in: MOCK_TOKEN_EXPIRES_IN,
        token_type: 'Bearer',
        scope: 'openid profile',
    });
}

/**
 * Создает мок ответ для introspection запроса
 */
export function createMockOAuthIntrospectionResponse() {
    return remoteServerImitation({
        active: true,
        client_id: 'nib-corp-credit-products-ui',
        username: 'mock-user',
        scope: 'openid profile',
        exp: Math.floor(Date.now() / 1000) + 3600,
        iat: Math.floor(Date.now() / 1000),
        sub: 'mock-user-id',
        aud: ['nib-corp-credit-products-ui'],
        iss: 'mock-oauth-server',
        token_type: 'Bearer',
    });
}

/**
 * Создает мок ответ для logout запроса
 */
export function createMockOAuthLogoutResponse() {
    return remoteServerImitation({ success: true });
}

export function createMockMksHeadersGetter(
    projectConfig: OpenApiProjectConfig,
    _mksAuthConfig: MksAuthConfig,
): OpenApiSystemHeadersGetter {
    return () =>
        remoteServerImitation({
            Authorization: `Bearer ${MOCK_ACCESS_TOKEN}`,
            'Content-Type': 'application/json',
            'X-Project-Id': projectConfig.projectId || '',
            'X-Channel-Id': projectConfig.channelId || '',
            'X-Client-Type': projectConfig.clientType || '',
        });
}
