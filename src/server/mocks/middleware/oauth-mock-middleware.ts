import { type Request, type ResponseToolkit, type Server } from '@hapi/hapi';

import { getOAuthMockHandler, isOAuthEndpoint } from '#/src/server/mocks/configs/mock-config-oauth';

/**
 * Middleware для мокирования OAuth запросов в режиме разработки
 * Перехватывает OAuth запросы и возвращает мок ответы
 */
export function createOAuthMockMiddleware() {
    return {
        name: 'oauth-mock-middleware',
        version: '1.0.0',
        register: async (server: Server) => {
            server.ext('onRequest', async (request: Request, reply: ResponseToolkit) => {
                if (isOAuthEndpoint(request.url.pathname)) {
                    const mockHandler = getOAuthMockHandler(request.url.pathname);

                    if (mockHandler) {
                        try {
                            return await mockHandler(request, reply);
                        } catch (error) {
                            return reply
                                .response({
                                    error: 'internal_server_error',
                                    error_description: 'Mock OAuth server error',
                                })
                                .type('application/json')
                                .code(500);
                        }
                    }
                }

                return reply.continue;
            });
        },
    };
}
