import { type Request, type ResponseObject, type ResponseToolkit } from '@hapi/hapi';

import {
    createMockOAuthIntrospectionResponse,
    createMockOAuthLogoutResponse,
    createMockOAuthTokenResponse,
} from '#/src/server/mocks/utils/mock-oauth';

/**
 * Тип для OAuth handler функции
 */
type OAuthHandler = (request: Request, reply: ResponseToolkit) => Promise<ResponseObject>;

/**
 * Список OAuth endpoint'ов, которые должны быть замоканы
 */
export const OAUTH_ENDPOINTS_TO_MOCK = [
    '/protocol/openid-connect/token',
    '/protocol/openid-connect/token/refresh',
    '/protocol/openid-connect/token/introspect',
    '/protocol/openid-connect/logout',
    '/oauth/token',
    '/oauth/token/refresh',
    '/oauth/introspect',
    '/oauth/logout',
];

/**
 * Мок конфигурация для OAuth endpoint'ов
 * Используется для перехвата и мокирования OAuth запросов в режиме разработки
 */
export const mockConfigOAuth = {
    /**
     * Мок endpoint для получения OAuth токена
     * Имитирует стандартный OAuth 2.0 token endpoint
     */
    '/protocol/openid-connect/token': {
        method: 'POST',
        handler: async (_request: Request, reply: ResponseToolkit) => {
            const tokenResponse = await createMockOAuthTokenResponse();

            return reply.response(tokenResponse).type('application/json');
        },
    },

    /**
     * Мок endpoint для обновления токена
     */
    '/protocol/openid-connect/token/refresh': {
        method: 'POST',
        handler: async (_request: Request, reply: ResponseToolkit) => {
            const tokenResponse = await createMockOAuthTokenResponse();

            return reply.response(tokenResponse).type('application/json');
        },
    },

    /**
     * Мок endpoint для получения информации о токене
     */
    '/protocol/openid-connect/token/introspect': {
        method: 'POST',
        handler: async (_request: Request, reply: ResponseToolkit) => {
            const introspectionResponse = await createMockOAuthIntrospectionResponse();

            return reply.response(introspectionResponse).type('application/json');
        },
    },

    /**
     * Мок endpoint для отзыва токена
     */
    '/protocol/openid-connect/logout': {
        method: 'POST',
        handler: async (_request: Request, reply: ResponseToolkit) => {
            const response = await createMockOAuthLogoutResponse();

            return reply.response(response).type('application/json');
        },
    },
};

/**
 * Проверяет, является ли URL OAuth endpoint'ом
 */
export function isOAuthEndpoint(url: string): boolean {
    return OAUTH_ENDPOINTS_TO_MOCK.some((endpoint) => url.includes(endpoint));
}

/**
 * Получает мок handler для OAuth endpoint'а
 */
export function getOAuthMockHandler(url: string): OAuthHandler | null {
    for (const [endpoint, config] of Object.entries(mockConfigOAuth)) {
        if (url.includes(endpoint)) {
            return config.handler;
        }
    }

    return null;
}
