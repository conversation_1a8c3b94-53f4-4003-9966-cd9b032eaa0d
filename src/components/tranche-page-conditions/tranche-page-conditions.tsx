import React from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type Amount as TAmount } from 'thrift-services/entities';
import { type UnixEpoch } from 'thrift-services/utils';

import { useMatchMedia } from '@alfalab/core-components/mq';
import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';

import { currentTimeSelector } from '#/src/ducks/settings/selectors';

import { DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';
import AmountPure from '../ui/amount-pure';

import './tranche-page-conditions.css';

const cn = createCn('tranche-page-conditions');

type TOwnProps = {
    sum?: TAmount;
    fromDate?: UnixEpoch;
    toDate?: UnixEpoch;
    debtRate?: number;
    docNumber?: string;
    debtRateDaily?: number | null;
};

type TProps = TOwnProps;

export const TranchePageConditions: React.FC<TProps> = ({
    sum,
    toDate,
    fromDate,
    debtRate,
    debtRateDaily,
}) => {
    const currentTime = useSelector(currentTimeSelector);
    const [isTablet] = useMatchMedia('--tablet-m');

    return (
        <BackgroundPlate
            view={isTablet ? BackgroundPlateView.Primary : BackgroundPlateView.Secondary}
            className={cn('conditions-container')}
            data-test-id='conditions-pane'
        >
            <div className={cn('condition-item')} data-test-id='sum'>
                <Typography.Text className={cn('text')} view='component' color='secondary'>
                    Сумма
                </Typography.Text>
                <Typography.Text tag='div' view='primary-large' weight='medium'>
                    <AmountPure value={sum} transparentMinor={false} />
                </Typography.Text>
            </div>
            <div className={cn('condition-item')} data-test-id='rate'>
                <Typography.Text className={cn('text')} view='component' color='secondary'>
                    Ставка
                </Typography.Text>
                <Typography.Text tag='div' view='primary-large' weight='medium'>
                    {debtRateDaily ? `${debtRateDaily}% в день` : `${debtRate}% годовых`}
                </Typography.Text>
            </div>
            <div className={cn('condition-item')} data-test-id='term'>
                <Typography.Text className={cn('text')} view='component' color='secondary'>
                    Действие договора
                </Typography.Text>
                <Typography.Text view='primary-large' weight='medium' tag='div'>
                    {dateToCustomFormat(currentTime, fromDate, DATE_FORMAT)} -{' '}
                    {dateToCustomFormat(currentTime, toDate, DATE_FORMAT)}
                </Typography.Text>
            </div>
        </BackgroundPlate>
    );
};
