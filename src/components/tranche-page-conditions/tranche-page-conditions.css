@import '@alfalab/core-components/vars';

.tranche-page-conditions {
    &__conditions-container {
        display: grid;
        grid-template-columns: 232px 232px 232px;
        padding: var(--gap-32);
        gap: var(--gap-xl);

        @media (max-width: 1025px) {
            grid-template-columns: 232px 232px;
        }

        @media (--small-only) {
            padding: var(--gap-24);
            gap: var(--gap-m);
            grid-template-columns: 232px;
        }
    }

    &__condition-item {
        width: 232px;

        @media (min-width: 1250px) {
            &:not(:first-child) {
                border-left: 1px solid var(--color-dark-indigo-15-flat);
                padding-left: var(--gap-16);
            }
        }

        @media (min-width: 700px) {
            &:nth-child(even) {
                border-left: 1px solid var(--color-dark-indigo-15-flat);
                padding-left: var(--gap-16);
            }
        }

        @media (--small-only) {
            &:nth-child(even) {
                border-left: none;
                padding-left: 0;
            }
        }
    }

    &__text {
        display: block;
        margin-bottom: calc(var(--gap-4) + var(--gap-2));
    }
}
