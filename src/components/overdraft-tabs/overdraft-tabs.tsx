import React, { type MouseEvent, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Indicator } from '@alfalab/core-components/indicator';
import { Tab, Tabs } from '@alfalab/core-components/tabs';

import { newStatementsCountSelector } from '#/src/ducks/statement-requests/selectors';

import { ETabs, type TTabsList } from '../../types/overdraft';
import { getOverdraftTabsData } from '../../view-utils/overdraft';

type TProps = {
    hasClientLimit: boolean;
    currentTab: ETabs;
    withTranches: boolean;
    onTabClick: (tab: ETabs) => void;
    onDidMount: (currentTab: ETabs) => void;
    isDocumentsTabVisible: boolean;
    canChangeRepaymentType: boolean;
    isSuspensiveConditionsTabVisible: boolean;
    notificationsCount?: number;
    isStatementsTabVisible: boolean;
};

const OverdraftTabs: React.FC<TProps> = ({
    withTranches,
    hasClientLimit,
    onDidMount,
    canChangeRepaymentType,
    currentTab,
    isDocumentsTabVisible,
    isSuspensiveConditionsTabVisible,
    onTabClick,
    notificationsCount,
    isStatementsTabVisible,
}) => {
    const [tabsList, setTabsList] = useState<TTabsList | null>(null);
    const statementsCount = useSelector(newStatementsCountSelector);

    useEffect(() => {
        const { currentTab, tabsList } = getOverdraftTabsData({
            withTranches,
            hasClientLimit,
            canChangeRepaymentType,
        });

        onDidMount(currentTab);

        setTabsList(tabsList);
    }, []);

    const handleChangeTabs = (_: MouseEvent, { selectedId }: { selectedId: string | number }) => {
        onTabClick(selectedId as ETabs);
    };

    // TODO: need refactoring
    const getOtherTabs = (): JSX.Element => {
        if (!tabsList) return null as unknown as JSX.Element;

        return tabsList.map((tabInfo) => (
            <Tab key={tabInfo.value} id={tabInfo.value} title={tabInfo.text} />
        )) as unknown as JSX.Element;
    };

    return (
        <Tabs scrollable={true} selectedId={currentTab} onChange={handleChangeTabs}>
            {getOtherTabs()}

            <Tab
                id={ETabs.documents}
                title='Кредитная документация'
                hidden={!isDocumentsTabVisible}
            />

            <Tab
                id={ETabs.suspensiveConditions}
                title='Отлагательные условия'
                hidden={!isSuspensiveConditionsTabVisible}
                rightAddons={
                    notificationsCount ? (
                        <Indicator height={20} view='red' value={notificationsCount} />
                    ) : null
                }
                keepMounted={true}
            />
            <Tab
                id={ETabs.statementRequests}
                title='Выписки'
                hidden={!isStatementsTabVisible}
                rightAddons={
                    statementsCount > 0 ? (
                        <Indicator height={20} view='red' value={statementsCount} />
                    ) : null
                }
                keepMounted={true}
            />
        </Tabs>
    );
};

export default OverdraftTabs;
