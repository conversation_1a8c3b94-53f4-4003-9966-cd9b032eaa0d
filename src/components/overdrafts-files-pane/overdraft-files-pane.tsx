import React, { useCallback, useState } from 'react';
import { createCn } from 'bem-react-classname';

import { Link } from '@alfalab/core-components/link';
import { Notification } from '@alfalab/core-components/notification';
import { DocumentPdfMIcon } from '@alfalab/icons-glyph/DocumentPdfMIcon';

import './overdraft-files-pane.css';

type TProps = {
    overdraftRulesFilePath: string;
};
const cn = createCn('overdraft-docs-pane');

const OverdraftFilesPane: React.FC<TProps> = ({ overdraftRulesFilePath }) => {
    const [isVisibleNotification, setNotificationVisibility] = useState(false);

    const handleCloseNotification = useCallback(() => {
        setNotificationVisibility(false);
    }, []);

    return (
        <div className={cn()}>
            <Notification
                visible={isVisibleNotification}
                badge='positive'
                autoCloseDelay={8000}
                title='Идет загрузка файла'
                onCloseTimeout={handleCloseNotification}
                onClose={handleCloseNotification}
                onClickOutside={handleCloseNotification}
            >
                Не покидайте страницу, иначе загрузка прервётся
            </Notification>
            {overdraftRulesFilePath && (
                <Link href={overdraftRulesFilePath} download={true} className={cn('link')}>
                    <DocumentPdfMIcon className={cn('icon')} width={18} height={18} />
                    Соглашение о кредитовании банковского счёта
                </Link>
            )}
        </div>
    );
};

export default OverdraftFilesPane;
