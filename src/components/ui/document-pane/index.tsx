import React, { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { Notification } from '@alfalab/core-components/notification';
import { Spinner } from '@alfalab/core-components/spinner';
import { FormatPdfMBlackIcon } from '@alfalab/icons-classic/FormatPdfMBlackIcon';

import { getDocumentStart } from '#/src/ducks/documents/actions';
import { getDocumentStatusSelector } from '#/src/ducks/documents/selectors';
import { DocumentDownloadStatuses, type DocumentTypes } from '#/src/types/documents';

import './styles.css';

type Props = {
    className?: string;
    documentName: string;
    documentType: DocumentTypes;
    participantCode: string;
    onButtonClick?: () => void;
};

const DocumentPane: React.FC<Props> = ({
    className,
    documentName,
    documentType,
    participantCode,
    onButtonClick,
}) => {
    const cn = createCn('document-pane', className);

    const dispatch = useDispatch();

    const selectDocumentStatus = useMemo(
        () => getDocumentStatusSelector(participantCode, documentType),
        [participantCode, documentType],
    );

    const status = useSelector(selectDocumentStatus);

    const handleClick = useCallback(() => {
        dispatch(getDocumentStart(documentType, documentName, participantCode));
        onButtonClick?.();
    }, [dispatch, documentType, documentName, participantCode, onButtonClick]);

    return (
        <React.Fragment>
            {status === DocumentDownloadStatuses.ERROR && (
                <Notification
                    visible={true}
                    badge='negative'
                    title='Не удалось скачать документ'
                    data-test-id='download-error-notification'
                >
                    Попробуйте ещё раз
                </Notification>
            )}
            <div className={cn()} role='button' tabIndex={0} onClick={handleClick}>
                {status === DocumentDownloadStatuses.IN_PROGRESS ? (
                    <Spinner preset={16} visible={true} />
                ) : (
                    <FormatPdfMBlackIcon />
                )}
                <span className={cn('name')}>{documentName}</span>
            </div>
        </React.Fragment>
    );
};

export default React.memo(DocumentPane);
