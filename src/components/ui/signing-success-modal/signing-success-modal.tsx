import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Modal } from '@alfalab/core-components/modal';
import { StatusScreenSuccess } from 'arui-private/status-screen';

import { setSuccessfullySignedModalOpen } from '#/src/ducks/early-pay/actions';
import { isSuccessfullySignedModalOpenSelector } from '#/src/ducks/early-pay/selectors';

export const SigningSuccessModal = () => {
    const dispatch = useDispatch();
    const isSigningSuccessModalOpen = useSelector(isSuccessfullySignedModalOpenSelector);

    const onClose = () => {
        dispatch(setSuccessfullySignedModalOpen(false));
    };

    return (
        <Modal open={isSigningSuccessModalOpen} hasCloser={true} onClose={onClose}>
            <StatusScreenSuccess
                title='Подписание прошло успешно'
                useBackgroundPlate={true}
                secondaryButtonProps={{ label: 'Понятно', props: { onClick: onClose } }}
            />
        </Modal>
    );
};
