import * as React from 'react';
import { createCn } from 'bem-react-classname';

import { Typography } from '@alfalab/core-components/typography';

import PaneItem from '../pane-item';

import './date-pane.css';

type TOwnProps = {
    title: React.ReactNode;
    text: React.ReactNode;
    date: React.ReactNode;
    withTooltip?: boolean;
    textForTooltip?: React.ReactNode;
    className?: string;
    popupName?: string;
    onTogglePopup?: (popupName?: string) => void;
    id?: string;
    idForText?: string;
    icon?: React.ReactNode;
};

const DatePane: React.FC<TOwnProps> = ({
    title,
    text,
    date,
    withTooltip = false,
    textForTooltip,
    popupName,
    onTogglePopup,
    idForText,
    className,
    icon,
}) => {
    const cn = createCn('date-pane', className);

    return (
        <div className={cn()}>
            {icon}
            <Typography.Text className={cn('date')} view='caps' weight='bold' color='secondary'>
                {date}
            </Typography.Text>
            <PaneItem
                title={title}
                text={text}
                withTooltip={withTooltip}
                textForTooltip={textForTooltip}
                popupName={popupName}
                onTogglePopup={onTogglePopup}
                idForText={idForText}
            />
        </div>
    );
};

export default DatePane;
