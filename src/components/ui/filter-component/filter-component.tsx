import React, { useCallback } from 'react';
import { createCn } from 'bem-react-classname/create-cn';

import { BaseOption } from '@alfalab/core-components/select/shared';
import {
    type BaseSelectProps,
    type BottomSheetSelectMobileProps,
    type OptionProps,
    type OptionShape,
} from '@alfalab/core-components/select/typings';
import { FilterSelect } from 'arui-private/filter-select';

import { CustomFieldMultiple } from '#/src/components/ui/custom-field-multiple/custom-field-multiple';

import { CustomField, type View } from '../custom-field/custom-field';

import './filter-component.css';

const cn = createCn('filter-component');

export const NUMBER_OF_OPTIONS_TO_SHOW_HEADER = 4;

type Props = {
    label?: string;
    searchPlaceholder?: string;
    hasSearch?: boolean;
    options: BaseSelectProps['options'];
    selected: React.ComponentProps<typeof FilterSelect>['selected'];
    onChange: (value: OptionShape[]) => void;
    onClear?: () => void;
    isBlock?: boolean;
    dataTestId?: string;
    popperClassName?: string;
    className?: string;
    multiple?: boolean;
    showClear?: boolean;
    limitDynamicOptionGroupSize?: boolean;
    optionsListWidth?: 'content' | 'field';
    Option?: React.ComponentType<OptionProps>;
    withoutLabelInFilterContent?: boolean;
    fieldView?: View;
    bottomSheetProps?: BottomSheetSelectMobileProps['bottomSheetProps'];
};

export const FilterComponent = ({
    selected = [],
    onChange,
    onClear,
    options,
    label,
    searchPlaceholder,
    hasSearch = false,
    isBlock = false,
    dataTestId,
    popperClassName,
    className,
    multiple = true,
    showClear = false,
    limitDynamicOptionGroupSize = true,
    optionsListWidth = 'content',
    Option = BaseOption,
    withoutLabelInFilterContent,
    fieldView,
    bottomSheetProps,
}: Props) => {
    const showSearch = hasSearch && options.length > NUMBER_OF_OPTIONS_TO_SHOW_HEADER;

    const handleChangeMultiple = useCallback(
        (currentValue: OptionShape[], _?: string) => {
            onChange(currentValue);
        },
        [onChange],
    );

    const handleClearSelect = useCallback(() => {
        if (onClear) {
            onClear();

            return;
        }

        onChange([]);
    }, [onChange, onClear]);

    if (multiple) {
        return (
            <FilterSelect
                popoverPosition='bottom-start'
                Field={CustomFieldMultiple}
                bottomSheetProps={bottomSheetProps}
                label={label}
                placeholder={label}
                dataTestId={dataTestId}
                popperClassName={popperClassName}
                searchProps={{
                    componentProps: {
                        placeholder: searchPlaceholder,
                    },
                }}
                Option={Option}
                visibleOptions={5}
                limitDynamicOptionGroupSize={limitDynamicOptionGroupSize}
                options={options}
                optionsListWidth={optionsListWidth}
                optionClassName={cn('multi-option')}
                selected={selected}
                onChange={handleChangeMultiple}
                allowUnselect={true}
                multiple={true}
                showHeaderWithSelectAll={options.length > 1}
                showClear={showClear}
                showSearch={showSearch}
                block={isBlock}
                fieldProps={{
                    onClear: handleClearSelect,
                    label,
                    options,
                    withoutLabelInFilterContent,
                    componentView: fieldView,
                }}
                className={className}
            />
        );
    }

    return (
        <FilterSelect
            popoverPosition='bottom-start'
            bottomSheetProps={bottomSheetProps}
            Field={CustomField}
            label={label}
            placeholder={label}
            dataTestId={dataTestId}
            popperClassName={popperClassName}
            selected={selected}
            options={options}
            optionsListWidth={optionsListWidth}
            optionClassName={cn('single-option')}
            showSearch={showSearch}
            searchProps={{
                componentProps: {
                    placeholder: searchPlaceholder,
                },
            }}
            Option={Option}
            onChange={handleChangeMultiple}
            visibleOptions={5}
            limitDynamicOptionGroupSize={limitDynamicOptionGroupSize}
            multiple={false}
            block={isBlock}
            fieldProps={{
                label,
                showClear,
                withoutLabelInFilterContent,
                componentView: fieldView,
            }}
            className={className}
        />
    );
};
