import React from 'react';
import { createCn } from 'bem-react-classname';

import { Typography } from '@alfalab/core-components/typography';

import './status-page-skeleton.css';

type Props = {
    className?: string;
    icon: React.ReactNode;
    title: React.ReactNode;
    description?: React.ReactNode;
    actionButton?: React.ReactNode;
};

const StatusPageSkeleton: React.FC<Props> = ({
    className,
    title,
    description,
    icon,
    actionButton,
}) => {
    const cn = createCn('status-page-skeleton', className);

    return (
        <div className={cn()}>
            <div className={cn('image')}>{icon}</div>
            <div className={cn('title')}>
                <Typography.Title view='small' tag='h1' font='system' color='primary'>
                    {title}
                </Typography.Title>
            </div>
            {description && (
                <Typography.Text className={cn('description')} color='primary'>
                    {description}
                </Typography.Text>
            )}
            {actionButton && <div className={cn('action-button-container')}>{actionButton}</div>}
        </div>
    );
};

export default StatusPageSkeleton;
