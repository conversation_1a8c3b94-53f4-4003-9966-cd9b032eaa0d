import React from 'react';
import { createCn } from 'bem-react-classname';

import { Typography } from '@alfalab/core-components/typography';

import './notify-plate.css';

type TProps = {
    title?: string;
    text?: string;
    className?: string;
    children?: React.ReactNode;
};

const NotifyPlate: React.FC<TProps> = ({ title, text, children, className }) => {
    const cn = createCn('notify-plate', className);

    return (
        <div className={cn()}>
            {title && (
                <Typography.TitleResponsive
                    className={cn('title')}
                    view='small'
                    tag='h4'
                    font='system'
                    color='primary'
                >
                    {title}
                </Typography.TitleResponsive>
            )}
            {text && (
                <Typography.Text className={cn('text')} view='primary-medium' color='primary'>
                    {text}
                </Typography.Text>
            )}
            {children}
        </div>
    );
};

export default NotifyPlate;
