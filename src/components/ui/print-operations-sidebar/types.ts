import { type StatementFormat } from 'corp-loan-statements-api-typescript-services';

export type PrintOperationsData = {
    fromDate: string;
    toDate: string;
    format: StatementFormat;
    selectedTranches: string[];
    withSignature?: boolean;
};

export enum PrintOperationsSidebarType {
    SIDEBAR_CREDIT = 'SIDEBAR_CREDIT',
    SIDEBAR_CREDIT_LINE = 'SIDEBAR_CREDIT_LINE',
    SIDEBAR_SOPUK_LINE = 'SIDEBAR_SOPUK_LINE',
    SIDEBAR_OVERDRAFT = 'SIDEBAR_OVERDRAFT',
}

export const PrintOperationsSidebarTypeName = {
    [PrintOperationsSidebarType.SIDEBAR_CREDIT]: 'Кредит',
    [PrintOperationsSidebarType.SIDEBAR_CREDIT_LINE]: 'Кредитная линия',
    [PrintOperationsSidebarType.SIDEBAR_SOPUK_LINE]: 'Рамочный договор',
    [PrintOperationsSidebarType.SIDEBAR_OVERDRAFT]: 'Овердрафт',
};
