/* eslint-disable complexity */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type ServicingAccounts } from 'corp-credit-products-api-typescript-services';
import { StatementFormat } from 'corp-loan-statements-api-typescript-services';
import { isBefore, startOfDay } from 'date-fns';
import formatDate from 'date-fns/format';
import startOfWeek from 'date-fns/startOfWeek';
import subDays from 'date-fns/subDays';
import { type TServicingAccountsV2 } from 'thrift-services/services/credit_products_v2';
import { type UnixEpoch } from 'thrift-services/utils';

import { Button } from '@alfalab/core-components/button';
import { CalendarRange } from '@alfalab/core-components/calendar-range';
import { Checkbox } from '@alfalab/core-components/checkbox';
import { Loader } from '@alfalab/core-components/loader';
import { ModalDesktop } from '@alfalab/core-components/modal/desktop';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Plate } from '@alfalab/core-components/plate';
import { RadioGroup } from '@alfalab/core-components/radio-group';
import { SidePanel } from '@alfalab/core-components/side-panel';
import { Tag } from '@alfalab/core-components/tag';
import { Typography } from '@alfalab/core-components/typography';

import {
    DATE_FOR_SERVICES,
    DATE_FORMAT,
    TIME_PERIODS_MAP,
    TimePeriods,
} from '#/src/constants/date';
import { NBSP } from '#/src/constants/unicode-symbols';
import { getByAccountNumbersStart } from '#/src/ducks/accounts/actions';
import {
    filteredAccountsSelector,
    isAccountsFetchingSelector,
} from '#/src/ducks/accounts/selectors';
import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import { isCreditProductsTranchesFetchingSelector } from '#/src/ducks/credit-products/selectors/tranches.selectors';
import { getTranchesSelectStart } from '#/src/ducks/credit-products/tranches-select/actions';
import {
    tranchesSelectIsLoadingSelector,
    tranchesSelectSelector,
} from '#/src/ducks/credit-products/tranches-select/selectors/tranches.selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { STATEMENTS_METRICS } from '#/src/metrics';
import { ETrancheTypes } from '#/src/sagas/workers/get-tranche-credit-products-worker';
import {
    calculateDateByPeriod,
    getMoscowTimeZone,
    parseDateFromAny,
    reformatDate,
    toUnixEpoch,
} from '#/src/utils/date';
import { getEarliestTrancheOpenDate, hasActiveTrancheOutOfDateRange } from '#/src/utils/statements';

import TranchesSelectWithTags from '../tranches-select';
import { type TrancheOption } from '../tranches-select/types';

import {
    type PrintOperationsData,
    PrintOperationsSidebarType,
    PrintOperationsSidebarTypeName,
} from './types';

import './print-operations-sidebar.css';

export const cn = createCn('print-operations-sidebar');

const FORMAT_MAP: Partial<Record<StatementFormat, string>> = {
    [StatementFormat.Pdf]: 'PDF',
    [StatementFormat.Xls]: 'Excel',
};

type Props = {
    docNumber: string;
    servicingAccounts?: Array<TServicingAccountsV2 | ServicingAccounts>;
    selectedTrancheDocNumber?: string;
    customerOrganizationId?: string;
    startDate?: UnixEpoch;
    closedDate?: UnixEpoch;
    type: PrintOperationsSidebarType;
    isSignatureVisible: boolean;
    isClosedDeal?: boolean;
    onClose: () => void;
    onSubmit: (data: PrintOperationsData) => void;
    isVisible: boolean;
};

export const PrintOperationsSidebarContent: React.FC<Props> = ({
    docNumber,
    selectedTrancheDocNumber,
    customerOrganizationId,
    type,
    servicingAccounts,
    isSignatureVisible,
    closedDate,
    startDate,
    isClosedDeal = false,
    onClose,
    onSubmit,
    isVisible,
}) => {
    const dispatch = useDispatch();
    const currentTime = useSelector(currentTimeSelector);
    const [isMobile] = useMatchMedia('--mobile');
    const [isTablet] = useMatchMedia('--tablet');

    const today = closedDate ? new Date(closedDate.seconds * 1000) : currentTime;
    const yesterday = closedDate ? today : subDays(today, 1);
    const yesterdayFormatted = formatDate(yesterday, DATE_FORMAT);
    const weekAgoFormatted = formatDate(startOfWeek(yesterday, { weekStartsOn: 1 }), DATE_FORMAT);
    const startDateFormatted = startDate && formatDate(startDate.seconds * 1000, DATE_FORMAT);

    const [fromDate, setFromDate] = useState(weekAgoFormatted);
    const [toDate, setToDate] = useState(yesterdayFormatted);
    const [period, setPeriod] = useState<TimePeriods | null>(TimePeriods.WEEK);
    const [format, setFormat] = useState(StatementFormat.Pdf);

    const [selectedTranches, setSelectedTranches] = useState<TrancheOption[]>([]);
    const [isDateFromError, setIsDateFromError] = useState(false);
    const [isDateToError, setIsDateToError] = useState(false);
    const [signature, setSignature] = useState(false);

    const { tranches } = useSelector(tranchesSelectSelector);
    const filteredAccounts = useSelector(filteredAccountsSelector);
    const isAccountsFetching = useSelector(isAccountsFetchingSelector);
    const isCreditProductsTranchesFetching = useSelector(isCreditProductsTranchesFetchingSelector);
    const isLoadingTranchesSelect = useSelector(tranchesSelectIsLoadingSelector);
    const organizationId = useSelector(currentOrganizationEqIdSelector);

    const isLoading =
        isCreditProductsTranchesFetching || isLoadingTranchesSelect || isAccountsFetching;

    const isCreditLineOrSopuk = [
        PrintOperationsSidebarType.SIDEBAR_CREDIT_LINE,
        PrintOperationsSidebarType.SIDEBAR_SOPUK_LINE,
    ].includes(type);

    const isActiveTrancheOutOfDateRange = useMemo(
        () => hasActiveTrancheOutOfDateRange(selectedTranches, fromDate, toDate),
        [fromDate, selectedTranches, toDate],
    );

    const getNotifyLabel = () => {
        if (!selectedTrancheDocNumber && isActiveTrancheOutOfDateRange) {
            if (type === PrintOperationsSidebarType.SIDEBAR_CREDIT_LINE) return 'транши';
            if (type === PrintOperationsSidebarType.SIDEBAR_SOPUK_LINE) return 'кредиты';
        }

        return null;
    };

    const notifyLabel = getNotifyLabel();

    const filteredTranches = useMemo(
        () =>
            tranches.filter((tranche) => {
                if (selectedTrancheDocNumber) {
                    return tranche.docNumber === selectedTrancheDocNumber;
                }

                return selectedTranches.some((value) => tranche.docNumber === value.key);
            }),
        [selectedTrancheDocNumber, selectedTranches, tranches],
    );

    const accountNumbers = useMemo(() => {
        if (selectedTrancheDocNumber) {
            return filteredTranches?.[0]?.servicingAccounts
                ?.map((account) => account.number ?? '')
                .filter(Boolean);
        }

        return servicingAccounts?.map((account) => account.number ?? '').filter(Boolean);
    }, [filteredTranches, selectedTrancheDocNumber, servicingAccounts]);

    const openDate = useMemo(() => {
        if (isCreditLineOrSopuk && !isLoading) {
            const isSingleTranchePage = selectedTrancheDocNumber && accountNumbers?.length;
            const accounts = isSingleTranchePage
                ? filteredAccounts.filter((acc) =>
                      accountNumbers.includes(acc.mainInfo.number || ''),
                  )
                : filteredAccounts;

            return getEarliestTrancheOpenDate({
                accounts,
                tranches: filteredTranches,
                startDate,
                currentTime,
            });
        }

        const dateString = filteredAccounts.find(
            (account) => account?.mainInfo?.number === servicingAccounts?.[0]?.number,
        )?.mainInfo?.openDate;

        return dateString == null
            ? startDate
            : toUnixEpoch({
                  currentTime,
                  dateString,
              });
    }, [
        accountNumbers,
        currentTime,
        filteredAccounts,
        filteredTranches,
        isCreditLineOrSopuk,
        isLoading,
        selectedTrancheDocNumber,
        servicingAccounts,
        startDate,
    ]);

    const openDateFormatted = useMemo(
        () => openDate && formatDate(openDate.seconds * 1000, DATE_FORMAT),
        [openDate],
    );

    const isVisibleOpenDateMismatchNotify =
        type !== PrintOperationsSidebarType.SIDEBAR_CREDIT_LINE &&
        type !== PrintOperationsSidebarType.SIDEBAR_SOPUK_LINE &&
        openDateFormatted &&
        openDateFormatted !== startDateFormatted;

    const onChangeFormat: React.ComponentProps<typeof RadioGroup>['onChange'] = (_, payload) => {
        setFormat(payload?.value as StatementFormat);

        if (payload?.value == StatementFormat.Xls) {
            setSignature(false);
        }
    };

    const onChangePeriod: React.ComponentProps<typeof RadioGroup>['onChange'] = (_, payload) => {
        setPeriod(payload?.value as TimePeriods);
        setFromDate(
            formatDate(
                calculateDateByPeriod(payload?.value as TimePeriods, currentTime, closedDate),
                DATE_FORMAT,
            ),
        );
        setToDate(formatDate(yesterday, DATE_FORMAT));
    };

    const calendarMinDate = useMemo(
        () =>
            openDate
                ? startOfDay(openDate.seconds * 1000).getTime()
                : startOfDay(Number(startDate?.seconds) * 1000).getTime(),
        [openDate, startDate?.seconds],
    );

    const isSubmitButtonDisabled = useMemo(
        () =>
            isDateFromError ||
            isDateToError ||
            (isCreditLineOrSopuk && (!selectedTranches.length || isLoading)),
        [isCreditLineOrSopuk, isDateFromError, isDateToError, isLoading, selectedTranches.length],
    );

    const onDateFromChange = useCallback(
        ({ date, value }) => {
            const minDate = getMoscowTimeZone(startOfDay(calendarMinDate));
            const currentTimeZoneDate = getMoscowTimeZone(date);

            const isBeforeThanMinDate = isBefore(currentTimeZoneDate, minDate);

            setIsDateFromError(isBeforeThanMinDate || !date);
            setFromDate(value);
        },
        [calendarMinDate],
    );

    const onDateToChange = useCallback(({ date, value }) => {
        const maxDate = new Date();

        setIsDateToError(!date || date > maxDate);
        setToDate(value);
    }, []);

    const handleSubmit = useCallback(() => {
        dispatch(trackAlfaMetrics(STATEMENTS_METRICS.сlickCreateButton, {}));

        onSubmit({
            format,
            fromDate: reformatDate(fromDate, DATE_FORMAT, DATE_FOR_SERVICES),
            toDate: reformatDate(toDate, DATE_FORMAT, DATE_FOR_SERVICES),
            selectedTranches: selectedTranches.map((item) => item.key),
            withSignature: signature,
        });
    }, [dispatch, onSubmit, format, fromDate, toDate, selectedTranches, signature]);

    const handleChangeSignature = () => {
        setSignature(!signature);
    };

    useEffect(() => {
        if (!isLoading) {
            if (isCreditLineOrSopuk) {
                dispatch(
                    getTranchesSelectStart({
                        organizationId: customerOrganizationId ?? organizationId,
                        docNumber,
                        trancheType:
                            type === PrintOperationsSidebarType.SIDEBAR_SOPUK_LINE
                                ? ETrancheTypes.deal
                                : ETrancheTypes.tranche,
                    }),
                );
            } else if (accountNumbers?.length) {
                dispatch(
                    getByAccountNumbersStart({
                        accountNumbers,
                        organizationId: customerOrganizationId,
                    }),
                );
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [organizationId, customerOrganizationId, docNumber, isCreditProductsTranchesFetching, type]);

    useEffect(() => {
        if (parseDateFromAny(currentTime, fromDate, DATE_FORMAT).getTime() < calendarMinDate) {
            setFromDate(formatDate(calendarMinDate, DATE_FORMAT));
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [calendarMinDate]);

    // на мобилке пока не отображаем выписки
    if (isMobile) {
        return null;
    }

    const renderContent = () => (
        <React.Fragment>
            {isLoading ? (
                <Loader className={cn('loader')} />
            ) : (
                <div className={cn('content-wrapper')}>
                    {isCreditLineOrSopuk && (
                        <TranchesSelectWithTags
                            productType={
                                type === PrintOperationsSidebarType.SIDEBAR_SOPUK_LINE
                                    ? ETrancheTypes.deal
                                    : ETrancheTypes.tranche
                            }
                            key='sidebar'
                            onSelect={setSelectedTranches}
                            withTypeTags={!isClosedDeal}
                            selectedTrancheDocNumber={selectedTrancheDocNumber}
                            inputLabel={
                                type === PrintOperationsSidebarType.SIDEBAR_CREDIT_LINE
                                    ? 'Транши'
                                    : 'Кредиты'
                            }
                        />
                    )}
                    <div className={cn('row')} key='range'>
                        <CalendarRange
                            key='calendar'
                            valueFrom={fromDate}
                            valueTo={toDate}
                            onDateFromChange={onDateFromChange}
                            onDateToChange={onDateToChange}
                            minDate={calendarMinDate}
                            maxDate={closedDate ? closedDate.seconds * 1000 : yesterday.valueOf()}
                            calendarPosition='popover'
                            className={cn('calendar')}
                        />
                        <Typography.Text
                            key='calendar-error'
                            tag='div'
                            color='negative'
                            view='primary-small'
                            className={cn('date-range-error')}
                        >
                            {isDateFromError && (
                                <div key='e1'>
                                    Выберите дату начала не раньше дня открытия сделки
                                </div>
                            )}
                            {isDateToError && (
                                <div key='e2'>Выберите дату окончания не позже текущей</div>
                            )}
                        </Typography.Text>
                        <RadioGroup
                            key='calendar-lays'
                            direction='horizontal'
                            type='tag'
                            name='period'
                            value={period}
                            onChange={onChangePeriod}
                        >
                            {Object.keys(TIME_PERIODS_MAP).map((key) => (
                                <Tag key={key} value={key} size='xs' view='filled'>
                                    {TIME_PERIODS_MAP[key as TimePeriods]}
                                </Tag>
                            ))}
                        </RadioGroup>
                    </div>
                    <div className={cn('row')} key='format'>
                        <Typography.Text tag='div' view='primary-large' className={cn('label')}>
                            Формат
                        </Typography.Text>
                        <RadioGroup
                            direction='horizontal'
                            type='tag'
                            name='format'
                            value={format}
                            onChange={onChangeFormat}
                        >
                            {Object.keys(FORMAT_MAP).map((key) => (
                                <Tag value={key} size='xs' key={key} view='filled'>
                                    {FORMAT_MAP[key as StatementFormat]}
                                </Tag>
                            ))}
                        </RadioGroup>
                    </div>
                    {isSignatureVisible && (
                        <div className={cn('row')}>
                            <Checkbox
                                onChange={handleChangeSignature}
                                checked={signature}
                                label='С электронной подписью банка'
                                inactive={format == StatementFormat.Xls}
                            />
                        </div>
                    )}

                    {isVisibleOpenDateMismatchNotify && (
                        <Plate rounded={false} view='attention' className={cn('attention-notify')}>
                            Дата открытия ссудного счета {openDateFormatted} не{NBSP}
                            совпадает с{NBSP}датой открытия кредита, поэтому в{NBSP}выписке не
                            {NBSP}будут отображаться операции, совершенные{NBSP}до{' '}
                            {openDateFormatted}. Для получения полной выписки по{NBSP}счету
                            обратитесь в{NBSP}отделение банка.
                        </Plate>
                    )}

                    {!!notifyLabel && (
                        <Plate rounded={false} view='attention' className={cn('attention-notify')}>
                            Покажем только те {notifyLabel}, которые действовали
                            <br />в выбранном периоде
                        </Plate>
                    )}
                </div>
            )}
        </React.Fragment>
    );

    if (isTablet) {
        return (
            <ModalDesktop
                disableFocusLock={true}
                open={isVisible}
                onClose={onClose}
                dataTestId='early-pay-sidebar'
                size={800}
                className={cn('tablet')}
            >
                <ModalDesktop.Header>
                    <div className={cn('tablet-header-wrapper')}>
                        <Typography.Title view='medium' font='system' tag='div'>
                            Выписка по задолженности
                        </Typography.Title>
                        <Typography.Text view='primary-medium' color='secondary'>
                            {PrintOperationsSidebarTypeName[type]} №{docNumber}
                        </Typography.Text>
                    </div>
                </ModalDesktop.Header>

                <ModalDesktop.Content flex={true} className={cn('content')}>
                    {renderContent()}
                </ModalDesktop.Content>

                <ModalDesktop.Footer>
                    <Button
                        key='download'
                        view='primary'
                        onClick={handleSubmit}
                        disabled={isSubmitButtonDisabled}
                        size='s'
                        block={true}
                    >
                        Создать выписку
                    </Button>
                </ModalDesktop.Footer>
            </ModalDesktop>
        );
    }

    return (
        <SidePanel
            disableFocusLock={true}
            open={isVisible}
            onClose={onClose}
            dataTestId='early-pay-sidebar'
        >
            <SidePanel.Header
                bottomAddons={
                    <Typography.Text view='primary-medium' color='secondary' tag='div'>
                        {PrintOperationsSidebarTypeName[type]} №{docNumber}
                    </Typography.Text>
                }
                className='side-panel-header'
                bottomAddonsClassName={cn('side-panel-header-bottom-addons')}
            >
                <Typography.Title view='medium' font='system' tag='h1'>
                    Выписка по задолженности
                </Typography.Title>
            </SidePanel.Header>

            <SidePanel.Content className={cn('content')}>{renderContent()}</SidePanel.Content>

            <SidePanel.Footer>
                <Button
                    key='download'
                    view='primary'
                    onClick={handleSubmit}
                    disabled={isSubmitButtonDisabled}
                    size='s'
                >
                    Создать выписку
                </Button>
            </SidePanel.Footer>
        </SidePanel>
    );
};
