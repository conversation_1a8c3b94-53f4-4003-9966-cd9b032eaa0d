@import '@alfalab/core-components/vars/index.css';

.print-operations-sidebar {
    &__label {
        margin-bottom: var(--gap-12);
    }

    &__calendar {
        margin-bottom: var(--gap-12);

        > div {
            width: 100%;
        }
    }

    &__subtitle {
        margin-bottom: var(--gap-32);
    }

    &__row {
        margin-bottom: var(--gap-24);
        position: relative;

        &_tranches {
            margin-bottom: var(--gap-12);
        }
    }

    &__date-range-error {
        position: relative;
        top: var(--gap-xs-neg);
    }

    &__loader {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: var(--gap-40);
    }

    &__calendar-label {
        margin-bottom: var(--gap-8);
    }

    &__tablet-header-wrapper {
        display: flex;
        flex-direction: column;
        white-space: nowrap;
        gap: var(--gap-4);
    }

    &__mobile-header-text {
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &__error-notification {
        width: 280px;
    }

    &__success-notification {
        width: 432px;
    }

    &__tablet {
        height: 100%;
    }

    &__side-panel-header-bottom-addons {
        padding: var(--gap-0) var(--gap-12) var(--gap-16) var(--gap-12);
    }

    &__content {
        margin-top: var(--gap-16);
    }

    &__content-wrapper {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    &__attention-notify {
        margin-top: auto;
    }
}
