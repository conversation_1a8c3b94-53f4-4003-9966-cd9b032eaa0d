import React from 'react';
import { type ServicingAccounts } from 'corp-credit-products-api-typescript-services';
import { type TServicingAccountsV2 } from 'thrift-services/services/credit_products_v2';
import { type UnixEpoch } from 'thrift-services/utils';

import { Notification } from '@alfalab/core-components/notification';

import { cn, PrintOperationsSidebarContent } from './print-operations-sidebar-content';
import { type PrintOperationsData, type PrintOperationsSidebarType } from './types';

type Props = {
    isFetching: boolean;
    isError: boolean;
    docNumber?: string;
    servicingAccounts?: Array<TServicingAccountsV2 | ServicingAccounts>;
    startDate?: UnixEpoch;
    closedDate?: UnixEpoch;
    type: PrintOperationsSidebarType;
    isVisible: boolean;
    isSignatureVisible?: boolean;
    isClosedDeal?: boolean;
    selectedTrancheDocNumber?: string;
    customerOrganizationId?: string;
    onClose: () => void;
    onSubmit: (data: PrintOperationsData) => void;
    onCloseNotification: () => void;
};

const PrintOperationsSidebar: React.FC<Props> = ({
    isFetching,
    isError,
    docNumber,
    servicingAccounts,
    type,
    isVisible,
    isSignatureVisible = false,
    isClosedDeal = false,
    startDate,
    closedDate,
    selectedTrancheDocNumber,
    customerOrganizationId,
    onClose,
    onSubmit,
    onCloseNotification,
}) => (
    <React.Fragment>
        <Notification
            visible={isFetching}
            badge='neutral-operation'
            title='Выписка создается'
            onClose={onCloseNotification}
            className={cn('success-notification')}
        >
            Это займёт до 2 минут
        </Notification>
        <Notification
            visible={isError}
            badge='negative'
            onClose={onCloseNotification}
            title='Не получилось создать выписку'
            contentClassName={cn('error-notification')}
        >
            <span>Попробуйте ещё раз</span>
        </Notification>
        {isVisible && (
            <PrintOperationsSidebarContent
                docNumber={docNumber}
                type={type}
                servicingAccounts={servicingAccounts}
                isSignatureVisible={isSignatureVisible}
                startDate={startDate}
                closedDate={closedDate}
                onClose={onClose}
                onSubmit={onSubmit}
                isClosedDeal={isClosedDeal}
                selectedTrancheDocNumber={selectedTrancheDocNumber}
                customerOrganizationId={customerOrganizationId}
                isVisible={isVisible}
            />
        )}
    </React.Fragment>
);

export default PrintOperationsSidebar;
