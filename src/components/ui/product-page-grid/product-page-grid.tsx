import React, { useEffect, useMemo } from 'react';
import { createCn } from 'bem-react-classname';

import RequestAlreadyCreatedErrorModal from '#/src/containers/request-already-created-error-modal';
import SigningErrorModal from '#/src/containers/signing-error-modal';
import { type SomeMappedProduct } from '#/src/utils/credit-products-mappers';

import { ErrorStateForCreditProduct } from '../../error-state-for-credit-product';
import { SigningSuccessModal } from '../signing-success-modal';

import './product-page-grid.css';

type TownProps = {
    productInfo: SomeMappedProduct;
    heading?: React.ReactNode;
    subHeading?: React.ReactNode;
    datesPane?: React.ReactNode;
    productInfoPane?: React.ReactNode;
    isExpanded?: boolean;
    onPageDidMountHandler?: (docNumber: string) => void;
    tabInfo?: React.ReactNode;
    tabs?: React.ReactNode;
    debtPane?: React.ReactNode;
};

type TProps = TownProps;

const cn = createCn('product-page-grid');

const ProductPageGrid: React.FC<TProps> = ({
    productInfo,
    heading,
    subHeading,
    tabInfo,
    tabs,
    isExpanded,
    debtPane,
    datesPane,
    productInfoPane,
    onPageDidMountHandler,
}) => {
    const { docNumber } = productInfo || {};

    const productType = useMemo(
        () => (productInfo && 'type' in productInfo ? productInfo.type : false),
        [productInfo],
    );

    useEffect(() => {
        if (onPageDidMountHandler && docNumber) {
            onPageDidMountHandler(docNumber);
        }
    }, []); // eslint-disable-line react-hooks/exhaustive-deps

    if (!productInfo || ('fault' in productInfo && productInfo.fault)) {
        return <ErrorStateForCreditProduct heading={heading} />;
    }

    return (
        <div className={cn()}>
            {heading && <section className={cn('section', { heading: true })}>{heading}</section>}
            {subHeading && (
                <section className={cn('section', { subheading: true })}>{subHeading}</section>
            )}
            {datesPane && <section className={cn('section', { dates: true })}>{datesPane}</section>}
            {debtPane && (
                <section className={cn('section', { debt: true, type: productType })}>
                    {debtPane}
                </section>
            )}
            {tabs && (
                <section className={cn('section', { tabs: true, expanded: !!isExpanded })}>
                    {tabs}
                </section>
            )}
            {tabInfo && (
                <section className={cn('section', { expanded: !!isExpanded })}>{tabInfo}</section>
            )}
            {productInfoPane && <section className={cn('section')}>{productInfoPane}</section>}
            <SigningErrorModal />
            <SigningSuccessModal />
            <RequestAlreadyCreatedErrorModal />
        </div>
    );
};

export default ProductPageGrid;
