@import '@alfalab/core-components/vars';

.product-page-grid {
    width: 100%;

    &__section {
        margin-bottom: var(--gap-32);
        padding: var(--gap-0);

        @media (--desktop-l) {
            padding: var(--gap-0);
        }

        &_heading {
            margin-bottom: var(--gap-0);
        }

        &_subheading {
            margin-bottom: var(--gap-16);

            @media (--small-only) {
                margin-bottom: var(--gap-8);
            }
        }

        &_dates {
            display: flex;
            align-items: flex-end;
            margin-bottom: var(--gap-32);

            @media (--small-only) {
                flex-direction: column;
                align-items: flex-start;
            }
        }

        &_type_overdraft,
        &_type_creditLine,
        &_type_sopuk {
            max-width: 100%;
        }

        &_type_sopuk {
            padding-top: var(--gap-0);
        }

        &_tabs {
            margin-bottom: var(--gap-24);
        }

        &_expanded {
            width: 100%;
            max-width: none;
        }

        .pane-group {
            margin: var(--gap-0);
        }
    }
}
