import * as React from 'react';
import { createCn } from 'bem-react-classname';

import { type mapBaseTranche } from '../../../../utils/credit-products-mappers';
import SectionSpin from '../../section-spin/section-spin';
import DetailFields from '../detail-fields';

import './desktop-tranche-detail.css';

type TProps = {
    isCheckboxAvailable?: boolean;
    isFlexiblePayment: boolean;
    isFineExists: boolean;
    hasDetail?: boolean;
    tranche: ReturnType<typeof mapBaseTranche>;
};

const cn = createCn('desktop-tranche-detail');

const DesktopTrancheDetail: React.FC<TProps> = ({
    isCheckboxAvailable,
    isFlexiblePayment,
    isFineExists,
    tranche,
    hasDetail,
}) => (
    <div className={cn()}>
        <div className={cn('inner', { 'with-checkbox': isCheckboxAvailable })}>
            {hasDetail ? (
                <DetailFields
                    tranche={tranche}
                    isDesktop={true}
                    isFineExists={isFineExists}
                    isFlexiblePayment={isFlexiblePayment}
                />
            ) : (
                <div className={cn('body')}>
                    <SectionSpin />
                </div>
            )}
        </div>
    </div>
);

export default DesktopTrancheDetail;
