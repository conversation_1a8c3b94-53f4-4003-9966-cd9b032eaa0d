import React from 'react';

import { Modal } from '@alfalab/core-components/modal';

import { SigningErrorModalContent } from './signing-error-modal-content';

type Props = {
    onClose: () => void;
    visible: boolean;
    docNumber?: string;
};

const SigningErrorModal = ({ visible, onClose: handleClose, docNumber }: Props) => (
    <Modal open={visible} hasCloser={true} onClose={handleClose}>
        <SigningErrorModalContent docNumber={docNumber} onClose={handleClose} />
    </Modal>
);

export default SigningErrorModal;
