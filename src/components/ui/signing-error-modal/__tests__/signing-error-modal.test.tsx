import React from 'react';
import { render } from '@testing-library/react';

import { SigningErrorModalContent } from '../signing-error-modal-content';

Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

describe('SigningErrorModal', () => {
    describe('metrics', () => {
        it('should track metric on mount', () => {
            const track = {
                show: jest.fn(),
            };

            render(
                <SigningErrorModalContent
                    docNumber='docNumber'
                    useSigningErrorModalMetrics={() => track}
                    onClose={() => {}}
                />,
            );

            expect(track.show).toHaveBeenCalledWith('docNumber');
        });
    });
});
