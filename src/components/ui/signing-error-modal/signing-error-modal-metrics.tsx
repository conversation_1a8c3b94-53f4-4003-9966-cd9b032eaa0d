import { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { earlyRepaymentProcessingTypeSelector } from '#/src/ducks/credit-processing/selectors';
import { EARLY_REPAYMENT_METRICS } from '#/src/metrics';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

export const useSigningErrorModalMetrics = () => {
    const trackAlfaMetrics = useTrackAlfaMetrics();
    const processingType = useSelector(earlyRepaymentProcessingTypeSelector);

    return useMemo(
        () => ({
            show: (agreementNumber: string) =>
                trackAlfaMetrics(EARLY_REPAYMENT_METRICS.showSigningErrorModal, {
                    agreementNumber,
                    processingType: processingType[agreementNumber]?.toLowerCase(),
                }),
        }),
        [processingType, trackAlfaMetrics],
    );
};
