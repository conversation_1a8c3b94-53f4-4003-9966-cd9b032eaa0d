@import '@alfalab/core-components/vars';

.signing-error-modal {
    padding-bottom: var(--gap-48);

    &__content {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    &__heading {
        margin-top: var(--gap-24);
        margin-bottom: var(--gap-12);
    }

    &__about-error {
        max-width: 426px;
        margin-bottom: var(--gap-12);

        @media (--mobile-l) {
            margin-bottom: var(--gap-32);
        }
    }

    &__support-contacts-section {
        max-width: 480px;
        margin-top: var(--gap-12);

        @media (--mobile-l) {
            margin-top: var(--gap-32);
        }
    }

    &__support-contacts-section-text {
        margin-bottom: var(--gap-12);
    }

    &__phones {
        max-width: 356px;
        margin: 0 auto;

        @media (--mobile-l) {
            display: flex;
            justify-content: space-between;
        }
    }

    &__phone-and-explanation {
        min-width: 162px;
        margin-bottom: var(--gap-12);

        @media (--mobile-l) {
            margin-bottom: 0;
        }
    }

    &__phone-and-explanation-text {
        margin-bottom: calc(var(--gap-2xs) / 2);
    }

    &__phone-link {
        text-decoration: none;
        color: inherit;
    }
}
