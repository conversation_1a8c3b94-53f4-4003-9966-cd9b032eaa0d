import React, { useEffect } from 'react';

import { StatusScreenTechnicalError } from 'arui-private/status-screen';

import { useSigningErrorModalMetrics as useSigningErrorModalMetricsInjectedHook } from './signing-error-modal-metrics';

type Props = {
    onClose: () => void;
    docNumber?: string;
    useSigningErrorModalMetrics?: typeof useSigningErrorModalMetricsInjectedHook;
};

export const SigningErrorModalContent = ({
    docNumber,
    onClose,
    useSigningErrorModalMetrics = useSigningErrorModalMetricsInjectedHook,
}: Props) => {
    const track = useSigningErrorModalMetrics();

    useEffect(() => {
        if (!!docNumber && !!track) {
            track.show(docNumber);
        }
    }, [docNumber, track]);

    return (
        <StatusScreenTechnicalError
            title='Что-то пошло не так'
            subtitle='В процессе подписания возникли временные проблемы. Попробуйте через пару минут заполнить форму снова и отправьте её на подписание'
            useBackgroundPlate={true}
            secondaryButtonProps={{ label: 'Понятно', props: { onClick: onClose } }}
        />
    );
};
