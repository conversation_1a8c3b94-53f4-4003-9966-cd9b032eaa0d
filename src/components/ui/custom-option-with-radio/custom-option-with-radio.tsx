import React from 'react';
import { createCn } from 'bem-react-classname/create-cn';

import { BaseOption } from '@alfalab/core-components/select/shared';
import { type CheckmarkProps, type OptionProps } from '@alfalab/core-components/select/typings';

import './custom-option-with-radio.css';

const cn = createCn('custom-option');

const CustomCheckmark = ({ selected }: CheckmarkProps) => (
    <span className={cn('checkmark-circle', { selected: Boolean(selected) })} />
);

export const CustomOption = (props: OptionProps) => (
    <BaseOption
        {...props}
        className={cn()}
        checkmarkPosition='before'
        Checkmark={CustomCheckmark}
    />
);
