@import '@alfalab/core-components/vars';

.custom-option {
    gap: var(--gap-12);

    &:last-child {
        margin-bottom: var(--gap-8);
    }

    &__checkmark-circle {
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        box-sizing: border-box;
        border-radius: var(--border-radius-circle);
        position: relative;
        margin: var(--gap-2);
        transition:
            background-color 0.2s ease,
            border 0.2s ease;
        background-color: var(--color-light-neutral-0);
        border: 1.5px solid var(--color-light-neutral-translucent-700);

        &:before {
            content: '';
            position: absolute;
            background-color: var(--color-light-neutral-translucent-1300-inverted);
            border-radius: var(--border-radius-circle);
            left: 50%;
            right: var(--gap-0);
            top: 50%;
            bottom: var(--gap-0);
            transition: opacity 0.2s ease;
            opacity: 0;
            width: 6px;
            height: 6px;
            transform: translate(-50%, -50%);
        }
    }

    &__checkmark-circle:hover {
        background-color: var(--color-light-neutral-0-hover);
        border-color: var(--color-light-neutral-translucent-700);
    }

    &__checkmark-circle:active {
        background-color: var(--color-light-neutral-0-press);
        border-color: var(--color-light-neutral-translucent-700);
    }

    &__checkmark-circle_selected {
        background-color: var(--color-light-accent-secondary);
        border: 1.5px solid transparent;

        &:before {
            opacity: 1;
        }
    }

    &__checkmark-circle_selected:hover {
        background-color: var(--color-light-accent-secondary-hover);
        border-color: transparent;
    }

    &__checkmark-circle_selected:active {
        background-color: var(--color-light-accent-secondary-press);
        border-color: transparent;
    }
}
