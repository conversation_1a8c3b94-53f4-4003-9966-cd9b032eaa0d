import React, { useState } from 'react';
import { createCn } from 'bem-react-classname';

import { AmountInput } from '@alfalab/core-components/amount-input';
import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { ButtonMobile } from '@alfalab/core-components/button/mobile';
import { FilterTag } from '@alfalab/core-components/filter-tag';
import { Gap } from '@alfalab/core-components/gap';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Space } from '@alfalab/core-components/space';
import { formatAmount } from '@alfalab/utils';
import { FilterRange } from 'arui-private/filter-range';

import './range-select.css';

type Props = {
    valueFrom: number | null;
    valueTo: number | null;
    onChange: (valueFrom: number | null, valueTo: number | null) => void;
    onApply: (valueFrom: number | null, valueTo: number | null) => void;
    onClear: () => void;
};

const cn = createCn('range-select');

export const getChipLabel = (valueFrom: number | null, valueTo: number | null) => {
    if (valueFrom && valueTo) {
        const formattedValueFrom = formatAmount({
            value: valueFrom,
            currency: 'RUB',
            minority: 100,
            view: 'default',
        }).formattedWithCurrency;

        const formattedValueTo = formatAmount({
            value: valueTo,
            currency: 'RUB',
            minority: 100,
            view: 'default',
        }).formattedWithCurrency;

        return `${formattedValueFrom} - ${formattedValueTo}`;
    }

    if (valueFrom) {
        const formattedValueFrom = formatAmount({
            value: valueFrom,
            currency: 'RUB',
            minority: 100,
            view: 'default',
        }).formattedWithCurrency;

        return `от ${formattedValueFrom}`;
    }

    if (valueTo) {
        const formattedValueTo = formatAmount({
            value: valueTo,
            currency: 'RUB',
            minority: 100,
            view: 'default',
        }).formattedWithCurrency;

        return `до ${formattedValueTo}`;
    }

    return 'Сумма';
};

export const RangeSelect = ({ valueFrom, valueTo, onChange, onApply, onClear }: Props) => {
    const [isMobile] = useMatchMedia('--mobile');

    const [showModal, setShowModal] = useState(false);

    if (isMobile) {
        return (
            <React.Fragment>
                <FilterTag
                    className={cn('mobile-filter')}
                    checked={!!valueTo || !!valueFrom}
                    size={32}
                    view='filled'
                    shape='rectangular'
                    onClick={() => setShowModal(true)}
                    onClear={onClear}
                >
                    {getChipLabel(valueFrom, valueTo)}
                </FilterTag>
                <BottomSheet
                    hasCloser={true}
                    title='Сумма'
                    open={showModal}
                    onClose={() => setShowModal(false)}
                >
                    <Space direction='horizontal' fullWidth={true}>
                        <AmountInput
                            label='От'
                            labelView='outer'
                            placeholder='50 000 ₽'
                            value={valueFrom}
                            integersOnly={false}
                            minority={100}
                            onChange={(_, payload) => {
                                onChange(payload.value, valueTo);
                            }}
                            size={48}
                            block={true}
                        />
                        <AmountInput
                            labelView='outer'
                            label='До'
                            placeholder='100 000 000 ₽'
                            value={valueTo}
                            block={true}
                            onChange={(_, payload) => {
                                onChange(valueFrom, payload.value);
                            }}
                            size={48}
                        />
                    </Space>
                    <Gap size={24} />
                    <Space direction='horizontal' fullWidth={true}>
                        <ButtonMobile
                            onClick={onClear}
                            block={true}
                            size={56}
                            view='secondary'
                            disabled={!valueTo && !valueFrom}
                        >
                            Сбросить
                        </ButtonMobile>
                        <ButtonMobile
                            onClick={() => {
                                setShowModal(false);
                                onApply(valueFrom, valueTo);
                            }}
                            block={true}
                            size={56}
                            view='primary'
                        >
                            Применить
                        </ButtonMobile>
                    </Space>
                </BottomSheet>
            </React.Fragment>
        );
    }

    return <FilterRange size={40} valueFrom={valueFrom} valueTo={valueTo} onChange={onApply} />;
};
