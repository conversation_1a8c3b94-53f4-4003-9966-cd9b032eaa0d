import React from 'react';

import { FilterTag } from '@alfalab/core-components/filter-tag';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { type FieldProps, type OptionShape } from '@alfalab/core-components/select/typings';

const getFieldContent = ({
    selectedMultiple,
    content,
    optionsLength,
}: {
    selectedMultiple?: OptionShape[];
    content?: React.ReactNode;
    optionsLength?: number;
}) => {
    switch (selectedMultiple?.length) {
        case 1:
            return `${content}`;
        case optionsLength:
            return `выбраны все`;
        default:
            return `выбрано ${selectedMultiple?.length}`;
    }
};

export const CustomFieldMultiple = ({
    label,
    selected,
    selectedMultiple,
    innerProps: { ref, ...restInnerProps },
    ...restProps
}: FieldProps) => {
    const [isDesktop] = useMatchMedia('--desktop-s');

    const content = selected?.value || selected?.content;

    const hasNoLabel = restProps?.hasNoLabel;
    const withoutLabelInFilterContent = restProps?.withoutLabelInFilterContent;
    const optionsLength = restProps?.options?.length;

    const checkedContent = (
        <span>
            {`${label ?? 'Выбор'}: `}
            <b>{getFieldContent({ selectedMultiple, content, optionsLength })}</b>
        </span>
    );

    const getContent = () => {
        if (hasNoLabel && content && withoutLabelInFilterContent) {
            return content;
        }

        if (withoutLabelInFilterContent && content) {
            return content;
        }

        return selected ? checkedContent : label;
    };

    return (
        <FilterTag
            checked={!!selected?.content}
            {...restInnerProps}
            {...restProps}
            ref={ref}
            block={true}
            view='filled'
            shape='rectangular'
            size={isDesktop ? 40 : 32}
        >
            {getContent()}
        </FilterTag>
    );
};
