import * as React from 'react';
import { createCn } from 'bem-react-classname';

import { Spinner } from '@alfalab/core-components/spinner';

import './section-spin.css';

const cn = createCn('section-spin');

type Props = {
    visible?: boolean;
};

const SectionSpin: React.FC<Props> = ({ visible = true }: Props) => (
    <div className={cn()}>
        <Spinner preset={24} visible={visible} />
    </div>
);

export default SectionSpin;
