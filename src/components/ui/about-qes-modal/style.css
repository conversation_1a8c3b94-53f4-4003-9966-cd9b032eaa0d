@import '~@alfalab/core-components/vars';

.about-qes-modal {
    @mixin paragraph_primary_small;

    @media (--tablet-m) {
        @mixin paragraph_primary_medium;
        width: calc(100vw - var(--gap-2xl));
        max-width: 800px;
        padding: 0;
    }

    @media (--desktop-m) {
        max-width: 1140px;
    }

    &__header {
        display: flex;
        flex-direction: column-reverse;
        padding: var(--gap-40) var(--gap-16) var(--gap-32) var(--gap-16);
        background-color: var(--color-light-blue);
        border-radius: var(--border-radius-12) var(--border-radius-12) var(--border-radius-0) 0;

        @media (--tablet-m) {
            flex-direction: row;
            padding: var(--gap-64) var(--gap-48) var(--gap-24) var(--gap-48);
        }

        &-heading {
            @mixin headline-system_small;
            max-width: 100%;
            margin-bottom: var(--gap-16);
            z-index: 1;

            @media (--tablet-m) {
                @mixin headline-system_medium;
                max-width: 322px;
            }
            @media (--desktop-m) {
                @mixin headline-system_large;
                max-width: 480px;
            }
        }

        &-figure {
            display: flex;
            flex-shrink: 0;
            margin: 0 auto;
            margin-bottom: var(--gap-48);
            width: 100%;
            justify-content: space-around;

            @media (--tablet-m) {
                width: 366px;
                margin-bottom: 0;
            }

            @media (--desktop-m) {
                width: 532px;
            }

            &-img {
                user-select: none;
                display: block;
                max-width: 100%;
                max-height: 100%;
            }
        }
    }

    &__content {
        &-section {
            background-color: var(--color-dark-bg-primary-inverted);
            padding: var(--gap-32) var(--gap-16);

            @media (--tablet-m) {
                padding: var(--gap-64) var(--gap-48);
            }

            &_grey {
                background-color: var(--color-light-blue);
            }

            &_border-radius {
                border-radius: var(--border-radius-0) 0 var(--border-radius-12)
                    var(--border-radius-12);
            }

            &_padding-top {
                padding-top: var(--gap-24);
            }
        }

        &-heading {
            @mixin headline-system_small;

            max-width: 100%;

            @media (--tablet-m) {
                @mixin headline-system_medium;
            }
        }

        &-title {
            @mixin headline-system_small;
            margin-bottom: var(--gap-24);

            @media (--tablet-m) {
                @mixin headline-system_medium;
                margin-bottom: var(--gap-40);
            }
        }

        &-benefits {
            display: flex;
            flex-direction: column;
            flex-wrap: nowrap;

            @media (--tablet-m) {
                flex-wrap: wrap;
                flex-direction: row;
            }

            &-card {
                display: flex;
                flex-direction: column;
                height: 120px;
                width: 100%;
                background-color: var(--color-light-bg-primary);
                border-radius: var(--border-radius-8);
                padding: var(--gap-24);
                margin-right: var(--gap-24);
                margin-top: var(--gap-24);
                box-shadow: var(--shadow-xs-hard);

                @media (--tablet-m) {
                    max-width: 340px;
                    width: calc(50% - var(--gap-xl));
                    height: 120px;
                    flex-grow: 1;
                }

                @media (--desktop-m) {
                    height: 144px;
                    width: 243px;
                }

                &-text {
                    @mixin accent_primary_medium;
                    max-width: 100%;
                    margin-top: var(--gap-16);

                    @media (--desktop-m) {
                        max-width: 70%;
                    }
                }

                &:nth-child(2) {
                    @media (--tablet-m) {
                        margin-right: 0;
                    }

                    @media (--desktop-m) {
                        margin-right: var(--gap-24);
                    }
                }

                &:last-child {
                    margin-right: 0;
                }
            }

            &-icon {
                height: 32px;
                color: var(--color-light-graphic-secondary);
            }
        }
    }

    &__connect {
        display: flex;
        flex-direction: column;

        @media (--desktop-m) {
            flex-direction: row;
        }

        &-card {
            width: 100%;
            height: 100%;
            background-color: var(--color-light-bg-primary);
            border-radius: var(--border-radius-8);
            padding: var(--gap-24) var(--gap-16);
            margin-right: 0;
            margin-bottom: var(--gap-24);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-shadow: var(--shadow-xs-hard);

            @media (--desktop-m) {
                width: 510px;
                height: 444px;
                margin-right: var(--gap-24);
                margin-bottom: 0;
                padding: var(--gap-32);
            }

            &-title {
                @mixin headline-system_xsmall;
                margin-bottom: var(--gap-24);

                @media (--tablet-m) {
                    @mixin headline-system_small;
                }
            }

            &-footer {
                display: flex;
                flex-direction: column;
                align-items: start;
                margin-top: var(--gap-24);

                &-text {
                    color: var(--color-light-text-secondary);
                }

                @media (--desktop-m) {
                    margin-top: 0;
                }
            }

            &-button {
                margin-top: var(--gap-16);
                width: 100%;

                @media (--tablet-m) {
                    width: auto;
                }
            }

            &:last-child {
                margin-right: 0;
                margin-bottom: 0;
            }
        }
    }

    &__phone-number {
        white-space: nowrap;
        text-decoration: none;
        color: var(--color-light-text-primary);
    }

    &__steps-list {
        max-width: 685px;
        margin-bottom: var(--gap-24);

        &-text {
            color: var(--color-light-text-secondary);
        }

        @media (--tablet-m) {
            margin-bottom: var(--gap-40);
        }

        .ordered-instructions-list-item {
            flex-direction: column;

            @media (--tablet-m) {
                flex-direction: row;
            }

            &:before {
                background-color: var(--color-light-graphic-primary);
                color: var(--color-light-text-primary-inverted);

                @mixin paragraph_secondary_medium;
                width: 20px;
                height: 20px;
                margin-bottom: var(--gap-16);
                text-align: center;
                content: counter(number);
                border-radius: var(--border-radius-circle);
                line-height: 20px;
            }

            &:after {
                display: none;

                @media (--tablet-m) {
                    display: inline-block;
                    left: 9px;
                }
            }

            &:last-child:after {
                display: none;
            }
        }
    }

    &__dash {
        color: var(--color-light-graphic-accent);
    }
}
