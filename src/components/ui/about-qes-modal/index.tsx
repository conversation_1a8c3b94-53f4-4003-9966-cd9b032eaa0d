import React from 'react';
import { createCn } from 'bem-react-classname';

import { Alert } from '@alfalab/core-components/alert';
import { Button } from '@alfalab/core-components/button';
import { Link } from '@alfalab/core-components/link';
import { List } from '@alfalab/core-components/list';
import { Modal } from '@alfalab/core-components/modal';
import { type ModalResponsiveProps } from '@alfalab/core-components/modal/typings';
import { Space } from '@alfalab/core-components/space';
import { Typography } from '@alfalab/core-components/typography';
import { BankMIcon } from '@alfalab/icons-glyph/BankMIcon';
import { DesktopMIcon } from '@alfalab/icons-glyph/DesktopMIcon';
import { DocumentLineMIcon } from '@alfalab/icons-glyph/DocumentLineMIcon';
import { EagleMIcon } from '@alfalab/icons-glyph/EagleMIcon';
import OrderedInstructionsList from 'arui-private/ordered-instructions-list';

import FnsImg from './images/fns.svg';
import {
    cryptoProUrl,
    fnsDepartmentListUrl,
    fnsUrl,
    infotecsUrl,
    osnovanieApplyUrl,
    osnovaniePriceUrl,
    ucDepartmentUrl,
    ucFnsUrl,
} from './constants';

import './style.css';

export const cn = createCn('about-qes-modal');

// TODO Вынести этот компонент в arui-private
const AboutQESModal = ({ open, onClose }: Partial<ModalResponsiveProps>) => (
    <Modal
        open={!!open}
        size='l'
        onClose={onClose}
        className={cn()}
        zIndex={10000}
        contentClassName={cn('main')}
    >
        <div className={cn('header')}>
            <div>
                <Typography.TitleResponsive
                    view='medium'
                    tag='h3'
                    font='system'
                    className={cn('header-heading')}
                >
                    Электронная подпись от&nbsp;ФНС России
                </Typography.TitleResponsive>
                <div className={cn('header-description')}>
                    С&nbsp;2022 года руководители&nbsp;ЮЛ и&nbsp;ИП могут получить квалифицированную
                    электронную подпись только в&nbsp;удостоверяющем центре (УЦ) ФНС или у&nbsp;его
                    доверенных лиц
                </div>
            </div>
            <figure className={cn('header-figure')}>
                <img
                    src={FnsImg}
                    alt='иллюстрация фнс'
                    className={cn('header-figure-img')}
                    width='395px'
                    height='234px'
                />
            </figure>
        </div>
        <div className={cn('content')}>
            <div className={cn('content-section', { grey: true, 'padding-top': true })}>
                <Typography.TitleResponsive
                    view='medium'
                    tag='h3'
                    font='system'
                    className={cn('content-heading')}
                >
                    Для чего пригодится
                </Typography.TitleResponsive>
                <div className={cn('content-benefits')}>
                    <div className={cn('content-benefits-card')}>
                        <div className={cn('content-benefits-icon')}>
                            <DesktopMIcon width={32} height={32} />
                        </div>
                        <Typography.Text
                            view='primary-medium'
                            weight='bold'
                            tag='span'
                            className={cn('content-benefits-card-text')}
                        >
                            Отчётность онлайн
                        </Typography.Text>
                    </div>
                    <div className={cn('content-benefits-card')}>
                        <div className={cn('content-benefits-icon')}>
                            <DocumentLineMIcon width={32} height={32} />
                        </div>
                        <Typography.Text
                            view='primary-medium'
                            weight='bold'
                            tag='span'
                            className={cn('content-benefits-card-text')}
                        >
                            Электронные документы
                        </Typography.Text>
                    </div>
                    <div className={cn('content-benefits-card')}>
                        <div className={cn('content-benefits-icon')}>
                            <EagleMIcon width={32} height={32} />
                        </div>
                        <Typography.Text
                            view='primary-medium'
                            weight='bold'
                            tag='span'
                            className={cn('content-benefits-card-text')}
                        >
                            Государственные порталы
                        </Typography.Text>
                    </div>
                    <div className={cn('content-benefits-card')}>
                        <div className={cn('content-benefits-icon')}>
                            <BankMIcon width={32} height={32} />
                        </div>
                        <Typography.Text
                            view='primary-medium'
                            weight='bold'
                            tag='span'
                            className={cn('content-benefits-card-text')}
                        >
                            Торговые площадки
                        </Typography.Text>
                    </div>
                </div>
            </div>
            <div className={cn('content-section')}>
                <Typography.Title
                    view='medium'
                    font='system'
                    tag='h3'
                    className={cn('content-title')}
                >
                    Как получить и&nbsp;начать использовать
                </Typography.Title>
                <OrderedInstructionsList className={cn('steps-list')}>
                    <Space size='m'>
                        <Typography.Title view='xsmall' font='system' tag='h4'>
                            Приобрести ключевой носитель («токен», «флешка»)
                        </Typography.Title>
                        <Typography.Text
                            view='primary-medium'
                            tag='p'
                            className={cn('steps-list-text')}
                        >
                            Подойдут любые сертифицированные ФСТЭК или ФСБ России, например: Рутокен
                            ЭЦП 2.0, Рутокен S, Рутокен Lite, JaCarta ГОСТ, JaCarta-2 ГОСТ, JaCarta
                            LT, ESMART Token, ESMART Token ГОСТ и&nbsp;т.п.
                        </Typography.Text>
                    </Space>
                    <Space size='m'>
                        <Typography.Title view='xsmall' font='system' tag='h4'>
                            Подать заявление на&nbsp;получение ЭЦП ФНС
                        </Typography.Title>
                        <Typography.Text
                            view='primary-medium'
                            tag='p'
                            className={cn('steps-list-text')}
                        >
                            Подать заявление можно в&nbsp;личном кабинете ЮЛ/ИП ФНС или
                            в&nbsp;личном кабинете доверенного лица ФНС
                        </Typography.Text>
                    </Space>
                    <Space size='m'>
                        <Typography.Title view='xsmall' font='system' tag='h4'>
                            Посетить отделение УЦ ФНС или доверенного лица УЦ ФНС
                        </Typography.Title>
                        <Typography.Text
                            view='primary-medium'
                            tag='p'
                            className={cn('steps-list-text')}
                        >
                            ЭЦП ФНС выдаётся в&nbsp;одном экземпляре лично руководителю ЮЛ/ИП
                            (должен быть указан в&nbsp;ЕГРЮЛ/ЕГРИП) после очной идентификации. При
                            себе необходимо иметь: паспорт, СНИЛС и&nbsp;токен. Получить ЭЦП ФНС
                            по&nbsp;доверенности нельзя
                        </Typography.Text>
                    </Space>

                    <Space size='m'>
                        <Typography.Title view='xsmall' font='system' tag='h4'>
                            Настроить компьютер
                        </Typography.Title>
                        <div>
                            <Typography.Text
                                view='primary-medium'
                                tag='p'
                                className={cn('steps-list-text')}
                            >
                                На&nbsp;компьютере должно быть установлено программное обеспечение
                                для работы с&nbsp;электронной подписью одного из&nbsp;2-х
                                поставщиков:
                            </Typography.Text>
                            <List tag='ul'>
                                <Typography.Text
                                    view='primary-medium'
                                    className={cn('steps-list-text')}
                                >
                                    ООО &laquo;Криптопро&raquo; доступно{' '}
                                    <Link view='default' href={cryptoProUrl} target='_blank'>
                                        по&nbsp;ссылке
                                    </Link>
                                </Typography.Text>
                                <Typography.Text
                                    view='primary-medium'
                                    className={cn('steps-list-text')}
                                >
                                    АО&nbsp;&laquo;ИнфоТеКС&raquo; доступно{' '}
                                    <Link view='default' href={infotecsUrl} target='_blank'>
                                        по&nbsp;ссылке
                                    </Link>
                                </Typography.Text>
                            </List>
                        </div>
                    </Space>
                    <Space size='m'>
                        <Typography.Title view='xsmall' font='system' tag='h4'>
                            Подключить ЭЦП в&nbsp;Альфа-Банке
                        </Typography.Title>
                        <Typography.Text
                            view='primary-medium'
                            tag='p'
                            className={cn('steps-list-text')}
                        >
                            Перед началом использования квалифицированной электронной подписи
                            в&nbsp;Альфа-Банке подключите её&nbsp;на&nbsp;странице &laquo;Способы
                            подписания&raquo; в&nbsp;Альфа-Бизнес Онлайн
                        </Typography.Text>
                    </Space>
                </OrderedInstructionsList>
                <Alert>
                    Более подробную информацию о&nbsp;процессе получения ЭЦП ФНС можно получить{' '}
                    <Link view='default' href={ucFnsUrl} target='_blank'>
                        на&nbsp;сайте ФНС
                    </Link>
                </Alert>
            </div>
            <div className={cn('content-section', { grey: true, 'border-radius': true })}>
                <div className={cn('connect')}>
                    <div className={cn('connect-card')}>
                        <Typography.Title
                            view='small'
                            font='system'
                            tag='h4'
                            className={cn('connect-card-title')}
                        >
                            Получить ЭЦП ФНС в&nbsp;УЦ&nbsp;ФНС
                        </Typography.Title>

                        <List
                            tag='ul'
                            className={cn('connect-card-list')}
                            marker={<div className={cn('dash')}>—</div>}
                        >
                            <Typography.Text view='primary-medium'>
                                Бесплатная выдача ЭЦП, включая программные продукты КриптоПро
                            </Typography.Text>
                            <Typography.Text view='primary-medium'>
                                Заявление будет заполнено автоматически в&nbsp;личном кабинете ФНС
                            </Typography.Text>
                            <Typography.Text view='primary-medium'>
                                Получение ЭЦП ФНС возможно только в&nbsp;следующих{' '}
                                <Link view='default' href={fnsDepartmentListUrl} target='_blank'>
                                    отделениях ФНС
                                </Link>
                            </Typography.Text>
                        </List>
                        <div className={cn('connect-card-footer')}>
                            <Typography.Text
                                view='primary-small'
                                tag='span'
                                className={cn('connect-card-footer-text')}
                            >
                                Выберите личный кабинет ФНС в&nbsp;соответствии с
                                организационно-правовой формой
                            </Typography.Text>
                            <Button
                                view='tertiary'
                                size='m'
                                className={cn('connect-card-button')}
                                href={fnsUrl}
                                target='_blank'
                            >
                                Перейти на&nbsp;сайт ФНС
                            </Button>
                        </div>
                    </div>
                    <div className={cn('connect-card')}>
                        <div>
                            <Typography.Title
                                view='small'
                                font='system'
                                tag='h4'
                                className={cn('connect-card-title')}
                            >
                                Получить ЭЦП ФНС у&nbsp;доверенного лица УЦ&nbsp;ФНС
                            </Typography.Title>
                            <List
                                tag='ul'
                                className={cn('connect-card-list')}
                                marker={<div className={cn('dash')}>—</div>}
                            >
                                <Typography.Text view='primary-medium'>
                                    В&nbsp;стоимость услуги включены программные продукты КриптоПро,
                                    более подробно с&nbsp;тарифами можно ознакомиться{' '}
                                    <Link view='default' href={osnovaniePriceUrl} target='_blank'>
                                        здесь
                                    </Link>
                                </Typography.Text>
                                <Typography.Text view='primary-medium'>
                                    Круглосуточная линия технической поддержки{' '}
                                    <a href='tel:88001014140' className={cn('phone-number')}>
                                        8&nbsp;800&nbsp;101 41&nbsp;40
                                    </a>
                                </Typography.Text>
                                <Typography.Text view='primary-medium'>
                                    <Link view='default' href={ucDepartmentUrl} target='_blank'>
                                        Список отделений
                                    </Link>{' '}
                                    доверенного лица УЦ&nbsp;ФНС
                                </Typography.Text>
                            </List>
                        </div>
                        <div className={cn('connect-card-footer')}>
                            <Typography.Text
                                view='primary-small'
                                tag='span'
                                className={cn('connect-card-footer-text')}
                            >
                                АО&nbsp;&laquo;Аналитический центр&raquo; является доверенным лицом
                                УЦ&nbsp;ФНС
                            </Typography.Text>
                            <Button
                                view='tertiary'
                                size='m'
                                className={cn('connect-card-button')}
                                href={osnovanieApplyUrl}
                                target='_blank'
                            >
                                Подать заявку
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Modal>
);

export default AboutQESModal;
