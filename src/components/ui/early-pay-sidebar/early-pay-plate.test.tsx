import React from 'react';
import { render } from '@testing-library/react';
import { parseISO } from 'date-fns';

import { earlyPaymentTypes } from '#/src/types/early-pay-sidebar';
import { millisecondsToUnixEpoch } from '#/src/utils/date';

import EarlyPayPlate from './early-pay-plate';

Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

describe('EarlyPayPlate', () => {
    const baseAccount = {
        mainInfo: {},
        currency: {},
        specConditions: [],
        clientInfo: {},
    };

    it('should render plate about same date', () => {
        const sameDates = {
            closestFuturePaymentDate: millisecondsToUnixEpoch(+parseISO('2014-02-11')),
            paymentDate: '11.02.2014',
        };

        const anyAccount = {
            ...baseAccount,
            balance: { total: 100 },
        };

        const accountWithNegativeBalance = {
            ...baseAccount,
            balance: { total: -1 },
        };

        const cases: Array<[earlyPaymentTypes, string]> = [
            [earlyPaymentTypes.FULL, 'same-date-plate-payment-full'],
            [earlyPaymentTypes.PARTIAL, 'same-date-plate-payment-partial'],
        ];

        for (const [paymentType, testId] of cases) {
            for (const account of [anyAccount, accountWithNegativeBalance]) {
                const { container } = render(
                    <EarlyPayPlate
                        paymentType={paymentType}
                        {...sameDates}
                        selectedAccount={account}
                        total={0}
                    />,
                );

                const el = container.querySelector(`[data-test-id="${testId}"]`);

                expect(el).toBeInTheDocument();
            }
        }
    });

    it('should render plate about low balance', () => {
        const differentDates = {
            closestFuturePaymentDate: millisecondsToUnixEpoch(+parseISO('2014-02-11')),
            paymentDate: '2015-03-10',
        };

        const positiveTotal = 1;

        const accountCases = [
            { ...baseAccount, balance: { total: 0 } },
            { ...baseAccount, balance: { total: -1 } },
            { ...baseAccount, balance: { total: (positiveTotal / 2) * 100 } },
        ];

        for (const account of accountCases) {
            const { container } = render(
                <EarlyPayPlate
                    paymentType={earlyPaymentTypes.FULL}
                    {...differentDates}
                    selectedAccount={account}
                    total={positiveTotal}
                />,
            );

            const el = container.querySelector('[data-test-id="low-balance-plate"]');

            expect(el).toBeInTheDocument();
        }
    });

    it('should render nothing', () => {
        const propsList = [
            {
                selectedAccount: undefined,
                total: 0,
                closestFuturePaymentDate: millisecondsToUnixEpoch(+parseISO('2014-02-11')),
                paymentDate: '2014-02-11',
            },
            {
                selectedAccount: undefined,
                total: 0,
                closestFuturePaymentDate: millisecondsToUnixEpoch(+parseISO('2014-02-11')),
                paymentDate: '',
            },
            {
                selectedAccount: {
                    ...baseAccount,
                    balance: { total: 0 },
                },
                total: 0,
                closestFuturePaymentDate: millisecondsToUnixEpoch(+parseISO('2014-02-11')),
                paymentDate: '2015-03-10',
            },
            {
                selectedAccount: {
                    ...baseAccount,
                    balance: { total: 200 },
                },
                total: 1,
                closestFuturePaymentDate: millisecondsToUnixEpoch(+parseISO('2014-02-11')),
                paymentDate: '2015-03-10',
            },
        ];

        for (const props of propsList) {
            const { container } = render(
                <EarlyPayPlate {...props} paymentType={earlyPaymentTypes.FULL} />,
            );

            expect(container.childElementCount).toBe(0);
        }
    });
});
