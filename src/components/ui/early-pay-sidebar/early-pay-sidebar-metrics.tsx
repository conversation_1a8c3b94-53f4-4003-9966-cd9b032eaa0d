import { useEffect, useMemo, useRef } from 'react';
import { useSelector } from 'react-redux';

import { earlyRepaymentProcessingTypeSelector } from '#/src/ducks/credit-processing/selectors';
import { EARLY_REPAYMENT_METRICS } from '#/src/metrics';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

export const useEarlyPaySidebarMetrics = (agreementNumber: string, isVisible: boolean) => {
    const trackAlfaMetrics = useTrackAlfaMetrics();
    const isFirstRenderRef = useRef(true);
    const processingType = useSelector(earlyRepaymentProcessingTypeSelector);

    useEffect(() => {
        if (
            isFirstRenderRef.current === true &&
            !!agreementNumber &&
            !!processingType[agreementNumber] &&
            isVisible
        ) {
            isFirstRenderRef.current = false;

            trackAlfaMetrics(EARLY_REPAYMENT_METRICS.renderEarlyRepaymentSidebar, {
                agreementNumber,
                processingType: processingType[agreementNumber]?.toLowerCase(),
            });
        }
    }, [trackAlfaMetrics, agreementNumber, processingType, isVisible]);

    return useMemo(
        () => ({
            submit: (paymentType: 'full' | 'partial') =>
                trackAlfaMetrics(EARLY_REPAYMENT_METRICS.submitEarlyRepaymentSidebar, {
                    agreementNumber,
                    processingType: processingType[agreementNumber]?.toLowerCase(),
                    paymentType,
                }),
            validationError: (errorsMessages: string) =>
                trackAlfaMetrics(
                    EARLY_REPAYMENT_METRICS.submitValidationErrorEarlyRepaymentSidebar,
                    {
                        agreementNumber,
                        processingType: processingType[agreementNumber]?.toLowerCase(),
                        errorsMessages,
                    },
                ),
        }),
        [trackAlfaMetrics, agreementNumber, processingType],
    );
};
