import React, { useMemo } from 'react';
import { type Account } from 'corp-accounts-api-typescript-services';
import { isSameDay, parse } from 'date-fns';
import { type UnixEpoch } from 'thrift-services/utils';

import { Plate } from '@alfalab/core-components/plate';

import { earlyPaymentTypes } from '#/src/types/early-pay-sidebar';

import { DATE_FORMAT, DAY_MONTH_FORMAT, MILLISECONDS_IN_SECOND } from '../../../constants/date';
import { MDASH, NBSP } from '../../../constants/unicode-symbols';
import { dateToCustomFormat } from '../../../utils/date';
import { convertNumberToAmount } from '../../../utils/number-helpers';

type Props = {
    selectedAccount: Account | undefined;
    total: number;
    closestFuturePaymentDate: UnixEpoch;
    paymentDate: string;
    paymentType: earlyPaymentTypes;
    currentTime?: Date | null;
};

export default function EarlyPayPlate({
    selectedAccount,
    total,
    closestFuturePaymentDate,
    paymentDate,
    paymentType,
    currentTime = null,
}: Props) {
    const { isPaymentDateTheSameAsClosestFuturePaymentDate, formattedDate } = useMemo(() => {
        const closestFuturePaymentString = String(
            closestFuturePaymentDate
                ? closestFuturePaymentDate.seconds * MILLISECONDS_IN_SECOND
                : 0,
        );
        const parsedClosestFuturePaymentDate = parse(closestFuturePaymentString, 'T', new Date());
        let parsedPaymentDate = new Date();

        try {
            parsedPaymentDate = parse(paymentDate, DATE_FORMAT, new Date());
        } catch (e) {}

        return {
            isPaymentDateTheSameAsClosestFuturePaymentDate: isSameDay(
                parsedClosestFuturePaymentDate,
                parsedPaymentDate,
            ),
            formattedDate: dateToCustomFormat(currentTime, parsedPaymentDate, DAY_MONTH_FORMAT),
        };
    }, [closestFuturePaymentDate.seconds, currentTime, paymentDate]);

    if (isPaymentDateTheSameAsClosestFuturePaymentDate) {
        if (paymentType === earlyPaymentTypes.FULL) {
            return (
                <Plate view='common' dataTestId='same-date-plate-payment-full' rounded={false}>
                    {`До${NBSP}${formattedDate} убедитесь, что на${NBSP}счёте достаточно средств для полного погашения`}
                </Plate>
            );
        }
        if (paymentType === earlyPaymentTypes.PARTIAL) {
            return (
                <Plate view='common' dataTestId='same-date-plate-payment-partial' rounded={false}>
                    {`Дата досрочного погашения совпадает с${NBSP}плановым платежом${NBSP}${MDASH} до${NBSP}${formattedDate} нужно обеспечить наличие средств на${NBSP}счёте для обоих платежей`}
                </Plate>
            );
        }
    }

    if (selectedAccount) {
        const { balance } = selectedAccount;
        const { amount } = convertNumberToAmount(total);

        if (amount !== undefined && (balance?.total || 0) < amount) {
            return (
                <Plate dataTestId='low-balance-plate' view='common' rounded={false}>
                    Для досрочного погашения не&nbsp;хватает средств, внесите на&nbsp;счёт
                    необходимую сумму
                </Plate>
            );
        }
    }

    return null;
}
