import React, { useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import format from 'date-fns/format';
import isSameDay from 'date-fns/isSameDay';
import { type UnixEpoch } from 'thrift-services/utils';

import { Calendar } from '@alfalab/core-components/calendar';
import { UniversalDateInput } from '@alfalab/core-components/universal-date-input';
import { usePrevious } from '@alfalab/hooks';

import { DATE_FORMAT } from '#/src/constants/date';
import { isAutomaticEarlyRepaymentProcessingTypeSelector } from '#/src/ducks/credit-processing/selectors';
import { isKIBCategoryCodeSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { isMmbCategorySelector } from '#/src/ducks/organization/selectors';
import { futurePaymentsSelector } from '#/src/ducks/payment-schedule/selectors';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { earlyPaymentTypes } from '#/src/types/early-pay-sidebar';
import {
    calculateMaxDate,
    calculateMinDate,
    getClosestFuturePaymentDate,
} from '#/src/utils/early-pay';

type Props = {
    docNumber: string;
    setPaymentDate:
        | ((formattedValue?: string, value?: number) => void)
        | ((formattedValue?: string) => void);
    paymentType: earlyPaymentTypes;
    toDate?: UnixEpoch;
    offDays: number[];
    paymentDate: string;
    paymentDateError: string;
    isTodayTimeAfterDeadline: boolean;
    daysBeforeAdvancedRepay: number;
};

export const EarlyPaySidebarCalendarInput = ({
    docNumber,
    setPaymentDate,
    paymentType,
    toDate = { seconds: 0 },
    offDays,
    paymentDate,
    paymentDateError,
    isTodayTimeAfterDeadline,
    daysBeforeAdvancedRepay,
}: Props) => {
    const currentTime = useSelector(currentTimeSelector);
    const futurePayments = useSelector(futurePaymentsSelector);
    const isAutomaticProcessingType = useSelector(
        isAutomaticEarlyRepaymentProcessingTypeSelector(docNumber),
    );
    const isKIBCategoryCode = useSelector(isKIBCategoryCodeSelector);
    const isMmbCategory = useSelector(isMmbCategorySelector);
    const isComputedPaymentDate =
        isAutomaticProcessingType && paymentType === earlyPaymentTypes.FULL;

    const calendarProps = useMemo(() => {
        const closestFuturePaymentDate = getClosestFuturePaymentDate(
            currentTime,
            futurePayments,
            toDate,
        );
        const isOffDay = (date: number) => offDays.some((offDay) => isSameDay(offDay, date));

        const maxDate = calculateMaxDate({
            isAutomaticProcessingType,
            paymentType,
            closestFuturePaymentDate,
            currentTime,
            toDate,
        });

        const minDate = calculateMinDate({
            currentTime,
            maxDate,
            daysBeforeAdvancedRepay: isKIBCategoryCode ? daysBeforeAdvancedRepay : 0,
            isTodayTimeAfterDeadline,
            isAutomaticProcessingType,
            isMmbCategory,
            isOffDay,
        });

        return {
            minDate,
            maxDate,
            offDays,
        };
    }, [
        currentTime,
        futurePayments,
        toDate,
        isAutomaticProcessingType,
        paymentType,
        isKIBCategoryCode,
        daysBeforeAdvancedRepay,
        isTodayTimeAfterDeadline,
        isMmbCategory,
        offDays,
    ]);

    const prevValues = usePrevious({
        minDate: calendarProps.minDate,
        isComputedPaymentDate,
    });

    const onCalendarChangeHandler = (
        _event:
            | React.ChangeEvent<HTMLInputElement>
            | React.MouseEvent<HTMLButtonElement, globalThis.MouseEvent>
            | null,
        { value }: { value: string },
    ) => {
        setPaymentDate(value);
    };

    useEffect(() => {
        if (
            !prevValues ||
            (paymentDate && calendarProps.minDate !== prevValues.minDate) ||
            prevValues.isComputedPaymentDate !== isComputedPaymentDate
        ) {
            setPaymentDate(isComputedPaymentDate ? format(calendarProps.minDate, DATE_FORMAT) : '');
        }
    }, [isComputedPaymentDate, setPaymentDate, calendarProps.minDate, prevValues, paymentDate]);

    return (
        <UniversalDateInput
            view='date'
            picker={true}
            size='m'
            Calendar={Calendar}
            disabled={isComputedPaymentDate}
            placeholder='Дата погашения'
            value={paymentDate}
            error={paymentDateError}
            block={true}
            onInputChange={onCalendarChangeHandler}
            calendarProps={calendarProps}
            minDate={calendarProps.minDate}
            maxDate={calendarProps.maxDate}
        />
    );
};
