import React from 'react';

import { EarlyPaySidebar, type TEarlyPaySidebarProps } from './early-pay-sidebar';

export const EarlyPayContainer: React.FC<TEarlyPaySidebarProps> = ({
    processingType,
    isTranche,
    isVisible,
    docNumber,
    fromDate,
    toDate,
    debt,
    interest,
    totalLoanSum,
    accounts,
    amount,
    accountNumber,
    closestFuturePaymentDate,
    futurePayments,
    hasPrepaymentAvailability,
    calendarDisabledDates,
    daysBeforeAdvancedRepay,
    onClose,
    onSubmit,
    useEarlyPaySidebarMetrics,
    isAnnuityScheduleType,
}) =>
    isVisible ? (
        <EarlyPaySidebar
            processingType={processingType}
            isTranche={isTranche}
            isVisible={isVisible}
            daysBeforeAdvancedRepay={daysBeforeAdvancedRepay}
            docNumber={docNumber}
            fromDate={fromDate}
            toDate={toDate}
            amount={amount}
            debt={debt}
            interest={interest}
            totalLoanSum={totalLoanSum}
            accounts={accounts}
            accountNumber={accountNumber}
            closestFuturePaymentDate={closestFuturePaymentDate}
            futurePayments={futurePayments}
            hasPrepaymentAvailability={hasPrepaymentAvailability}
            calendarDisabledDates={calendarDisabledDates}
            onClose={onClose}
            onSubmit={onSubmit}
            isAnnuityScheduleType={isAnnuityScheduleType}
            useEarlyPaySidebarMetrics={useEarlyPaySidebarMetrics}
        />
    ) : null;
