/* eslint-disable complexity */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { type Account } from 'corp-accounts-api-typescript-services';
import { CURRENCY } from 'corporate-services/lib/currency';
import isEqual from 'date-fns/isEqual';
import startOfDay from 'date-fns/startOfDay';
import { type Amount } from 'thrift-services/entities';
import { type TLoanPayment } from 'thrift-services/services/credit_products';
import { type UnixEpoch } from 'thrift-services/utils';

import { Alert } from '@alfalab/core-components/alert';
import { AmountInput, type AmountInputProps } from '@alfalab/core-components/amount-input';
import { Divider } from '@alfalab/core-components/divider';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Plate } from '@alfalab/core-components/plate';
import { RadioGroup } from '@alfalab/core-components/radio-group';
import { SidePanel } from '@alfalab/core-components/side-panel';
import { Tag } from '@alfalab/core-components/tag';
import { Typography } from '@alfalab/core-components/typography';
import { CorporateAccountSelect } from 'arui-private/corporate-account-select';
import CorporateAmount from 'arui-private/corporate-amount';

import { earlyRepaymentProcessingTypes } from '#/src/constants/credit-processing';
import { type EProductCodes, showEarlyPayNotification } from '#/src/constants/credit-products';
import { DATE_FORMAT, MILLISECONDS_IN_SECOND, SPACE_DATE_FORMAT } from '#/src/constants/date';
import { NBSP } from '#/src/constants/unicode-symbols';
import { isSignSuccessSelector } from '#/src/ducks/correspondence/selectors';
import { earlyRepaymentApplicationIdSelector } from '#/src/ducks/credit-processing/selectors';
import { productCodeSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { setEarlyRepaymentData } from '#/src/ducks/early-pay/actions';
import {
    isRequestAlreadyCreatedErrorModalOpenSelector,
    isSigningErrorModalOpenSelector,
} from '#/src/ducks/early-pay/selectors';
import { setTimeAfterDeadlineError } from '#/src/ducks/settings/actions';
import {
    currentTimeSelector,
    deadlineForAcceptingApplicationsMoscowTimeSelector,
    isTimeAfterDeadlineErrorSelector,
} from '#/src/ducks/settings/selectors';
import { TEXTS } from '#/src/texts';
import { type EarlyPayData, earlyPaymentTypes } from '#/src/types/early-pay-sidebar';
import { dateToCustomFormat, getMoscowDate, parseDateFromAny } from '#/src/utils/date';
import { getIsTimeBeforeDeadline } from '#/src/utils/early-pay';
import {
    convertAmountToString,
    convertNumberToAmount,
    divideNumberByMinorUnits,
} from '#/src/utils/number-helpers';
import {
    getDebtToPayValidationErrors,
    getInterestToPayValidationErrors,
    getPaymentDateValidationErrors,
    getSelectedAccountNumberValidationErrors,
} from '#/src/utils/validations/early-pay-sidebar';
import { DEFAULT_AMOUNT } from '#/src/view-utils/credit-pane';

import EarlyPayPlate from './early-pay-plate';
import EarlyPaySidebarButtons from './early-pay-sidebar-buttons';
import { EarlyPaySidebarCalendarInput } from './early-pay-sidebar-calendar-input';
import cn from './early-pay-sidebar-cn';
import { useEarlyPaySidebarMetrics as useEarlyPaySidebarMetricsInjectedHook } from './early-pay-sidebar-metrics';

export type TEarlyPaySidebarProps = {
    processingType: string;
    isTranche: boolean;
    isVisible: boolean;
    docNumber?: string;
    fromDate?: UnixEpoch;
    toDate?: UnixEpoch;
    amount?: Amount;
    debt?: Amount;
    interest?: Amount;
    totalLoanSum?: Amount;
    accounts: Account[];
    accountNumber: string;
    closestFuturePaymentDate?: UnixEpoch;
    futurePayments: TLoanPayment[];
    hasPrepaymentAvailability?: boolean;
    calendarDisabledDates: number[];
    daysBeforeAdvancedRepay?: number;
    onClose: () => void;
    onSubmit: (data: EarlyPayData) => void;
    useEarlyPaySidebarMetrics?: typeof useEarlyPaySidebarMetricsInjectedHook;
    isAnnuityScheduleType?: boolean;
};

export const EarlyPaySidebar: React.FC<TEarlyPaySidebarProps> = ({
    processingType,
    isTranche,
    isVisible,
    docNumber = '',
    fromDate,
    toDate,
    debt = DEFAULT_AMOUNT,
    interest = DEFAULT_AMOUNT,
    totalLoanSum = DEFAULT_AMOUNT,
    accounts,
    accountNumber,
    closestFuturePaymentDate = { seconds: 0 },
    futurePayments,
    hasPrepaymentAvailability = false,
    calendarDisabledDates,
    daysBeforeAdvancedRepay = 0,
    onClose,
    onSubmit,
    useEarlyPaySidebarMetrics = useEarlyPaySidebarMetricsInjectedHook,
    isAnnuityScheduleType,
}) => {
    const dispatch = useDispatch();
    const currentTime = useSelector(currentTimeSelector);
    const productCode = useSelector(productCodeSelector);
    const [isMobile] = useMatchMedia('--mobile');

    const [paymentType, setPaymentType] = useState<earlyPaymentTypes>(earlyPaymentTypes.PARTIAL);
    const [paymentDateOrUndefined, setPaymentDate] = useState<string | undefined>('');
    const paymentDate = paymentDateOrUndefined || '';
    const [debtToPay, setDebtToPay] = useState<number | null>(null);
    const [interestToPay, setInterestToPay] = useState<number | null>(null);
    const [total, setTotal] = useState(0);
    const [selectedAccountNumber, setSelectedAccountNumber] = useState(accountNumber);
    const [isFetching, setIsFetching] = useState(false);

    const [paymentDateError, setPaymentDateError] = useState('');
    const [debtToPayError, setDebtToPayError] = useState('');
    const [interestToPayError, setInterestToPayError] = useState('');
    const [selectedAccountNumberError, setSelectedAccountNumberError] = useState('');
    const [isPaymentToday, setIsPaymentToday] = useState(true);

    const deadlineForAcceptingApplicationsMoscowTime = useSelector(
        deadlineForAcceptingApplicationsMoscowTimeSelector,
    );
    const earlyRepaymentApplicationId = useSelector(earlyRepaymentApplicationIdSelector);
    const isTimeAfterDeadlineError = useSelector(isTimeAfterDeadlineErrorSelector);
    const isRequestAlreadyCreatedErrorModalOpen = useSelector(
        isRequestAlreadyCreatedErrorModalOpenSelector,
    );
    const isSigningErrorModalOpen = useSelector(isSigningErrorModalOpenSelector);
    const isSignSuccess = useSelector(isSignSuccessSelector);

    const selectedAccount = useMemo(
        () => accounts.find((acc) => acc.mainInfo?.number === selectedAccountNumber),
        [selectedAccountNumber, accounts],
    );

    const totalAmount = (debtToPay || 0) + (interestToPay || 0);

    const isTodayTimeAfterDeadline = useMemo(
        () => !getIsTimeBeforeDeadline(currentTime, deadlineForAcceptingApplicationsMoscowTime),
        [currentTime, deadlineForAcceptingApplicationsMoscowTime],
    );

    const resetForm = useCallback(() => {
        setPaymentType(earlyPaymentTypes.PARTIAL);
        setPaymentDate('');
        setDebtToPay(null);
        setInterestToPay(null);
        setDebtToPayError('');
        setInterestToPayError('');
        setPaymentDateError('');
    }, []);

    const isAutomaticProcessingType = processingType === earlyRepaymentProcessingTypes.AUTO;
    const track = useEarlyPaySidebarMetrics(docNumber, isVisible);

    const offDays = useMemo(
        () =>
            calendarDisabledDates.map(
                (date) => +startOfDay(getMoscowDate(new Date(date * MILLISECONDS_IN_SECOND))),
            ),
        [calendarDisabledDates],
    );

    const formValidationErrors = useMemo(
        () => ({
            paymentDateError:
                getPaymentDateValidationErrors(
                    currentTime,
                    paymentDate,
                    offDays,
                    isAutomaticProcessingType,
                    paymentType,
                    closestFuturePaymentDate,
                ).pop() || '',
            debtToPayError:
                getDebtToPayValidationErrors(
                    divideNumberByMinorUnits(debtToPay, 100),
                    divideNumberByMinorUnits(interestToPay, 100),
                    isAutomaticProcessingType,
                    paymentType,
                    totalLoanSum,
                ).pop() || '',
            interestToPayError:
                getInterestToPayValidationErrors(
                    divideNumberByMinorUnits(interestToPay, 100),
                    divideNumberByMinorUnits(debtToPay, 100),
                    paymentType,
                ).pop() || '',
            selectedAccountNumberError:
                getSelectedAccountNumberValidationErrors([selectedAccountNumber]).pop() || '',
        }),
        [
            currentTime,
            paymentDate,
            offDays,
            isAutomaticProcessingType,
            paymentType,
            closestFuturePaymentDate,
            debtToPay,
            interestToPay,
            totalLoanSum,
            selectedAccountNumber,
        ],
    );

    const isFormValid = useMemo(
        () => Object.values(formValidationErrors).every((errorMessage) => !errorMessage),
        [formValidationErrors],
    );

    const setValidationMessages = useCallback(() => {
        let errorsMessages = '';

        if (formValidationErrors.paymentDateError) {
            setPaymentDateError(formValidationErrors.paymentDateError);
            errorsMessages = formValidationErrors.paymentDateError;
        }
        if (formValidationErrors.debtToPayError) {
            setDebtToPayError(formValidationErrors.debtToPayError);
            errorsMessages += `; ${formValidationErrors.debtToPayError}`;
        }
        if (formValidationErrors.interestToPayError) {
            setInterestToPayError(formValidationErrors.interestToPayError);
            errorsMessages += `; ${formValidationErrors.interestToPayError}`;
        }
        if (formValidationErrors.selectedAccountNumberError) {
            setSelectedAccountNumberError(formValidationErrors.selectedAccountNumberError);
            errorsMessages += `; ${formValidationErrors.selectedAccountNumberError}`;
        }

        if (track) {
            track.validationError(errorsMessages);
        }
    }, [formValidationErrors, track]);

    const handleSubmit = useCallback(() => {
        if (track) {
            track.submit(paymentType);
        }

        if (!isFormValid) {
            setValidationMessages();

            return;
        }

        const data = {
            docNumber,
            fromDate: `${dateToCustomFormat(currentTime, fromDate, SPACE_DATE_FORMAT)}${NBSP}г`,
            paymentDate,
            paymentType,
            debtToPay: convertAmountToString(
                convertNumberToAmount(divideNumberByMinorUnits(debtToPay, 100), debt?.currency),
            ),
            interestToPay: convertAmountToString(
                convertNumberToAmount(
                    divideNumberByMinorUnits(interestToPay, 100),
                    interest?.currency,
                ),
            ),
            total: total.toString(),
            selectedAccountNumber,
            currency: debt?.currency || CURRENCY.RUR,
            branchNumber: selectedAccount?.mainInfo?.branchNumber
                ? selectedAccount?.mainInfo?.branchNumber
                : '',
        };

        setIsFetching(true);
        dispatch(setEarlyRepaymentData(data));
        onSubmit(data);
    }, [
        track,
        isFormValid,
        docNumber,
        currentTime,
        fromDate,
        paymentDate,
        paymentType,
        debtToPay,
        debt?.currency,
        interestToPay,
        interest?.currency,
        total,
        selectedAccountNumber,
        selectedAccount?.mainInfo?.branchNumber,
        dispatch,
        onSubmit,
        setValidationMessages,
    ]);

    const handleAccountChange = useCallback((value) => {
        setSelectedAccountNumber(value);
    }, []);

    const handlePaymentTypeChange = useCallback((_, { value }) => {
        setPaymentType(value);
    }, []);

    const handleChangeDebtToPay = useCallback<NonNullable<AmountInputProps['onChange']>>(
        (_, { value }) => {
            setDebtToPay(value);
        },
        [setDebtToPay],
    );

    const handleChangeInterestToPay = useCallback<NonNullable<AmountInputProps['onChange']>>(
        (_, { value }) => {
            setInterestToPay(value);
        },
        [setInterestToPay],
    );

    const handleCancel = useCallback(() => {
        setIsFetching(false);
        resetForm();
        onClose();
    }, [resetForm, onClose]);

    const getSelectedCurrencyAccounts = useCallback(
        () =>
            accounts
                .filter((accountItem) => accountItem?.currency?.code === debt?.currency?.code)
                .map((account) => ({
                    number: account.mainInfo.number,
                    cropAccountNumber: false,
                    currency: {
                        mnemonicCode: account.currency.mnemonics,
                        minorUnits: account.currency.minorUnitsNumber,
                    },
                    balanceWithoutOverdraft: account.balance?.total,
                    balance: account.balance?.balance,
                })) as React.ComponentProps<typeof CorporateAccountSelect>['accounts'],
        [accounts, debt],
    );

    useEffect(() => {
        if (paymentType === earlyPaymentTypes.FULL) {
            const paymentDateParsed = parseDateFromAny(currentTime, paymentDate, DATE_FORMAT);

            const paymentsBeforePaymentDate = futurePayments.filter(
                (payment) =>
                    (payment.paymentDate?.seconds || 0) * 1000 < paymentDateParsed.valueOf(),
            );

            const closestPaymentBeforePaymentDateBalance = paymentsBeforePaymentDate.length
                ? paymentsBeforePaymentDate[paymentsBeforePaymentDate.length - 1].loanBalance
                : debt;

            if (paymentDateParsed.valueOf() < closestFuturePaymentDate.seconds * 1000) {
                setDebtToPay(debt?.amount || null);
            } else {
                setDebtToPay(closestPaymentBeforePaymentDateBalance?.amount || null);
            }

            const paymentsAfterPaymentDate = futurePayments.filter(
                (payment) =>
                    (payment.paymentDate?.seconds || 0) * 1000 >= paymentDateParsed.valueOf(),
            );
            const closestPaymentAfterPaymentDateInterest = paymentsAfterPaymentDate.length
                ? paymentsAfterPaymentDate[0].paymentInterest
                : interest;
            const isPaymentToday = isEqual(startOfDay(paymentDateParsed), startOfDay(currentTime));

            setIsPaymentToday(isPaymentToday);

            if (
                isPaymentToday &&
                paymentDateParsed.valueOf() < closestFuturePaymentDate.seconds * 1000
            ) {
                setInterestToPay(interest?.amount || null);
            } else {
                setInterestToPay(closestPaymentAfterPaymentDateInterest?.amount || null);
            }
        }
    }, [
        paymentType,
        paymentDate,
        debt,
        interest,
        closestFuturePaymentDate,
        futurePayments,
        currentTime,
    ]);

    useEffect(() => {
        if (paymentType === earlyPaymentTypes.PARTIAL) {
            setDebtToPay(null);
            setInterestToPay(null);
            setPaymentDateError('');
        }
    }, [paymentType]);

    useEffect(() => {
        setTotal(
            (debtToPay || 0) / (debt?.currency?.minorUnits || 100) +
                (interestToPay || 0) / (interest?.currency?.minorUnits || 100),
        );

        if (debtToPay || interestToPay) {
            setInterestToPayError('');
            setDebtToPayError('');
        }
    }, [debtToPay, interestToPay, debt?.currency, interest?.currency]);

    useEffect(() => {
        if (paymentDate) {
            setPaymentDateError('');
        }
    }, [paymentDate]);

    useEffect(() => {
        if (isTimeAfterDeadlineError) {
            setIsFetching(false);
            setPaymentDateError(TEXTS.earlyRepayment.timeBeforeDeadline);
            dispatch(setTimeAfterDeadlineError(false));
        }
    }, [dispatch, isTimeAfterDeadlineError, setValidationMessages]);

    useEffect(() => {
        if (
            earlyRepaymentApplicationId !== '' ||
            isRequestAlreadyCreatedErrorModalOpen ||
            isSigningErrorModalOpen ||
            isSignSuccess
        ) {
            setIsFetching(false);
            resetForm();
            onClose();
        }
    }, [
        earlyRepaymentApplicationId,
        onClose,
        resetForm,
        isRequestAlreadyCreatedErrorModalOpen,
        isSigningErrorModalOpen,
        isSignSuccess,
    ]);

    const showNotification = showEarlyPayNotification(productCode as EProductCodes);

    return (
        <SidePanel
            disableFocusLock={true}
            open={isVisible}
            onClose={handleCancel}
            dataTestId='early-pay-sidebar'
        >
            <SidePanel.Header>
                {isMobile ? (
                    <div className={cn('mobile-header-wrapper')}>
                        <Typography.Text
                            view='component'
                            weight='medium'
                            className={cn('mobile-header-text')}
                        >
                            Досрочное погашение {isTranche ? 'транша' : 'кредита'}
                        </Typography.Text>
                        <Typography.Text
                            view='primary-small'
                            color='secondary'
                            className={cn('mobile-header-text')}
                        >
                            №{docNumber}
                            {!isTranche &&
                                `${NBSP}от${NBSP}${dateToCustomFormat(
                                    currentTime,
                                    fromDate,
                                    SPACE_DATE_FORMAT,
                                )}${NBSP}г`}
                        </Typography.Text>
                    </div>
                ) : (
                    <Typography.TitleResponsive
                        view='medium'
                        tag='h3'
                        defaultMargins={false}
                        font='system'
                        color='primary'
                    >
                        Досрочное погашение {isTranche ? 'транша' : 'кредита'}
                    </Typography.TitleResponsive>
                )}
            </SidePanel.Header>

            <SidePanel.Content>
                <div data-processing-type={processingType}>
                    {!isMobile && (
                        <Typography.Text tag='div' color='secondary' className={cn('row')}>
                            №{docNumber}
                            {!isTranche &&
                                `${NBSP}от${NBSP}${dateToCustomFormat(
                                    currentTime,
                                    fromDate,
                                    SPACE_DATE_FORMAT,
                                )}${NBSP}г`}
                        </Typography.Text>
                    )}
                    <div className={cn('row')}>
                        <RadioGroup
                            value={paymentType}
                            onChange={handlePaymentTypeChange}
                            label='Тип досрочного погашения'
                            type='tag'
                            direction='horizontal'
                        >
                            <Tag value={earlyPaymentTypes.PARTIAL} size='xs' view='filled'>
                                Частичное
                            </Tag>
                            <Tag value={earlyPaymentTypes.FULL} size='xs' view='filled'>
                                Полное
                            </Tag>
                        </RadioGroup>
                    </div>
                    <div className={cn('row')}>
                        <CorporateAccountSelect
                            showSearch={false}
                            block={true}
                            zIndexPopover={100}
                            accounts={getSelectedCurrencyAccounts()}
                            disabled={isAutomaticProcessingType}
                            selected={selectedAccountNumber}
                            error={selectedAccountNumberError}
                            onChange={handleAccountChange}
                            dataTestId={
                                isAutomaticProcessingType
                                    ? 'corporate-account-item_disabled'
                                    : 'corporate-account-item'
                            }
                            label={null}
                            placeholder='Счет списания'
                        />
                    </div>
                    <div className={cn('row')}>
                        <EarlyPayPlate
                            selectedAccount={selectedAccount}
                            total={total}
                            paymentDate={paymentDate}
                            closestFuturePaymentDate={closestFuturePaymentDate}
                            paymentType={paymentType}
                        />
                    </div>
                    <div className={cn('row')}>
                        <EarlyPaySidebarCalendarInput
                            docNumber={docNumber}
                            toDate={toDate}
                            setPaymentDate={setPaymentDate}
                            paymentType={paymentType}
                            offDays={offDays}
                            paymentDate={paymentDate}
                            paymentDateError={paymentDateError}
                            isTodayTimeAfterDeadline={isTodayTimeAfterDeadline}
                            daysBeforeAdvancedRepay={daysBeforeAdvancedRepay}
                        />
                        {!paymentDateError && (
                            <Typography.Text
                                view='component-secondary'
                                color='secondary'
                                className={cn('input-label')}
                                tag='p'
                            >
                                {TEXTS.earlyRepayment.timeBeforeDeadline}
                            </Typography.Text>
                        )}
                    </div>

                    {!isAnnuityScheduleType &&
                        (!isAutomaticProcessingType || paymentType === earlyPaymentTypes.FULL) && (
                            <React.Fragment>
                                <div className={cn('row')}>
                                    <AmountInput
                                        size='m'
                                        currency={
                                            interest?.currency
                                                ?.mnemonicCode as React.ComponentProps<
                                                typeof AmountInput
                                            >['currency']
                                        }
                                        suffix={interest?.currency?.mnemonicCode || ''}
                                        block={true}
                                        bold={false}
                                        label='Сумма процентов'
                                        value={interestToPay}
                                        error={interestToPayError}
                                        onChange={handleChangeInterestToPay}
                                        disabled={paymentType === earlyPaymentTypes.FULL}
                                        transparentMinor={false}
                                    />
                                </div>
                                {paymentType === earlyPaymentTypes.FULL && !isPaymentToday && (
                                    <div className={cn('row')}>
                                        <Alert view='attention'>
                                            Сумма процентов взята из&nbsp;ближайшего планового
                                            платежа по&nbsp;графику. В&nbsp;день досрочного
                                            погашения спишется меньшая сумма процентов, так как
                                            будет произведён пересчёт.
                                        </Alert>
                                    </div>
                                )}
                            </React.Fragment>
                        )}

                    <div className={cn('row')}>
                        <AmountInput
                            size='m'
                            currency={
                                debt?.currency?.mnemonicCode as React.ComponentProps<
                                    typeof AmountInput
                                >['currency']
                            }
                            suffix={debt?.currency?.mnemonicCode || ''}
                            block={true}
                            bold={false}
                            label='Сумма основного долга'
                            value={debtToPay}
                            error={debtToPayError}
                            onChange={handleChangeDebtToPay}
                            disabled={paymentType === earlyPaymentTypes.FULL}
                            transparentMinor={false}
                        />
                    </div>
                </div>
            </SidePanel.Content>

            <SidePanel.Footer>
                <div className={cn('footer-wrapper')}>
                    <Divider className={cn('row')} />
                    <div className={cn('amount')}>
                        <Typography.Text view='primary-medium' color='primary'>
                            Общая сумма
                        </Typography.Text>
                        <Typography.Title tag='div' view='medium' font='system' color='primary'>
                            <CorporateAmount
                                amount={{ amount: totalAmount, currency: CURRENCY.RUR }}
                                bold='full'
                                transparentMinor={false}
                            />
                        </Typography.Title>
                    </div>
                    {showNotification && (
                        <Plate
                            className={cn('row')}
                            view='attention'
                            title='Обратите внимание'
                            rounded={false}
                        >
                            Частичное погашение не отменяет ближайший платеж по графику, нужно
                            обеспечить наличие необходимых средств на счете
                        </Plate>
                    )}
                    {isAutomaticProcessingType && (
                        <Plate className={cn('row')} view='common' rounded={false}>
                            Если у вас на одном счету есть несколько кредитных продуктов, то
                            досрочное погашение будет списываться в последнюю очередь. Приоритет — у
                            плановых платежей по кредитам и овердрафта
                        </Plate>
                    )}
                    <EarlyPaySidebarButtons
                        docNumber={docNumber}
                        isLoading={isFetching}
                        isTodayTimeAfterDeadline={isTodayTimeAfterDeadline}
                        handleSubmit={handleSubmit}
                        currentDate={currentTime}
                        hasPrepaymentAvailability={hasPrepaymentAvailability}
                    />
                </div>
            </SidePanel.Footer>
        </SidePanel>
    );
};
