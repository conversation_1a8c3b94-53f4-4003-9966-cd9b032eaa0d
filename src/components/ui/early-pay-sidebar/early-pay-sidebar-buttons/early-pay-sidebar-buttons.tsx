import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import isSameDay from 'date-fns/isSameDay';

import { Button } from '@alfalab/core-components/button';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { Plate } from '@alfalab/core-components/plate';

import { NBSP } from '#/src/constants/unicode-symbols';
import { isAutomaticEarlyRepaymentProcessingTypeSelector } from '#/src/ducks/credit-processing/selectors';
import { closestFuturePaymentDateSelector } from '#/src/ducks/payment-schedule/selectors';
import { signModuleLoadStart } from '#/src/ducks/sign-module/actions';
import { isSignModuleLoadedSelector } from '#/src/ducks/sign-module/selectors';
import { parseUnixEpochDate } from '#/src/utils/date';

import cn from '../early-pay-sidebar-cn';

type Props = {
    docNumber: string;
    isTodayTimeAfterDeadline: boolean;
    handleSubmit: () => void;
    currentDate: Date;
    hasPrepaymentAvailability: boolean;
    isLoading?: boolean;
};

const EarlyPaySidebarButtons = ({
    docNumber,
    isTodayTimeAfterDeadline,
    handleSubmit,
    currentDate,
    hasPrepaymentAvailability,
    isLoading = false,
}: Props) => {
    const dispatch = useDispatch();
    const isSignModuleLoaded = useSelector(isSignModuleLoadedSelector);
    const closestFuturePaymentDate = useSelector(closestFuturePaymentDateSelector);
    const isAutomaticProcessingType = useSelector(
        isAutomaticEarlyRepaymentProcessingTypeSelector(docNumber),
    );

    useEffect(() => {
        if (!isSignModuleLoaded) {
            dispatch(signModuleLoadStart());
        }
    }, [dispatch, isSignModuleLoaded]);

    const infoMessage = hasPrepaymentAvailability
        ? ''
        : `Штраф за${NBSP}досрочное погашение будет рассчитан согласно условиям
            договора. Сумму для списания штрафа необходимо обеспечить на${NBSP}счете
            в${NBSP}дату досрочного погашения.`;

    const submitDisabledReason =
        isAutomaticProcessingType &&
        isTodayTimeAfterDeadline &&
        closestFuturePaymentDate &&
        isSameDay(parseUnixEpochDate(closestFuturePaymentDate), currentDate)
            ? `Сегодня будет выполнен плановый платёж. После этого вы${NBSP}сможете досрочно погасить кредит.`
            : '';

    const isSubmitDisabled = Boolean(submitDisabledReason);

    return (
        <GenericWrapper column={true}>
            {!!infoMessage && (
                <Plate
                    view='common'
                    className={cn('row')}
                    data-test-id='submit-button-info'
                    rounded={false}
                >
                    {infoMessage}
                </Plate>
            )}
            {isSubmitDisabled && (
                <Plate
                    view='attention'
                    className={cn('row')}
                    data-test-id='submit-button-disabled-reason'
                    rounded={false}
                >
                    {submitDisabledReason}
                </Plate>
            )}
            <Button
                loading={isLoading}
                view='primary'
                size='s'
                onClick={handleSubmit}
                disabled={isSubmitDisabled}
                nowrap={true}
                block={true}
            >
                Подписать и отправить
            </Button>
        </GenericWrapper>
    );
};

export default EarlyPaySidebarButtons;
