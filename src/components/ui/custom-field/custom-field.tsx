import React from 'react';
import { createCn } from 'bem-react-classname/create-cn';

import { FilterTag } from '@alfalab/core-components/filter-tag';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { type FieldProps } from '@alfalab/core-components/select/typings';
import { type FilterCompanySelectProps } from 'arui-private/filter-company-select';

import './custom-field.css';

const cn = createCn('custom-field');

export type View = Exclude<FilterCompanySelectProps['singleFieldView'], 'default'>;

type CustomFieldProps = FieldProps & {
    componentView?: View;
};

export const CustomField = ({
    label,
    selected,
    innerProps: { ref, ...restInnerProps },
    componentView,
    ...restProps
}: CustomFieldProps) => {
    const [isDesktop] = useMatchMedia('--desktop-s');

    const content = selected?.value || selected?.content;

    const hasNoLabel = restProps?.hasNoLabel;
    const withoutLabelInFilterContent = restProps?.withoutLabelInFilterContent;

    const checkedContent = (
        <span>
            {!!label && `${label}: `}
            {label ? <b>{content}</b> : content}
        </span>
    );

    const getContent = () => {
        if (hasNoLabel && content) {
            return content;
        }

        if (withoutLabelInFilterContent && content) {
            return content;
        }

        return selected ? checkedContent : label;
    };

    return (
        <FilterTag
            checked={!!selected?.content}
            {...restInnerProps}
            {...restProps}
            view='filled'
            shape='rectangular'
            size={isDesktop ? 40 : 32}
            className={cn({ compact: componentView === 'compact' })}
            ref={ref}
        >
            {getContent()}
        </FilterTag>
    );
};
