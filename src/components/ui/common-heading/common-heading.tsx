import * as React from 'react';
import { createCn } from 'bem-react-classname';

import { TitleView, VIEWS } from 'arui-private/title-view';

import './common-heading.css';

type TOwnProps = {
    heading: string;
    subheading?: string;
    className?: string;
    buttonsGroup?: Array<
        React.ReactElement<unknown, string | React.JSXElementConstructor<unknown>>
    >;
};

type TProps = TOwnProps;

const cn = createCn('common-heading');

const CommonHeading: React.FC<TProps> = ({ heading, className, subheading, buttonsGroup }) => (
    <TitleView
        className={[cn('heading'), className].join(' ')}
        view={VIEWS.XLARGE}
        tag='h1'
        defaultMargins={false}
        subtitle={subheading}
        buttonsGroup={buttonsGroup}
        color='primary'
    >
        {heading}
    </TitleView>
);

export default CommonHeading;
