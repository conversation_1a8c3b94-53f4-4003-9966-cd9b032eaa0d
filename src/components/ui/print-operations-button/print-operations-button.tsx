import React from 'react';
import { createCn } from 'bem-react-classname';

import { Button, type ButtonProps } from '@alfalab/core-components/button';
import { type ComponentProps } from '@alfalab/core-components/button/typings';
import { useMatchMedia } from '@alfalab/core-components/mq';

import { STATEMENTS_METRICS } from '#/src/metrics';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import './print-operations-button.css';

const cn = createCn('print-operations-button');

type Props = ButtonProps & {
    onToggle: () => void;
    view?: ComponentProps['view'];
    leftAddons?: React.ReactNode;
    customClassName?: string;
};

const PrintOperationsButton: React.FC<Props> = ({
    onToggle,
    view,
    customClassName,
    ...buttonProps
}) => {
    const [isTablet] = useMatchMedia('--tablet-m');
    const [isMobile] = useMatchMedia('--mobile');
    const trackAlfaMetrics = useTrackAlfaMetrics();

    // на мобилке пока не отображаем выписки
    if (isMobile) {
        return null;
    }

    const handleOnClick = () => {
        onToggle();

        if (trackAlfaMetrics) {
            trackAlfaMetrics(STATEMENTS_METRICS.clickOpenButton, {});
        }
    };

    return (
        <Button
            size={isTablet ? 's' : 'xs'}
            onClick={handleOnClick}
            className={cn(customClassName)}
            view={view}
            dataTestId='print-operations-button'
            {...buttonProps}
        >
            Новая выписка
        </Button>
    );
};

export default PrintOperationsButton;
