import * as React from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type UnixEpoch } from 'thrift-services/utils';

import { Typography } from '@alfalab/core-components/typography';

import { currentTimeSelector } from '#/src/ducks/settings/selectors';

import { DAY_MONTH_YEAR_FORMAT } from '../../../constants/date';
import { dateToCustomFormat } from '../../../utils/date';

import './date-section.css';

type TOwnProps = {
    date: UnixEpoch;
};

type TProps = TOwnProps;

const cn = createCn('date-section');

const DateSection: React.FC<TProps> = ({ date }) => {
    const currentTime = useSelector(currentTimeSelector);
    const formattedDate = dateToCustomFormat(currentTime, date, DAY_MONTH_YEAR_FORMAT);

    return (
        <Typography.Text view='primary-medium' color='secondary' tag='div' className={cn()}>
            {formattedDate}
        </Typography.Text>
    );
};

export default DateSection;
