import React from 'react';
import { createCn } from 'bem-react-classname';

import { Link } from '@alfalab/core-components/link';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';
import CallMIcon from '@alfalab/icons-glyph/CallMIcon';
import { MailMIcon } from '@alfalab/icons-glyph/MailMIcon';

import './contacts-pane.css';

const cn = createCn('contacts-pane');

const TYPES = {
    MAIL: 'mail',
    PHONE: 'phone',
    CUSTOM: 'custom',
};

type Props = {
    type: 'mail' | 'phone' | 'custom';
    contacts: string;
    customHeader?: string;
    customIcon?: React.ReactNode;
    isSkeletonVisible?: boolean;
    isModal?: boolean;
};

export const ContactsPane = ({
    type,
    contacts,
    customIcon,
    customHeader,
    isSkeletonVisible,
    isModal = false,
}: Props) => {
    if (!contacts) return null;

    const getIcon = () => {
        switch (type) {
            case TYPES.CUSTOM:
                return customIcon;
            case TYPES.MAIL:
                return customIcon ?? <MailMIcon />;
            case TYPES.PHONE:
                return customIcon ?? <CallMIcon />;
            default:
                return customIcon ?? <MailMIcon />;
        }
    };

    const getFooter = () => {
        switch (type) {
            case TYPES.CUSTOM:
                return customHeader;
            case TYPES.MAIL:
                return customHeader ?? 'для писем';
            case TYPES.PHONE:
                return customHeader ?? 'для звонков и сообщений';
            default:
                return customHeader ?? 'для писем';
        }
    };

    const getLink = () => {
        switch (type) {
            case TYPES.PHONE:
                return (
                    <Link view='primary' underline={false} href={`tel:${contacts}`}>
                        {contacts}
                    </Link>
                );
            case TYPES.MAIL:
                return (
                    <Link view='primary' underline={false} href={`mailto:${contacts}`}>
                        {contacts}
                    </Link>
                );
            case TYPES.CUSTOM:
            default:
        }
    };

    return (
        <div className={cn('contact-card', { modal: isModal })}>
            <div className={cn('contact-card-icon')}>{getIcon()}</div>
            <div className={cn('contact-card-contacts')}>
                <Skeleton visible={isSkeletonVisible}>
                    <Typography.Text view='component'>{getLink()}</Typography.Text>
                </Skeleton>

                <Typography.Text
                    view='primary-small'
                    color='secondary'
                    className={cn('contact-card-footer')}
                >
                    {getFooter()}
                </Typography.Text>
            </div>
        </div>
    );
};
