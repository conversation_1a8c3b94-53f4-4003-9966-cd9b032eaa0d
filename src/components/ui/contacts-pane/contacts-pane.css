.contacts-pane {
    &__contact-card {
        width: 100%;
        display: flex;
        border-radius: var(--border-radius-16);
        background-color: var(--color-light-bg-secondary);

        margin-bottom: var(--gap-24);

        &:last-child {
            margin-bottom: 0;
        }

        &_modal {
            margin-bottom: var(--gap-16);
            padding: var(--gap-16);
        }
    }
    &__contact-card-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--color-light-bg-primary);
        color: var(--color-light-neutral-translucent-700);
        width: 48px;
        height: 48px;
        border-radius: var(--border-radius-16);
    }
    &__contact-card-contacts {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        flex-direction: column;
        margin-left: 18px;
    }
    &__contact-card-footer {
        margin-top: var(--gap-4);
    }
}
