import * as React from 'react';
import { createCn } from 'bem-react-classname';
import { type Amount } from 'thrift-services/entities';

import { Typography } from '@alfalab/core-components/typography';
import CorporateAmount from 'arui-private/corporate-amount';

import { ECreditProducts } from '#/src/constants/credit-products';

import AmountPure from '../amount-pure';

import './available-amount-panel.css';

type TOwnProps = {
    productType?: ECreditProducts;
    availableAmount?: Amount;
    limit?: Amount;
    text?: string | React.ReactNode;
};

type TProps = TOwnProps;

const cn = createCn('available-amount-panel');

const getTitleFromProductsType = (productType?: ECreditProducts) => {
    switch (productType) {
        case ECreditProducts.SOPUK:
            return 'Использовано';
        default:
            return 'Доступно';
    }
};

const AvailableAmountPanel: React.FC<TProps> = ({ productType, availableAmount, limit, text }) => (
    <div className={cn()}>
        {productType !== ECreditProducts.SOPUK && (
            <Typography.Text className={cn('label')} color='secondary'>
                {getTitleFromProductsType(productType)}
            </Typography.Text>
        )}
        {!!availableAmount && productType !== ECreditProducts.SOPUK && (
            <Typography.Title
                className={cn('available-amount')}
                view='medium'
                font='system'
                tag='div'
            >
                <CorporateAmount view='withZeroMinorPart' amount={availableAmount} />
            </Typography.Title>
        )}
        {!!limit && productType !== ECreditProducts.SOPUK && (
            <Typography.Text
                view='primary-medium'
                className={cn('available-limit')}
                color='secondary'
            >
                из&nbsp;
                <AmountPure value={limit} />
            </Typography.Text>
        )}
        {!!text && <div className={cn('text')}>{text}</div>}
    </div>
);

export default AvailableAmountPanel;
