import React from 'react';
import { createCn } from 'bem-react-classname';

import classNames from '#/src/utils/class-names';

import './filters-plate.css';

const cn = createCn('filters-plate');

export const FiltersPlate: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
    children,
    className,
    ...props
}) => (
    <div {...props} className={classNames(className, cn({ 'background-color': 'white' }))}>
        {children}
    </div>
);
