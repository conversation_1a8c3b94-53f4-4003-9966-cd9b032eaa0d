import React, { useCallback, useState } from 'react';
import { createCn } from 'bem-react-classname';

import { type Position } from '@alfalab/core-components/popover/Component';
import { Tooltip } from '@alfalab/core-components/tooltip';
import { type TextProps, Typography } from '@alfalab/core-components/typography';
import { type colors } from '@alfalab/core-components/typography/colors';
import { InformationCircleSIcon } from '@alfalab/icons-glyph/InformationCircleSIcon';

import './pane-item.css';

type TOwnProps = {
    tooltipPosition?: Position;
    idForText?: string;
    title: React.ReactNode;
    titleSize?: TextProps['view'];
    titleColor?: (typeof colors)[number];
    text?: React.ReactNode;
    textSize?: TextProps['view'];
    textWeight?: TextProps['weight'];
    textColor?: (typeof colors)[number];
    withTooltip?: boolean;
    textForTooltip?: React.ReactNode;
    className?: string;
    classNameTypeMode?: 'overdraft';
    popupName?: string;
    onTogglePopup?: (popupName?: string) => void;
};

const PaneItem: React.FC<TOwnProps> = ({
    tooltipPosition = 'right',
    withTooltip = false,
    titleSize = 'primary-medium',
    titleColor = 'primary',
    idForText,
    title,
    text,
    textSize = 'primary-large',
    textColor = 'primary',
    textWeight = 'medium',
    textForTooltip,
    onTogglePopup,
    popupName,
    className = '',
    classNameTypeMode,
}) => {
    const cn = createCn('pane-item', className);
    const [IsVisiblePopup, setPopupVisibility] = useState<boolean>(false);

    const handleTooltipClick = useCallback(() => {
        setPopupVisibility(!IsVisiblePopup);

        if (!IsVisiblePopup && onTogglePopup) {
            onTogglePopup(popupName);
        }
    }, [IsVisiblePopup, onTogglePopup, popupName]);

    return (
        <div className={cn()}>
            <div
                className={cn('head', {
                    type: classNameTypeMode ?? '',
                })}
            >
                <Typography.Text
                    className={cn('head-text', {
                        type: classNameTypeMode ?? '',
                    })}
                    view={titleSize}
                    color={titleColor}
                >
                    {title}
                </Typography.Text>
                {withTooltip && textForTooltip && (
                    <div onClick={(e) => e.stopPropagation()}>
                        <Tooltip
                            trigger='hover'
                            open={IsVisiblePopup}
                            content={textForTooltip}
                            position={tooltipPosition}
                            onOpen={handleTooltipClick}
                            onClose={handleTooltipClick}
                            contentClassName={cn('tooltip-text')}
                        >
                            <InformationCircleSIcon className={cn('info-icon')} />
                        </Tooltip>
                    </div>
                )}
            </div>
            <Typography.Text
                className={cn('text', { active: withTooltip, type: classNameTypeMode ?? '' })}
                tag='span'
                tabIndex={-1}
                onClick={withTooltip && textForTooltip ? handleTooltipClick : () => null}
                id={idForText}
                color={textColor}
                view={textSize}
                weight={textWeight}
            >
                {text}
            </Typography.Text>
        </div>
    );
};

export default PaneItem;
