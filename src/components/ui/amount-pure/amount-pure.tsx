import React from 'react';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Amount, type AmountProps } from '@alfalab/core-components/amount';

type TProps = {
    className?: string;
    value?: TAmount;
    bold?: AmountProps['bold'];
    view?: AmountProps['view'];
    codeFormat?: AmountProps['codeFormat'];
    transparentMinor?: AmountProps['transparentMinor'];
    dataTestId?: string;
};

const AmountPure: React.FC<TProps> = ({
    className,
    value,
    bold,
    view = 'withZeroMinorPart',
    codeFormat,
    transparentMinor = false,
    dataTestId,
}) => (
    <Amount
        className={className}
        value={value?.amount || 0}
        currency={value?.currency?.mnemonicCode as AmountProps['currency']}
        minority={value?.currency?.minorUnits || 100}
        view={view}
        bold={bold}
        codeFormat={codeFormat}
        transparentMinor={transparentMinor}
        dataTestId={dataTestId}
    />
);

export default AmountPure;
