import React from 'react';
import { createCn } from 'bem-react-classname';

import { PickerButton } from '@alfalab/core-components/picker-button';

import PrintOperationsButton from '../print-operations-button/print-operations-button';

import './print-operations-button-adaptive.css';

const cn = createCn('print-operations-button-adaptive');

type Props = {
    isDesktop: boolean;
    isTablet: boolean;
    isMobile: boolean;
    isStatementAllowed: boolean;
    isPrimaryButtonVisible: boolean;
    togglePrintOperationsSidebar: () => void;
};

const PrintOperationsButtonAdaptive = ({
    isDesktop,
    isTablet,
    isMobile,
    isStatementAllowed,
    isPrimaryButtonVisible,
    togglePrintOperationsSidebar,
}: Props) => {
    // Если нет доступа к выпискам или если это мобилка, то не отображаем кнопку
    if (!isStatementAllowed || isMobile) return null;

    // Кнопку "Новая выписка" отображаем на десктопе или если кнопка "Новый транш"
    // не отображается (в этом случае мы рисуем "Новую выписку" везде, кроме мобилки)
    if (isDesktop || !isPrimaryButtonVisible) {
        return (
            <PrintOperationsButton
                view='secondary'
                size={isTablet ? 's' : 'xs'}
                onToggle={togglePrintOperationsSidebar}
                nowrap={true}
            />
        );
    }

    // Прячем кнопку "Новая выписка" в три точки, если кнопка "Новый транш" отображается
    // и если это не десктоп (на мобилке также ничего не рисуем)
    return (
        <PickerButton
            options={[{ key: 'printOperationsButton', content: 'Новая выписка' }]}
            size={isTablet ? 's' : 'xs'}
            view='secondary'
            variant='compact'
            onChange={togglePrintOperationsSidebar}
            className={cn('picker-button')}
        />
    );
};

export { PrintOperationsButtonAdaptive };
