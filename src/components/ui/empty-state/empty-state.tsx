import React from 'react';
import { createCn } from 'bem-react-classname';

import { Button } from '@alfalab/core-components/button';
import { Typography } from '@alfalab/core-components/typography';
import { CategoryCreditMIcon } from '@alfalab/icons-glyph/CategoryCreditMIcon';

import './empty-state.css';

type TOwnProps = {
    label?: string | React.ReactNode;
    text?: string | React.ReactNode;
    textForButton?: string | React.ReactNode;
    className?: string;
    icon?: React.ReactNode;
    onButtonClick?: () => void;
    children?: React.ReactNode;
};

type TProps = TOwnProps;

const cn = createCn('empty-state');

const EmptyState: React.FC<TProps> = ({
    label = <span>Похоже, что-то&nbsp;пошло&nbsp;не&nbsp;так</span>,
    text = <span>Сервис&nbsp;временно&nbsp;недоступен, попробуйте&nbsp;зайти&nbsp;позднее.</span>,
    textForButton = <span>Перейти&nbsp;на&nbsp;главную</span>,
    icon = <CategoryCreditMIcon width={48} height={48} />,
    className,
    onButtonClick,
    children,
}) => (
    <div className={[cn(), className].join(' ')}>
        {icon && <div className={cn('icon')}>{icon}</div>}
        {label && (
            <Typography.Title
                className={cn('heading')}
                view='medium'
                font='system'
                color='primary'
                tag='div'
            >
                {label}
            </Typography.Title>
        )}
        {text && (
            <Typography.Text className={cn('text')} color='primary'>
                {text}
            </Typography.Text>
        )}
        {textForButton && (
            <Button className={cn('button')} size='xs' onClick={onButtonClick}>
                {textForButton}
            </Button>
        )}
        {children}
    </div>
);

export default EmptyState;
