import React from 'react';
import { useHistory } from 'react-router';
import { createCn } from 'bem-react-classname';

import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { Modal } from '@alfalab/core-components/modal';
import { Typography } from '@alfalab/core-components/typography';
import CategoryCreditMIcon from '@alfalab/icons-glyph/CategoryCreditMIcon';
import { StatusScreenNoMobile } from 'arui-private/status-screen';

import './adaptive-empty-state.css';

const cn = createCn('adaptive-empty-state');

type Props = {
    title: string;
    text: string;
    icon?: React.ReactNode;
    isMobile?: boolean;
};

const AdaptiveEmptyState = ({
    icon = <CategoryCreditMIcon width={64} height={64} />,
    title,
    text,
    isMobile = false,
}: Props) => {
    const history = useHistory();

    if (isMobile) {
        return (
            <Modal
                open={true}
                onClose={() => history.goBack()}
                hasCloser={true}
                className={cn('modal')}
                contentClassName={cn('modal-wrapper')}
            >
                <Modal.Header />
                <StatusScreenNoMobile
                    title={title}
                    subtitle={text}
                    secondaryButtonProps={{
                        label: 'Понятно',
                        props: {
                            onClick: () => history.goBack(),
                        },
                    }}
                    fullHeight={true}
                />
            </Modal>
        );
    }

    return (
        <div className={cn()}>
            <SuperEllipse size={64} className={cn('icon')}>
                {icon}
            </SuperEllipse>
            <div className={cn('text-wrapper')}>
                <Typography.Title tag='div' view='small' font='system'>
                    {title}
                </Typography.Title>
                <Typography.Text view='primary-medium'>{text}</Typography.Text>
            </div>
        </div>
    );
};

export { AdaptiveEmptyState };
