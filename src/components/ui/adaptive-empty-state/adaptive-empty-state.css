.adaptive-empty-state {
    padding: var(--gap-64) var(--gap-24);
    border-radius: var(--border-radius-12);
    background-color: var(--color-white);
    box-shadow: var(--shadow-xs);
    display: flex;
    flex-direction: column;
    align-items: center;

    @media (--mobile) {
        box-shadow: none;
        border-radius: var(--border-radius-16);
    }

    @media (--tablet) {
        box-shadow: none;
        border-radius: var(--border-radius-16);
    }

    &__icon {
        margin-bottom: var(--gap-24);
    }

    &__text-wrapper {
        display: flex;
        flex-direction: column;
        gap: var(--gap-s);
        text-align: center;
    }

    &__modal {
        max-width: 767px;
    }

    &__modal-wrapper {
        padding: var(--gap-16);
    }
}
