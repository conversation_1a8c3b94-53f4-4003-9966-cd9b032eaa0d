import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { ReportFormat } from 'thrift-services/entities';

import { useMatchMedia } from '@alfalab/core-components/mq';
import { Notification } from '@alfalab/core-components/notification';
import { PickerButton } from '@alfalab/core-components/picker-button';
import { type BaseSelectChangePayload } from '@alfalab/core-components/select/typings';

import {
    isDownloadPaymentsFileErrorSelector,
    isDownloadPaymentsFileStartSelector,
} from '#/src/ducks/payment-schedule/selectors';

import './schedule-download-pane.css';

const cn = createCn('schedule-download-pane');

type TProps = {
    onDownloadButtonClick?: (fileType?: ReportFormat) => void;
    downloadOptions?: Array<{ key: ReportFormat }>;
};

const defaultOptions = [ReportFormat.PDF, ReportFormat.XLS].map((key) => ({ key }));

const ScheduleDownloadPane: React.FC<TProps> = ({
    downloadOptions = defaultOptions,
    onDownloadButtonClick,
}) => {
    const [isTablet] = useMatchMedia('--tablet-m');
    const [isVisibleNotification, setNotificationVisibility] = useState(true);
    const isFetchingPayments = useSelector(isDownloadPaymentsFileStartSelector);
    const isDownloadPaymentsFileError = useSelector(isDownloadPaymentsFileErrorSelector);

    useEffect(() => {
        if (!isVisibleNotification) {
            setNotificationVisibility(true);
        }
    }, [isFetchingPayments, isDownloadPaymentsFileError]);

    const handleCloseNotification = useCallback(() => {
        setNotificationVisibility(false);
    }, []);

    const getDownloadButtonClickHandler = (fileType: BaseSelectChangePayload) => {
        if (onDownloadButtonClick) {
            onDownloadButtonClick(fileType.selected?.key as ReportFormat);
        }
    };

    return (
        <div className={cn()}>
            <Notification
                visible={!!isDownloadPaymentsFileError && isVisibleNotification}
                badge='negative'
                onClose={handleCloseNotification}
                onCloseTimeout={handleCloseNotification}
                title={isDownloadPaymentsFileError?.title}
                contentClassName={cn('content')}
                autoCloseDelay={5000}
            >
                {isDownloadPaymentsFileError?.text}
            </Notification>
            <Notification
                visible={isFetchingPayments && isVisibleNotification}
                badge='neutral-operation'
                title='График платежей скачивается'
                onClose={handleCloseNotification}
            >
                Это займет несколько секунд
            </Notification>
            <PickerButton
                className={cn('button')}
                options={downloadOptions}
                size={isTablet ? 'xxs' : 'xs'}
                showArrow={isTablet}
                onChange={getDownloadButtonClickHandler}
                label='Скачать график'
            />
        </div>
    );
};

export default ScheduleDownloadPane;
