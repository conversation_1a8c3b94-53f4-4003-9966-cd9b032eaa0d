import React from 'react';
import { createCn } from 'bem-react-classname';
import { type ReportFormat } from 'thrift-services/entities';

import { useMatchMedia } from '@alfalab/core-components/mq';
import { Typography } from '@alfalab/core-components/typography';

import PaneItem from '../pane-item';

import ScheduleDownloadPane from './schedule-download-pane';

import './schedule-common-heading.css';

type TOwnProps = {
    paymentsCount: number;
    isCanBeDownloaded?: boolean;
    onTogglePopup?: (popupName?: string) => void;
    onDownloadButtonClick?: (fileType?: ReportFormat) => void;
    downloadOptions?: Array<{ key: ReportFormat }>;
};

const cn = createCn('schedule-common-heading');

const ScheduleCommonHeading: React.FC<TOwnProps> = ({
    isCanBeDownloaded = false,
    paymentsCount,
    downloadOptions,
    onTogglePopup,
    onDownloadButtonClick,
}) => {
    const [isTablet] = useMatchMedia('--tablet-m');

    return (
        <div className={cn()}>
            {isTablet && (
                <PaneItem
                    title={
                        <Typography.Text>
                            Всего платежей:&nbsp;
                            <b>{paymentsCount}</b>
                        </Typography.Text>
                    }
                    titleSize='primary-large'
                    withTooltip={true}
                    popupName='graphicOnDateTooltip'
                    onTogglePopup={onTogglePopup}
                />
            )}
            {isCanBeDownloaded && (
                <ScheduleDownloadPane
                    downloadOptions={downloadOptions}
                    onDownloadButtonClick={onDownloadButtonClick}
                />
            )}
        </div>
    );
};

export default ScheduleCommonHeading;
