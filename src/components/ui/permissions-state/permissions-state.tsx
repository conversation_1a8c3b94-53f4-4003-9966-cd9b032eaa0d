import React from 'react';

import { StatusScreenError } from 'arui-private/status-screen';

import { NBSP } from '#/src/constants/unicode-symbols';

type TOwnProps = {
    label?: string | React.ReactNode;
    text?: string | React.ReactNode;
    textForButton?: string;
    onButtonClick?: () => void;
};

type TProps = TOwnProps;

const PermissionsState: React.FC<TProps> = ({
    label = 'Недостаточно прав',
    text = `Выберите другую компанию или обратитесь к${NBSP}руководителю`,
    textForButton = 'На главную',
    onButtonClick,
}) => (
    <StatusScreenError
        title={label}
        subtitle={text}
        secondaryButtonProps={{ label: textForButton, props: { onClick: onButtonClick } }}
    />
);

export default PermissionsState;
