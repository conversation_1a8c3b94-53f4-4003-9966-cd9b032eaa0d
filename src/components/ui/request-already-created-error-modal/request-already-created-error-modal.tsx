import React from 'react';
import { createCn } from 'bem-react-classname';

import { Button } from '@alfalab/core-components/button';
import { Modal } from '@alfalab/core-components/modal';
import { Typography } from '@alfalab/core-components/typography';
import warningImageSrc from 'arui-private/icons/warning.svg';

import './request-already-created-error-modal.css';

const cn = createCn('request-already-created-error-modal');

type Props = {
    onClose: () => void;
    visible: boolean;
};

export const RequestAlreadyCreatedErrorModal = ({ visible, onClose: handleClose }: Props) => (
    <Modal
        size='l'
        open={visible}
        className={cn()}
        hasCloser={true}
        onClose={handleClose}
        data-test-id='request-already-created-error-modal'
    >
        <div className={cn('content')}>
            <img src={warningImageSrc} width='320' height='240' alt='' />
            <Typography.TitleResponsive
                view='small'
                className={`${cn('heading')}`}
                tag='h4'
                font='system'
                color='primary'
            >
                Заявка в&nbsp;обработке
            </Typography.TitleResponsive>
            <Typography.Text className={cn('about-error')}>
                По&nbsp;этому договору уже создана заявка&nbsp;&mdash; дождитесь, пока она
                обработается, или обратитесь к&nbsp;персональному менеджеру для отмены
            </Typography.Text>
            <Button onClick={handleClose} view='tertiary'>
                Понятно
            </Button>
        </div>
    </Modal>
);
