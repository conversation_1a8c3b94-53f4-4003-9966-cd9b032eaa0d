@import '@alfalab/core-components/vars';

.pane-group {
    background-color: var(--color-light-bg-secondary);
    border: 1px solid var(--color-dark-indigo-15-flat);
    border-radius: var(--border-radius-8);
    transition:
        border 350ms,
        box-shadow 350ms;
    box-sizing: border-box;
    padding: var(--gap-24) var(--gap-24) 0 var(--gap-24);
    margin: 0 0 var(--gap-24) 0;

    @media (--tablet-m) {
        background-color: var(--color-light-base-bg-primary);
    }

    &__item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--gap-24);

        @media (--small-only) {
            margin-bottom: var(--gap-8);

            &:last-child {
                margin-bottom: var(--gap-16);
            }
        }

        &_with-divider {
            @media (--desktop) {
                &:nth-child(odd) {
                    &:after {
                        display: none;
                    }
                }
            }

            @media (--tablet) {
                &:nth-child(even) {
                    &:after {
                        display: none;
                    }
                }
            }

            &:first-child:after {
                display: block;

                @media (--mobile) {
                    display: none;
                }
            }

            &:last-child:after {
                display: none;
            }

            &:after {
                content: '';
                border-left: 1px solid var(--color-dark-indigo-15-flat);
                height: 46px;
                margin: 0 5% 0 0;

                @media (--mobile) {
                    display: none;
                }
            }
        }
    }

    &_without-border {
        border: none;
    }

    &_shadowed {
        box-shadow: var(--shadow-xs);
    }
}
