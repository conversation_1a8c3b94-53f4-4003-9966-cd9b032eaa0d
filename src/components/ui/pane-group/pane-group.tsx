import React, { type ReactNode } from 'react';
import { createCn } from 'bem-react-classname';

import { Grid } from '@alfalab/core-components/grid';
import { type ColProps } from '@alfalab/core-components/grid/col';
import { type RowProps } from '@alfalab/core-components/grid/row';

import './pane-group.css';

type TOwnProps = {
    justify?: RowProps['justify'];
    withoutBorder?: boolean;
    withDivider?: boolean;
    children: ReactNode[];
    className?: string;
    footer?: ReactNode;
    colWidth?: ColProps['width'];
    shadowed?: boolean;
};

type TProps = TOwnProps;

const PaneGroup: React.FC<TProps> = ({
    children,
    justify = 'between',
    withoutBorder = false,
    withDivider = false,
    shadowed = false,
    className,
    footer,
    colWidth = { mobile: 12, tablet: 6, desktop: 4 },
}) => {
    const cn = createCn('pane-group', className);

    return (
        <Grid.Row className={cn({ 'without-border': withoutBorder, shadowed })} justify={justify}>
            {children.map(
                (item, index) =>
                    item && (
                        <Grid.Col
                            className={cn('item', { 'with-divider': withDivider })}
                            width={colWidth}
                            key={`grid-item__${index}`}
                        >
                            {item}
                        </Grid.Col>
                    ),
            )}
            {footer}
        </Grid.Row>
    );
};

export default PaneGroup;
