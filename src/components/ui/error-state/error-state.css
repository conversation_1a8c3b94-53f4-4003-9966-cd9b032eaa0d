@import '@alfalab/core-components/vars';

.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    min-height: 160px;
    border: 1px solid var(--color-dark-indigo-07);
    border-radius: var(--border-radius-8);
    padding: var(--gap-16) var(--gap-24);
    background-color: var(--color-dark-indigo-05);
    margin-bottom: var(--gap-24);

    &_redesigned {
        box-shadow: var(--shadow-xs);
        border-radius: var(--border-radius-8);
        background-color: var(--color-white);
        border: 0;

        @media (--mobile) {
            box-shadow: none;
            border-radius: var(--border-radius-16);
        }

        @media (--tablet) {
            box-shadow: none;
            border-radius: var(--border-radius-16);
        }
    }

    &__heading {
        margin: var(--gap-0) var(--gap-0) var(--gap-12);
        text-align: center;
    }

    &__sub_heading {
        margin: var(--gap-0) var(--gap-0) var(--gap-12);
        text-align: center;
    }

    &__text {
        text-align: center;
    }

    &__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        background-color: var(--color-white);
        border-radius: 180px;
        padding: var(--gap-12);
        margin-bottom: var(--gap-20);
    }

    &__redesigned-icon {
        margin-bottom: var(--gap-20);
    }

    &__content {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    & &__button {
        margin-top: var(--gap-24);

        @media (--mobile) {
            position: absolute;
            bottom: var(--gap-24);
            left: var(--gap-24);
            right: var(--gap-24);
        }
    }
}
