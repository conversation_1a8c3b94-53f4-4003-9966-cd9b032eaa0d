import React, { type FC, type ReactNode } from 'react';
import { createCn } from 'bem-react-classname';

import { Button } from '@alfalab/core-components/button';
import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { Typography } from '@alfalab/core-components/typography';

import './error-state.css';

type TOwnProps = {
    text?: ReactNode;
    title?: string;
    subtitle?: string;
    icon?: ReactNode;
    textForButton?: string | ReactNode;
    height?: number;
    onButtonClick?: () => void;
    isRedesigned?: boolean;
    className?: string;
    children?: ReactNode;
    isMobile?: boolean;
};

const cn = createCn('error-state');

const ErrorState: FC<TOwnProps> = ({
    icon,
    text,
    title,
    subtitle,
    textForButton,
    height,
    onButtonClick,
    children,
    isRedesigned = false,
    className,
    isMobile = false,
}) => (
    <div className={`${cn({ redesigned: isRedesigned })} ${className}`} style={{ height }}>
        <div className={cn('content')}>
            {!!icon &&
                (isRedesigned ? (
                    <SuperEllipse className={cn('redesigned-icon')} size={64}>
                        {icon}
                    </SuperEllipse>
                ) : (
                    <div className={cn('icon')}>{icon}</div>
                ))}
            {!!title && (
                <Typography.TitleResponsive
                    className={cn('heading')}
                    view='small'
                    tag='h1'
                    font='system'
                    color='primary'
                >
                    {title}
                </Typography.TitleResponsive>
            )}
            {!!subtitle && (
                <Typography.TitleResponsive
                    className={cn('sub_heading')}
                    view='xlarge'
                    tag='h1'
                    font='system'
                    color='primary'
                >
                    {subtitle}
                </Typography.TitleResponsive>
            )}
            {!!text && (
                <Typography.Text
                    className={cn('text')}
                    tag='div'
                    view='primary-medium'
                    color='primary'
                >
                    {text}
                </Typography.Text>
            )}
            {!!textForButton && (
                <Button className={cn('button')} size={isMobile ? 56 : 40} onClick={onButtonClick}>
                    {textForButton}
                </Button>
            )}
            {children}
        </div>
    </div>
);

export default ErrorState;
