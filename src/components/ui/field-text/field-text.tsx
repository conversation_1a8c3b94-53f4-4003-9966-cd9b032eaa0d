import React from 'react';
import { createCn } from 'bem-react-classname';

import { Typography } from '@alfalab/core-components/typography';

import './field-text.css';

type Props = {
    title: React.ReactNode;
    text: React.ReactNode;
    className?: string;
};

const FieldText: React.FC<Props> = ({ className, title, text }) => {
    const cn = createCn('field-text', className);

    return (
        <div className={cn()}>
            <Typography.Text view='primary-small' color='secondary'>
                {title}
            </Typography.Text>
            <Typography.Text className={cn('text')}>{text}</Typography.Text>
        </div>
    );
};

export default FieldText;
