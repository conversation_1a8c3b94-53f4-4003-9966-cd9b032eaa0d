import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';

import { Indicator } from '@alfalab/core-components/indicator';
import { Tab, Tabs } from '@alfalab/core-components/tabs';

import { newStatementsCountSelector } from '#/src/ducks/statement-requests/selectors';
import {
    isSuspensiveConditionsAvailableSelector,
    isSuspensiveConditionsVisibleSelector,
} from '#/src/ducks/suspensive-conditions/selectors';
import { type SomeMappedProduct } from '#/src/utils/credit-products-mappers';

type TOwnProps = {
    product: SomeMappedProduct;
    currentTab: string;
    leftTabTitle: string;
    rightTabTitle: string;
    documentsTabTitle?: string;
    isDocumentsTabVisible?: boolean;
    sendDocTabTitle?: string;
    isAlfaCreditWidgetButtonVisible?: boolean;
    contractTabTitle?: string;
    trancheStatementsTabTitle?: string;
    onTabClick: (tab: string) => void;
    isHiddenTrancheTab?: boolean;
    isStatementsTabVisible?: boolean;
};

enum ETabs {
    info = 'info',
    schedule = 'schedule',
    documents = 'documents',
    sendDocuments = 'sendDocuments',
    contracts = 'contracts',
    trancheStatements = 'trancheStatements',
    suspensiveConditions = 'suspensiveConditions',
    statementRequests = 'statementRequests',
}

type TProps = TOwnProps;

const CreditPageTabs: React.FC<TProps> = ({
    product,
    currentTab,
    onTabClick,
    leftTabTitle,
    rightTabTitle,
    documentsTabTitle = 'Кредитная документация',
    isDocumentsTabVisible,
    isAlfaCreditWidgetButtonVisible = false,
    sendDocTabTitle = 'Отправка документов',
    contractTabTitle = 'Договоры',
    trancheStatementsTabTitle = 'Заявления на транши',
    isHiddenTrancheTab = false,
    isStatementsTabVisible,
}) => {
    const isSuspensiveConditionsVisible = useSelector(isSuspensiveConditionsVisibleSelector);
    const isSuspensiveConditionsAvailable = useSelector(isSuspensiveConditionsAvailableSelector);
    const isHiddenAlfaCreditTabs = !isAlfaCreditWidgetButtonVisible;
    const isHiddenTrancheStatementsTab = isHiddenTrancheTab || isHiddenAlfaCreditTabs;

    const notificationsCount = product?.suspensiveConditionsDeadlineInfo?.notificationsCount;
    const statementsCount = useSelector(newStatementsCountSelector);

    const isHiddenSuspensiveConditionsTab =
        !isSuspensiveConditionsAvailable || !isSuspensiveConditionsVisible;

    const handleTabClick = useCallback((_, { selectedId }) => onTabClick(selectedId), [onTabClick]);

    return (
        <Tabs
            scrollable={true}
            selectedId={currentTab}
            onChange={handleTabClick}
            dataTestId='credit-page-tabs'
            size='xs'
            tagView='filled'
        >
            <Tab
                title={leftTabTitle}
                id={ETabs.info}
                dataTestId={`credit-page-tab__${ETabs.info}`}
            />
            <Tab
                title={rightTabTitle}
                id={ETabs.schedule}
                dataTestId={`credit-page-tab__${ETabs.schedule}`}
            />
            <Tab
                hidden={!isDocumentsTabVisible}
                title={documentsTabTitle}
                id={ETabs.documents}
                dataTestId={`credit-page-tab__${ETabs.documents}`}
            />
            <Tab
                hidden={isHiddenTrancheStatementsTab}
                title={trancheStatementsTabTitle}
                id={ETabs.trancheStatements}
                dataTestId={`credit-page-tab__${ETabs.trancheStatements}`}
            />
            <Tab
                hidden={isHiddenAlfaCreditTabs}
                title={contractTabTitle}
                id={ETabs.contracts}
                dataTestId={`credit-page-tab__${ETabs.contracts}`}
            />
            <Tab
                hidden={isHiddenAlfaCreditTabs}
                title={sendDocTabTitle}
                id={ETabs.sendDocuments}
                dataTestId={`credit-page-tab__${ETabs.sendDocuments}`}
            />
            <Tab
                hidden={isHiddenSuspensiveConditionsTab}
                title='Отлагательные условия'
                id={ETabs.suspensiveConditions}
                dataTestId={`credit-line-tab__${ETabs.suspensiveConditions}`}
                rightAddons={
                    notificationsCount ? (
                        <Indicator height={20} view='red' value={notificationsCount} />
                    ) : null
                }
                keepMounted={true}
            />

            <Tab
                id={ETabs.statementRequests}
                title='Выписки'
                hidden={!isStatementsTabVisible}
                dataTestId={`credit-line-tab__${ETabs.statementRequests}`}
                rightAddons={
                    statementsCount > 0 ? (
                        <Indicator height={20} view='red' value={statementsCount} />
                    ) : null
                }
                keepMounted={true}
            />
        </Tabs>
    );
};

export default CreditPageTabs;
