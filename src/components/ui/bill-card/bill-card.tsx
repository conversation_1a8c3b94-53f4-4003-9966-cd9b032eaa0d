import React from 'react';
import { createCn } from 'bem-react-classname';

import { StatusBadge } from '@alfalab/core-components/status-badge';
import { Tooltip } from '@alfalab/core-components/tooltip';
import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';

import { NBSP } from '#/src/constants/unicode-symbols';

import './bill-card.css';

type TBillCardProps = {
    title: string;
    subText: string;
    text: React.ReactNode;
    isDanger?: boolean;
    textForTooltip?: string | React.ReactNode;
};

const cn = createCn('bill-card');

export const BillCard = ({
    title,
    subText,
    text,
    isDanger = false,
    textForTooltip,
}: TBillCardProps) => (
    <BackgroundPlate
        view={BackgroundPlateView.Colored}
        className={cn('card', { danger: isDanger })}
        data-test-id={`bill-card-${title}`}
    >
        <div>
            <Typography.Text view='component'>{title}</Typography.Text>
        </div>
        <div>
            <div>
                <Typography.Title
                    className={cn('text')}
                    view='small'
                    tag='div'
                    color={isDanger ? 'negative' : undefined}
                    font='system'
                    dataTestId='bill-card-text'
                >
                    {text}
                    {NBSP}
                    {!!textForTooltip && (
                        <Tooltip
                            trigger='hover'
                            content={textForTooltip}
                            position='right'
                            targetClassName={cn('tooltip')}
                            dataTestId='bill-card-tooltip'
                        >
                            <StatusBadge
                                view={isDanger ? 'negative-alert' : 'neutral-information'}
                                size={20}
                            />
                        </Tooltip>
                    )}
                </Typography.Title>
            </div>
            <Typography.Text tag='div' view='secondary-large' dataTestId='bill-card-subtext'>
                {subText}
            </Typography.Text>
        </div>
    </BackgroundPlate>
);
