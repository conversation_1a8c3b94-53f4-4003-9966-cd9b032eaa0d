import React from 'react';
import { type UnixEpoch } from 'thrift-services/utils';

import { Status } from '@alfalab/core-components/status';
import { Typography } from '@alfalab/core-components/typography';

import { creditOfferItemBadgeCn } from '#/src/components/credit-offer-item-badge/credit-offer-item-cn';
import {
    type ECreditOffers,
    type ECreditOffersCampaignCode,
    OffersWithDigitalSignBadge,
    TotalOffersCampaignCode,
} from '#/src/constants/credit-offers';
import { DATE_FORMAT } from '#/src/constants/date';
import { dateToCustomFormat } from '#/src/utils/date';

type Props = {
    offerType: ECreditOffers;
    preApproved?: boolean;
    campaignCode?: string;
    currentTime?: Date;
    earlierExpiryDate?: UnixEpoch;
};

export const CreditOfferItemBadge = ({
    offerType,
    preApproved,
    campaignCode,
    currentTime,
    earlierExpiryDate,
}: Props) => {
    const isTotalOffer = TotalOffersCampaignCode.includes(
        campaignCode as ECreditOffersCampaignCode,
    );

    const withDigitalSignBadge = OffersWithDigitalSignBadge.includes(offerType);

    const expiryDate = dateToCustomFormat(currentTime, earlierExpiryDate, DATE_FORMAT);

    return preApproved || withDigitalSignBadge ? (
        <div className={creditOfferItemBadgeCn()}>
            <div className={creditOfferItemBadgeCn('statuses')}>
                {preApproved && (
                    <Status className={creditOfferItemBadgeCn('status')}>
                        {isTotalOffer ? 'ОДОБРЕНО' : 'ПРЕДВАРИТЕЛЬНО ОДОБРЕНО'}
                    </Status>
                )}
                {withDigitalSignBadge && (
                    <Status className={creditOfferItemBadgeCn('status')} color='grey'>
                        ОНЛАЙН С ЭЦП ФНС
                    </Status>
                )}
            </div>

            {preApproved && (
                <Typography.Text
                    color='secondary'
                    className={creditOfferItemBadgeCn('description')}
                >
                    {isTotalOffer && 'Оформление за 3 минуты. '}Действует до {expiryDate}
                </Typography.Text>
            )}
        </div>
    ) : null;
};
