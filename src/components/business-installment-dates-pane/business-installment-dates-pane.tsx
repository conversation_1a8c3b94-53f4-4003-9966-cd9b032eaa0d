import React from 'react';
import { createCn } from 'bem-react-classname';
import { type Amount as TAmount } from 'thrift-services/entities';

import { TitleView, VIEWS } from 'arui-private/title-view';

import { BusinessInstallmentDifferentDatesPane } from './business-installment-different-dates-pane';
import { BusinessInstallmentSameDatesPane } from './business-installment-same-dates-pane';

import './business-installment-dates-pane.css';

type TOwnProps = {
    isFineExists: boolean;
    totalToPay?: TAmount;
    totalLoanSumToPay?: TAmount;
    totalInterestSumToPay?: TAmount;
    totalFine?: TAmount;
    totalOverdueAndFine?: TAmount;
    interestToPay?: TAmount;
    overdueDebt?: TAmount;
    shortAccountNumber?: string;
    onTogglePopup?: (popupName?: string) => void;
    payDebtTillDate?: { seconds: number };
    payInterestTillDate?: { seconds: number };
    currentTime: Date;
    isPaymentOfMainDebtAndInterestAtDifferentDates: boolean;
};

type TProps = TOwnProps;

export const cn = createCn('business-installment-dates-pane');

const BusinessInstallmentDatesPane: React.FC<TProps> = ({
    payDebtTillDate,
    payInterestTillDate,
    totalToPay,
    totalLoanSumToPay,
    totalInterestSumToPay,
    totalFine,
    currentTime,
    shortAccountNumber,
    totalOverdueAndFine,
    onTogglePopup,
    interestToPay,
    overdueDebt,
    isFineExists,
    isPaymentOfMainDebtAndInterestAtDifferentDates,
}) => (
    <div className={cn()}>
        <TitleView view={VIEWS.SMALL} tag='h2'>
            Платежи
        </TitleView>
        <div className={cn('wrapper')}>
            {isPaymentOfMainDebtAndInterestAtDifferentDates ? (
                <BusinessInstallmentDifferentDatesPane
                    totalFine={totalFine}
                    currentTime={currentTime}
                    overdueDebt={overdueDebt}
                    payDebtTillDate={payDebtTillDate}
                    totalLoanSumToPay={totalLoanSumToPay}
                    payInterestTillDate={payInterestTillDate}
                    totalOverdueAndFine={totalOverdueAndFine}
                    totalInterestSumToPay={totalInterestSumToPay}
                />
            ) : (
                <BusinessInstallmentSameDatesPane
                    totalFine={totalFine}
                    totalToPay={totalToPay}
                    overdueDebt={overdueDebt}
                    currentTime={currentTime}
                    isFineExists={isFineExists}
                    interestToPay={interestToPay}
                    onTogglePopup={onTogglePopup}
                    payDebtTillDate={payDebtTillDate}
                    shortAccountNumber={shortAccountNumber}
                    totalOverdueAndFine={totalOverdueAndFine}
                />
            )}
        </div>
    </div>
);

export default BusinessInstallmentDatesPane;
