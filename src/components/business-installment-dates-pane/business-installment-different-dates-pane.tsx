import React from 'react';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Typography } from '@alfalab/core-components/typography';

import { SPACE_DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';
import AmountPure from '../ui/amount-pure';
import { BillCard } from '../ui/bill-card/bill-card';

import '../credit-dates-pane/credit-dates-pane.css';

type TProps = {
    totalLoanSumToPay?: TAmount;
    totalInterestSumToPay?: TAmount;
    totalFine?: TAmount;
    payDebtTillDate?: { seconds: number };
    overdueDebt?: TAmount;
    payInterestTillDate?: { seconds: number };
    currentTime: Date;
    totalOverdueAndFine?: TAmount;
};

export const BusinessInstallmentDifferentDatesPane: React.FC<TProps> = ({
    payDebtTillDate,
    totalOverdueAndFine,
    totalLoanSumToPay,
    totalInterestSumToPay,
    currentTime,
    overdueDebt,
    totalFine,
}) => {
    if (!totalLoanSumToPay && !totalInterestSumToPay) return null;

    const loanSum = (totalLoanSumToPay?.amount ?? 0) > 0 && !!payDebtTillDate && (
        <BillCard
            title='По основному долгу'
            subText={dateToCustomFormat(currentTime, payDebtTillDate, SPACE_DATE_FORMAT)}
            text={<AmountPure value={totalLoanSumToPay} transparentMinor={false} />}
        />
    );

    return (
        <React.Fragment>
            {(totalOverdueAndFine?.amount ?? 0) > 0 && (
                <BillCard
                    title='Просрочено'
                    text={<AmountPure value={totalOverdueAndFine} transparentMinor={false} />}
                    subText='Пополните счёт'
                    isDanger={true}
                    textForTooltip={
                        <div>
                            <Typography.Text tag='div'>
                                <AmountPure
                                    bold='full'
                                    value={overdueDebt}
                                    transparentMinor={false}
                                />
                                &nbsp;— основной долг
                            </Typography.Text>
                            <Typography.Text tag='div'>
                                <AmountPure
                                    bold='full'
                                    value={totalFine}
                                    transparentMinor={false}
                                />
                                &nbsp;— неустойка
                            </Typography.Text>
                        </div>
                    }
                />
            )}
            {loanSum}
        </React.Fragment>
    );
};
