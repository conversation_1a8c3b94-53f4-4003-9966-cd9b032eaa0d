import React from 'react';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Typography } from '@alfalab/core-components/typography';

import { BULL } from '#/src/constants/unicode-symbols';

import { SPACE_DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';
import AmountPure from '../ui/amount-pure';
import { BillCard } from '../ui/bill-card/bill-card';

import './business-installment-dates-pane.css';

type TProps = {
    currentTime: Date;
    interestToPay?: TAmount;
    isFineExists: boolean;
    onTogglePopup?: (popupName?: string) => void;
    overdueDebt?: TAmount;
    shortAccountNumber?: string;
    payDebtTillDate?: { seconds: number };
    totalFine?: TAmount;
    totalInterestSumToPay?: TAmount;
    totalOverdueAndFine?: TAmount;
    totalToPay?: TAmount;
};

export const BusinessInstallmentSameDatesPane: React.FC<TProps> = ({
    payDebtTillDate,
    totalToPay,
    totalFine,
    currentTime,
    overdueDebt,
    shortAccountNumber,
    totalOverdueAndFine,
}) => {
    if (!totalToPay) return null;

    return (
        <React.Fragment>
            {(totalOverdueAndFine?.amount ?? 0) > 0 && (
                <BillCard
                    title='Просрочено'
                    isDanger={true}
                    text={
                        <span className='overdue-amount'>
                            <AmountPure value={totalOverdueAndFine} transparentMinor={false} />
                        </span>
                    }
                    textForTooltip={
                        <div>
                            <Typography.Text tag='div'>
                                <AmountPure
                                    bold='full'
                                    value={overdueDebt}
                                    transparentMinor={false}
                                />
                                &nbsp;— основной долг
                            </Typography.Text>
                            <Typography.Text tag='div'>
                                <AmountPure
                                    bold='full'
                                    value={totalFine}
                                    transparentMinor={false}
                                />
                                &nbsp;— неустойка
                            </Typography.Text>
                        </div>
                    }
                    subText={`Пополните счёт ${BULL}${BULL}${shortAccountNumber}`}
                />
            )}
            {(totalToPay?.amount ?? 0) > 0 && (
                <BillCard
                    title='Ближайший'
                    text={<AmountPure value={totalToPay} transparentMinor={false} />}
                    subText={dateToCustomFormat(currentTime, payDebtTillDate, SPACE_DATE_FORMAT)}
                />
            )}
        </React.Fragment>
    );
};
