@import '@alfalab/core-components/vars';

.business-installment-dates-pane {
    display: flex;
    flex-direction: column;

    @media (--small-only) {
        width: 100%;
    }

    &__wrapper {
        display: flex;
        gap: var(--gap-m);
        margin-top: var(--gap-24);
        width: 100%;

        @media (--small-only) {
            flex-direction: column;
        }
    }

    &__icon {
        color: var(--color-light-bg-primary-inverted);
    }

    &__date-pane {
        margin-right: var(--gap-24);
        background-color: var(--color-white);
        border: 1px solid var(--color-dark-indigo-15-flat);
        border-radius: var(--border-radius-8);
        transition:
            border 350ms,
            box-shadow 350ms;
        box-sizing: border-box;
        padding: var(--gap-16);

        @media (--small-only) {
            margin-right: var(--gap-8);
        }
    }

    .date-pane {
        background-color: var(--color-white);
        border: 1px solid var(--color-dark-indigo-15-flat);
        border-radius: var(--border-radius-8);
        transition:
            border 350ms,
            box-shadow 350ms;
        box-sizing: border-box;
        padding: var(--gap-16);

        &_overdue {
            background-color: var(--color-white);
            border-left: 4px solid var(--color-red-error);
        }
    }

    &__popup-label {
        display: block;
        margin-bottom: 5px;
    }

    &__popup-content {
        width: auto;
    }

    &__popup-amount {
        margin-bottom: var(--gap-2);
    }

    .overdue-amount {
        color: var(--color-dark-indigo);
    }
}
