import React, { memo, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { Alert } from '@alfalab/core-components/alert';
import { Button } from '@alfalab/core-components/button';
import { Modal } from '@alfalab/core-components/modal';
import { Radio } from '@alfalab/core-components/radio';
import { RadioGroup, type RadioGroupProps } from '@alfalab/core-components/radio-group';
import { Tooltip } from '@alfalab/core-components/tooltip';
import { Typography } from '@alfalab/core-components/typography';

import { OverdraftRepaymentList } from '#/src/constants/overdraft';
import { RepaymentStatusCode } from '#/src/ducks/credit-products/reducer/overdraft-change-repayment';
import { parentOverdraftRepaymentSelector } from '#/src/ducks/credit-products/selectors/overdraft.selectors';
import { type ERepaymentType } from '#/src/types/overdraft';

import './overdraft-repayment-modal.css';

const cn = createCn('overdraft-repayment-modal');

type Props = {
    open: boolean;
    handleClose: () => void;
    handleAcceptRepayment: (repayment: ERepaymentType) => void;
    currentRepaymentType: ERepaymentType;
};

const OverdraftRepaymentModal: React.FC<Props> = ({
    open,
    handleClose,
    handleAcceptRepayment,
    currentRepaymentType,
}) => {
    const repaymentData = useSelector(parentOverdraftRepaymentSelector);

    const [selectedRepayment, setSelectedRepayment] =
        useState<ERepaymentType>(currentRepaymentType);

    useEffect(() => {
        setSelectedRepayment(currentRepaymentType);
    }, [currentRepaymentType]);

    const handleRadioChange: RadioGroupProps['onChange'] = (event, payload) => {
        setSelectedRepayment(payload.value as ERepaymentType);
    };

    const onAccept = () => {
        handleAcceptRepayment(selectedRepayment);
    };

    const onClose = () => {
        setSelectedRepayment(currentRepaymentType);
        handleClose();
    };

    const showWarning =
        repaymentData?.statusData?.statusCode &&
        repaymentData.statusData.statusCode !== RepaymentStatusCode.NotFound;

    return (
        <Modal open={open} onClose={onClose} className={cn()}>
            <Modal.Header hasCloser={true}>
                <Typography.Title tag='div' view='small' font='system'>
                    Тип погашения
                </Typography.Title>
            </Modal.Header>
            <Modal.Content>
                <RadioGroup
                    direction='vertical'
                    name='repayment-radio-group'
                    onChange={handleRadioChange}
                    value={selectedRepayment}
                    className={cn('repayment-list')}
                >
                    {OverdraftRepaymentList.map((el) => (
                        <Radio
                            hint={el.description}
                            label={el.label}
                            value={el.key}
                            key={el.key}
                            name='repayment-radio-group'
                            className={cn('repayment-item', {
                                active: el.key === selectedRepayment,
                            })}
                        />
                    ))}
                </RadioGroup>
                {showWarning && (
                    <Alert view='attention' className={cn('alert')}>
                        Заявка на изменение типа погашения в работе
                    </Alert>
                )}
            </Modal.Content>
            <Modal.Footer>
                {currentRepaymentType === selectedRepayment ? (
                    <Tooltip
                        position='left'
                        trigger='hover'
                        content='Выберите тип погашения отличный от текущего'
                        fallbackPlacements={['bottom', 'top']}
                    >
                        <Button view='primary' size='s' disabled={true}>
                            Сохранить
                        </Button>
                    </Tooltip>
                ) : (
                    <Button
                        view='primary'
                        size='s'
                        onClick={onAccept}
                        disabled={
                            repaymentData?.statusData?.statusCode !== RepaymentStatusCode.NotFound
                        }
                        loading={repaymentData?.changePending}
                    >
                        Сохранить
                    </Button>
                )}
                <Button
                    view='tertiary'
                    size='s'
                    onClick={onClose}
                    disabled={repaymentData?.changePending}
                >
                    Отменить
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default memo(OverdraftRepaymentModal);
