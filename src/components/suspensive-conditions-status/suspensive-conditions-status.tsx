import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type SuspensiveConditionsDeadlineStatus } from 'corp-core-credit-products-api-typescript-services';

import { Link } from '@alfalab/core-components/link';
import { Plate } from '@alfalab/core-components/plate';

import { type ECreditProducts } from '#/src/constants/credit-products';
import { titleByStatus } from '#/src/constants/suspensive-conditions';
import { ETabs } from '#/src/containers/sopuk-line/sopuk-line';
import { goCreditProductPage, goTranchePage } from '#/src/ducks/credit-products/actions';
import { SUSPENSIVE_CONDITIONS_METRICS } from '#/src/metrics';
import { ETrancheTypes } from '#/src/sagas/workers/get-tranche-credit-products-worker';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';
import { getPathByProductType } from '#/src/view-utils/suspensive-conditions';

import './suspensive-conditions-status.css';

type TProps = {
    trancheType?: ETrancheTypes;
    productType: ECreditProducts;
    productCode?: string;
    trancheNumber?: string;
    customerId?: string;
    docNumber?: string;
    dataTestId?: string;
    suspensiveConditionsDeadlineStatus: SuspensiveConditionsDeadlineStatus;
};

const cn = createCn('suspensive-conditions-status');

export const SuspensiveConditionsStatus = ({
    suspensiveConditionsDeadlineStatus,
    trancheNumber,
    docNumber,
    trancheType,
    productType,
    productCode,
    customerId,
    dataTestId = 'suspensive-conditions-status',
}: TProps) => {
    const dispatch = useDispatch();
    const trackAlfaMetrics = useTrackAlfaMetrics();

    const onClick = () => {
        const path = getPathByProductType({
            productType,
            trancheType,
        });

        if (!!path && !!docNumber) {
            if (trancheType == ETrancheTypes.tranche || trancheType == ETrancheTypes.deal) {
                dispatch(
                    goTranchePage({
                        path,
                        docNumber,
                        trancheNumber,
                        customerId,
                        tab: ETabs.suspensiveConditions,
                    }),
                );
            } else {
                dispatch(
                    goCreditProductPage({
                        path,
                        docNumber,
                        customerId,
                        tab: ETabs.suspensiveConditions,
                    }),
                );
            }
        }

        if (!!trackAlfaMetrics && (!!productCode || !!docNumber)) {
            trackAlfaMetrics(SUSPENSIVE_CONDITIONS_METRICS.clickShowConditions, {
                productCode,
                docNumber,
            });
        }
    };

    const handleOnLinkClick: React.ComponentProps<typeof Link>['onClick'] = (event) => {
        event?.stopPropagation?.();
        onClick();
    };

    const handleOnPlateClick: React.ComponentProps<typeof Plate>['onClick'] = (event) => {
        event?.stopPropagation?.();
        onClick();
    };

    useEffect(() => {
        if (!!trackAlfaMetrics && (!!productCode || !!docNumber)) {
            trackAlfaMetrics(SUSPENSIVE_CONDITIONS_METRICS.showStatusPlate, {
                productCode,
                docNumber,
            });
        }
    }, [docNumber, productCode, trackAlfaMetrics]);

    return (
        <Plate
            view={titleByStatus[suspensiveConditionsDeadlineStatus]?.view}
            titleView='light'
            title={
                <React.Fragment>
                    {titleByStatus[suspensiveConditionsDeadlineStatus]?.title}{' '}
                    <Link onClick={handleOnLinkClick}>Посмотреть условия</Link>
                </React.Fragment>
            }
            rounded={false}
            border={true}
            className={cn()}
            dataTestId={dataTestId}
            onClick={handleOnPlateClick}
        />
    );
};
