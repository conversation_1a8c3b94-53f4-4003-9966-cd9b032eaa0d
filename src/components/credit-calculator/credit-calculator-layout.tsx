import React from 'react';

import { Grid } from '@alfalab/core-components/grid';

import { creditCalculatorCn } from './credit-calculator-cn';

type Props = {
    children: React.ReactNode[];
};

export const CreditCalculatorLayout: React.FC<Props> = ({ children }) => (
    <div className={creditCalculatorCn()}>
        <Grid.Row>
            <Grid.Col
                width={{ desktop: 12, mobile: 12 }}
                className={creditCalculatorCn('grid-col')}
            >
                {children[0]}
            </Grid.Col>
        </Grid.Row>
        <Grid.Row className={creditCalculatorCn('grid-row')}>
            <Grid.Col
                width={{ desktop: 'available', tablet: 'available', mobile: 'available' }}
                className={creditCalculatorCn('grid-col')}
            >
                {children[1]}
            </Grid.Col>
            <Grid.Col
                width={{ desktop: 'auto', tablet: 'auto', mobile: 12 }}
                className={creditCalculatorCn('grid-col')}
            >
                {children[2]}
            </Grid.Col>
        </Grid.Row>
    </div>
);
