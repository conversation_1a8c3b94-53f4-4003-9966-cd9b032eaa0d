import React from 'react';

import { Typography } from '@alfalab/core-components/typography';
import { pluralize } from 'arui-private/lib/formatters';

type Props = {
    productsNumber: number;
    className?: string;
};

export const CreditCalculatorProductsNumber = React.memo(({ productsNumber, className }: Props) => (
    <Typography.Title className={className} view='small' weight='bold' tag='div' font='system'>
        Вам {pluralize(productsNumber, ['подходит', 'подходят', 'подходят'])} {productsNumber}&nbsp;
        {pluralize(productsNumber, ['продукт', 'продукта', 'продуктов'])}
    </Typography.Title>
));
