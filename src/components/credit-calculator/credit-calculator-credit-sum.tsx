import React, { useCallback, useEffect, useState } from 'react';
import clamp from 'lodash/clamp';

import { AmountInput } from '@alfalab/core-components/amount-input';
import { SliderInput } from '@alfalab/core-components/slider-input';

import { CREDIT_SUM_STEP } from '#/src/constants/credit-calculator';
import { type SliderValue } from '#/src/types/core-components';
import { convertAmountToText } from '#/src/utils/formatters';

import { creditCalculatorCn } from './credit-calculator-cn';

type Props = {
    min: number;
    max: number;
    value: SliderValue;
    disabled: boolean;
    onChange: (value: SliderValue, isInit?: boolean) => void;
};

export const CreditCalculatorCreditSum = React.memo(
    ({ min, max, value, disabled, onChange }: Props) => {
        const [inputValue, setInputValue] = useState(0);

        const handleChange = useCallback(
            (payload: { value: number }) => {
                onChange(Number(payload.value));
                setInputValue(0);
            },
            [onChange],
        );

        const handleInputChange = useCallback(
            (_: React.ChangeEvent<HTMLInputElement>, payload: { value: SliderValue }) => {
                setInputValue(Number(payload.value));
            },
            [],
        );

        const handleBlur = useCallback(() => {
            if (inputValue) {
                const clampedValue = clamp(Number(inputValue), min, max);

                onChange(clampedValue);
                setInputValue(0);
            }
        }, [onChange, inputValue, min, max]);

        const handleKeyDown: React.KeyboardEventHandler<HTMLInputElement> = useCallback(
            (event) => {
                if (event.key === 'Enter') {
                    handleBlur();
                }
            },
            [handleBlur],
        );

        useEffect(() => {
            if (value === '') {
                onChange(min, true);
                setInputValue(0);
            }
        }, [value, onChange, min]);

        return (
            <SliderInput
                className={creditCalculatorCn('slider', { disabled })}
                value={Number(inputValue || value)}
                sliderValue={Number(value)}
                onInputChange={handleInputChange}
                onSliderChange={handleChange}
                onBlur={handleBlur}
                onKeyDown={handleKeyDown}
                min={min}
                max={max}
                step={CREDIT_SUM_STEP}
                Input={AmountInput}
                size='m'
                pips={{
                    mode: 'values',
                    values: [min, Math.round((min + max) / 2), max],
                    format: {
                        to: (currentValue: number) =>
                            convertAmountToText(currentValue / 100, {
                                hasCurrencySymbol: false,
                                isFloat: false,
                            }),
                    },
                }}
                label='Сумма кредита'
                block={true}
            />
        );
    },
);
