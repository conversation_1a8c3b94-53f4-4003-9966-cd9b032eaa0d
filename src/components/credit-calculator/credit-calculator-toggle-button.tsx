import React, { useCallback } from 'react';

import { Button } from '@alfalab/core-components/button';
import { CashMoneyMIcon } from '@alfalab/icons-glyph/CashMoneyMIcon';

import { type activeCreditCalculator } from '#/src/ducks/credit-calculator/actions';

type Props = {
    className?: string;
    isCreditCalculatorActive: boolean;
    onChange: (...args: Parameters<typeof activeCreditCalculator>) => void;
};

export const CreditCalculatorToggleButton = React.memo(
    ({ className, isCreditCalculatorActive, onChange }: Props) => {
        const toggleCreditCalculatorVisible = useCallback(() => {
            onChange(!isCreditCalculatorActive);
        }, [isCreditCalculatorActive, onChange]);

        return (
            <Button
                className={className}
                size='xxs'
                view='tertiary'
                onClick={toggleCreditCalculatorVisible}
                leftAddons={<CashMoneyMIcon />}
            >
                {isCreditCalculatorActive ? 'Смотреть все продукты' : 'Подобрать новый продукт'}
            </Button>
        );
    },
);
