import React from 'react';

import { Button } from '@alfalab/core-components/button';

type Props = {
    className?: string;
    onChange: () => void;
    disabled: boolean;
};

export const CreditCalculatorResetButton = React.memo(
    ({ className, onChange, disabled }: Props) => (
        <Button
            className={className}
            block={true}
            view='secondary'
            onClick={onChange}
            disabled={disabled}
        >
            Сбросить фильтр
        </Button>
    ),
);
