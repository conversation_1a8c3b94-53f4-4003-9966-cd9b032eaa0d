import React, { useEffect } from 'react';

import { CheckboxGroup } from '@alfalab/core-components/checkbox-group';
import { type OptionShape } from '@alfalab/core-components/select/typings';
import { Tag } from '@alfalab/core-components/tag';

import { creditCalculatorCn } from './credit-calculator-cn';

type Props = {
    options: OptionShape[];
    selected: string;
    onChange: React.ComponentProps<typeof CheckboxGroup>['onChange'];
};

export const CreditCalculatorCreditPurpose = React.memo(
    ({ options, selected, onChange: handleChange }: Props) => {
        useEffect(() => {
            if (options.length && options.every(({ key }) => key !== selected) && handleChange) {
                handleChange(undefined, { checked: true, name: options[0]?.key });
            }
        }, [handleChange, options, selected]);

        return (
            <CheckboxGroup onChange={handleChange} direction='horizontal' type='tag'>
                {options.map((option) => (
                    <Tag
                        key={option.key}
                        name={option.key}
                        checked={selected === option.key}
                        className={creditCalculatorCn('tag')}
                    >
                        {option.content}
                    </Tag>
                ))}
            </CheckboxGroup>
        );
    },
);
