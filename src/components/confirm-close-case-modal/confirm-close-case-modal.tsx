import React from 'react';
import { createCn } from 'bem-react-classname';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Modal } from '@alfalab/core-components/modal';
import { Text } from '@alfalab/core-components/typography/text';
import { Title } from '@alfalab/core-components/typography/title';

import { NBSP } from '#/src/constants/unicode-symbols';

import './confirm-close-case-modal.css';

const cn = createCn('confirm-close-case-modal');

type Props = {
    isModalVisible: boolean;
    onCancel: () => void;
    onContinue: () => void;
    showDesktopModal: boolean;
};

const ConfirmCloseCaseModal: React.FC<Props> = ({
    isModalVisible,
    onCancel,
    onContinue,
    showDesktopModal,
}) => {
    if (showDesktopModal) {
        return (
            <Modal open={isModalVisible} hasCloser={true} onClose={onCancel} size={500}>
                <Modal.Header>
                    <Title tag='div' color='primary' view='small' font='system'>
                        Удалить заявку?
                    </Title>
                </Modal.Header>
                <Modal.Content>
                    <Text color='primary'>
                        Придётся заполнять её заново, если понадобится кредит
                    </Text>
                </Modal.Content>
                <Modal.Footer>
                    <Button size='s' view='primary' onClick={onContinue}>
                        Удалить
                    </Button>
                    <Button size='s' view='secondary' onClick={onCancel}>
                        Оставить
                    </Button>
                </Modal.Footer>
            </Modal>
        );
    }

    return (
        <BottomSheet
            open={isModalVisible}
            hasCloser={true}
            onClose={onCancel}
            title='Удалить заявку?'
        >
            <Text>Придётся заполнять её заново, если{NBSP}понадобится кредит</Text>
            <div className={cn('buttons')}>
                <Button size='m' view='secondary' onClick={onCancel} block={true}>
                    Оставить
                </Button>
                <Button size='m' view='primary' onClick={onContinue} block={true}>
                    Удалить
                </Button>
            </div>
        </BottomSheet>
    );
};

export default ConfirmCloseCaseModal;
