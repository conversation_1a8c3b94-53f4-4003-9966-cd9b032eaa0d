@import '@alfalab/core-components/vars';

.product-with-tranches-debt-pane {
    width: 100%;

    &_eliminateNegativeMargins {
        .pane-group {
            margin: 0;
        }
    }

    &_redesigned {
        display: flex;
        flex-direction: column;
        max-width: 811px;
        background-color: var(--color-white);
        border-radius: var(--border-radius-8);
        transition:
            border 350ms,
            box-shadow 350ms;
        box-sizing: border-box;
        padding: var(--gap-32) 0 0 var(--gap-32);
        box-shadow: var(--shadow-xs);

        @media (--small-only) {
            padding: var(--gap-16) 0 0 var(--gap-16);
            width: auto;
        }
    }

    &__pane-group {
        padding: var(--gap-24) 0 var(--gap-8) 0;

        @media (--small-only) {
            padding: var(--gap-24) 0 0 0;
        }
    }

    &__sopuk-pane-group {
        padding: 0 0 var(--gap-8) 0;

        @media (--small-only) {
            padding: var(--gap-8) 0 0 0;
        }
    }

    &__pane-item {
        &_inflexible {
            min-width: 205px;
        }
    }

    &__amount {
        &_danger {
            color: var(--color-red-brand);
        }
    }
}
