import React, { useMemo } from 'react';
import { connect } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type Amount as TAmount } from 'thrift-services/entities';
import { type UnixEpoch } from 'thrift-services/utils';

import { Typography } from '@alfalab/core-components/typography';

import OverdraftRepaymentProcedureCollapse from '#/src/components/overdraft-repayment-procedure-collapse';
import { type ApplicationState } from '#/src/ducks/application-state';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { SopukPaneMmb } from '#/src/mmb/containers/main-page-mmb/main-page-products-mmb/main-page-credit-products-mmb/credit-products-mmb/credit-product-item-mmb/sopuk-pane-mmb';
import { isAmountGreaterThanZero } from '#/src/utils/amount';
import { PaneAmount } from '#/src/view-utils/credit-pane';
import { interestTooltipText } from '#/src/view-utils/credit-products';

import { ECreditProducts } from '../../constants/credit-products';
import { DAY_MONTH_YEAR_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';
import AmountPure from '../ui/amount-pure';
import AvailableAmountPanel from '../ui/available-amount-panel';
import PaneGroup from '../ui/pane-group';
import PaneItem from '../ui/pane-item';

import './product-with-tranches-debt-pane.css';

const cn = createCn('product-with-tranches-debt-pane');

type TOwnProps = {
    productType: ECreditProducts;
    totalToPay?: TAmount;
    totalInterestOverdue?: TAmount;
    totalFine?: TAmount;
    fineDebt?: TAmount;
    fineInterest?: TAmount;
    limit?: TAmount;
    availableAmount?: TAmount;
    actualDate?: UnixEpoch;
    onTogglePopup?: (popupName: string) => void;
    closestTranchePaymentAmount?: TAmount;
    overdueDebt?: TAmount;
    overdueInterest?: TAmount;
    withTranches?: boolean;
    isClearOverdraft?: boolean;
    debtToPay?: TAmount;
    isFromMMB?: boolean;
    fromDate?: UnixEpoch;
    toDate?: UnixEpoch;
    isRedesigned?: boolean;
    creditAmountsByCurrency?: TAmount[];
    eliminateNegativeMargins?: boolean;
};

function mapStateToProps(state: ApplicationState) {
    return {
        currentTime: currentTimeSelector(state),
    };
}

type TProps = TOwnProps & ReturnType<typeof mapStateToProps>;

// eslint-disable-next-line complexity
const ProductWithTranchesDebtPane: React.FC<TProps> = ({
    totalToPay,
    totalInterestOverdue,
    totalFine,
    actualDate,
    onTogglePopup,
    productType,
    currentTime,
    closestTranchePaymentAmount,
    overdueDebt,
    overdueInterest,
    withTranches,
    fineInterest,
    fineDebt,
    isClearOverdraft,
    debtToPay,
    isFromMMB,
    availableAmount,
    limit,
    fromDate,
    toDate,
    isRedesigned = false,
    creditAmountsByCurrency,
    eliminateNegativeMargins = false,
}: TProps) => {
    const renderAmount = (amount: TAmount, danger?: boolean) => (
        <Typography.Text view='primary-large'>
            <AmountPure
                className={cn('amount', {
                    danger: !!danger,
                })}
                bold={danger ? 'full' : undefined}
                value={amount}
            />
        </Typography.Text>
    );

    const currentTotalInterest = useMemo(() => {
        if (overdueInterest && totalInterestOverdue) {
            return {
                ...totalInterestOverdue,
                amount: (totalInterestOverdue.amount ?? 0) - (overdueInterest.amount ?? 0),
            };
        }

        return totalInterestOverdue;
    }, [totalInterestOverdue, overdueInterest]);

    const currentTotalOverdue = useMemo(() => {
        if (overdueDebt) {
            return {
                ...overdueDebt,
                amount: (overdueDebt.amount ?? 0) + (overdueInterest?.amount ?? 0),
            };
        }

        return undefined;
    }, [overdueDebt, overdueInterest]);

    const currentClosestPaymentAmount = useMemo(
        () => ({
            ...closestTranchePaymentAmount,
            amount:
                (closestTranchePaymentAmount?.amount ?? 0) -
                (currentTotalInterest?.amount ?? 0) -
                (currentTotalOverdue?.amount ?? 0),
        }),
        [closestTranchePaymentAmount, currentTotalInterest, currentTotalOverdue],
    );

    const isTranchesClosestPaymentVisible = useMemo(
        () =>
            (productType === ECreditProducts.OVERDRAFT &&
                !withTranches &&
                debtToPay?.amount !== undefined) ||
            (productType === ECreditProducts.OVERDRAFT && !withTranches && isClearOverdraft),
        [productType, withTranches, debtToPay, isClearOverdraft],
    );

    const isNZClosestPaymentVisible = useMemo(
        () =>
            (productType === ECreditProducts.OVERDRAFT &&
                withTranches &&
                isAmountGreaterThanZero(closestTranchePaymentAmount)) ||
            (productType === ECreditProducts.OVERDRAFT && withTranches && isClearOverdraft),
        [productType, withTranches, closestTranchePaymentAmount, isClearOverdraft],
    );

    const isOverdueVisible = useMemo(
        () =>
            (productType === ECreditProducts.OVERDRAFT && isAmountGreaterThanZero(overdueDebt)) ||
            (productType === ECreditProducts.OVERDRAFT && isClearOverdraft),
        [productType, overdueDebt, isClearOverdraft],
    );

    const paneFooter = useMemo(() => {
        if (productType === ECreditProducts.OVERDRAFT && isFromMMB) {
            return <OverdraftRepaymentProcedureCollapse />;
        }
    }, [productType, isFromMMB]);

    return (
        <div className={cn({ redesigned: isRedesigned, eliminateNegativeMargins })}>
            {isRedesigned && !!actualDate && productType !== ECreditProducts.SOPUK && (
                <React.Fragment>
                    <Typography.Text view='caps' weight='bold' color='secondary'>
                        на&nbsp;
                        {dateToCustomFormat(currentTime, actualDate, DAY_MONTH_YEAR_FORMAT)}
                    </Typography.Text>
                    <AvailableAmountPanel
                        productType={productType}
                        availableAmount={availableAmount}
                        limit={limit}
                    />
                </React.Fragment>
            )}
            {productType === ECreditProducts.SOPUK && (
                <SopukPaneMmb creditAmountsByCurrency={creditAmountsByCurrency || []} />
            )}
            <PaneGroup
                className={
                    isRedesigned
                        ? cn(
                              productType === ECreditProducts.SOPUK
                                  ? 'sopuk-pane-group'
                                  : 'pane-group',
                          )
                        : ''
                }
                withDivider={isRedesigned}
                withoutBorder={true}
                shadowed={productType === ECreditProducts.OVERDRAFT}
                justify='left'
                footer={paneFooter}
            >
                {!!totalToPay && (
                    <PaneItem
                        title='Задолженность'
                        titleSize='secondary-large'
                        text={<PaneAmount value={totalToPay} />}
                    />
                )}
                {productType === ECreditProducts.OVERDRAFT && !!currentTotalInterest && (
                    <PaneItem
                        title='Начисленные проценты'
                        titleSize='secondary-large'
                        text={renderAmount(currentTotalInterest)}
                    />
                )}
                {productType === ECreditProducts.OVERDRAFT && !!totalFine && (
                    <PaneItem
                        title='Неустойка за просрочку'
                        titleSize='secondary-large'
                        text={renderAmount(totalFine)}
                    />
                )}
                {isTranchesClosestPaymentVisible && (
                    <PaneItem
                        title='Ближайшая сумма к погашению'
                        titleSize='secondary-large'
                        text={renderAmount({
                            ...debtToPay,
                            amount:
                                (debtToPay?.amount ?? 0) + (currentTotalInterest?.amount ?? 0) <
                                (overdueDebt?.amount ?? 0) + (overdueInterest?.amount ?? 0)
                                    ? (overdueDebt?.amount ?? 0) + (overdueInterest?.amount ?? 0)
                                    : (debtToPay?.amount ?? 0) +
                                      (currentTotalInterest?.amount ?? 0),
                        })}
                        textForTooltip={
                            <React.Fragment>
                                {isAmountGreaterThanZero(debtToPay) && (
                                    <React.Fragment>
                                        <div>
                                            <AmountPure value={debtToPay} />
                                            &nbsp;&mdash;&nbsp;задолженность
                                        </div>
                                        <Typography.Text view='primary-small' color='primary'>
                                            ближайшая к погашению
                                            <br />
                                        </Typography.Text>
                                    </React.Fragment>
                                )}
                                {isAmountGreaterThanZero(overdueDebt) && (
                                    <React.Fragment>
                                        <div>
                                            <AmountPure
                                                value={{
                                                    ...overdueDebt,
                                                    amount:
                                                        (overdueDebt?.amount ?? 0) +
                                                        (overdueInterest?.amount ?? 0),
                                                }}
                                            />
                                            &nbsp;&mdash;&nbsp;просроченная
                                        </div>
                                        <Typography.Text view='primary-small' color='primary'>
                                            задолженность
                                            <br />
                                        </Typography.Text>
                                    </React.Fragment>
                                )}
                                {isAmountGreaterThanZero(currentTotalInterest) && (
                                    <React.Fragment>
                                        <div>
                                            <AmountPure value={currentTotalInterest} />
                                            &nbsp;&mdash;&nbsp;проценты
                                        </div>
                                        <Typography.Text view='primary-small' color='primary'>
                                            на всю задолженность
                                            <br />
                                        </Typography.Text>
                                    </React.Fragment>
                                )}
                            </React.Fragment>
                        }
                        withTooltip={!isClearOverdraft}
                        popupName='fineForOverdueTooltip'
                        onTogglePopup={onTogglePopup}
                    />
                )}
                {isNZClosestPaymentVisible && (
                    <PaneItem
                        title='Ближайшая сумма к погашению'
                        titleSize='secondary-large'
                        text={renderAmount(closestTranchePaymentAmount ?? {})}
                        textForTooltip={
                            <React.Fragment>
                                {isAmountGreaterThanZero(currentClosestPaymentAmount) && (
                                    <React.Fragment>
                                        <div>
                                            <AmountPure value={currentClosestPaymentAmount} />
                                            &nbsp;&mdash;&nbsp;задолженность
                                        </div>
                                        <Typography.Text view='primary-small' color='primary'>
                                            ближайшая к погашению
                                            <br />
                                        </Typography.Text>
                                    </React.Fragment>
                                )}
                                {isAmountGreaterThanZero(currentTotalOverdue) && (
                                    <React.Fragment>
                                        <div>
                                            <AmountPure value={currentTotalOverdue} />
                                            &nbsp;&mdash;&nbsp;просроченная
                                        </div>
                                        <Typography.Text view='primary-small' color='primary'>
                                            задолженность
                                            <br />
                                        </Typography.Text>
                                    </React.Fragment>
                                )}
                                {isAmountGreaterThanZero(currentTotalInterest) && (
                                    <React.Fragment>
                                        <div>
                                            <AmountPure value={currentTotalInterest} />
                                            &nbsp;&mdash;&nbsp;проценты
                                        </div>
                                        <Typography.Text view='primary-small' color='primary'>
                                            на всю задолженность
                                            <br />
                                        </Typography.Text>
                                    </React.Fragment>
                                )}
                            </React.Fragment>
                        }
                        withTooltip={!isClearOverdraft}
                        popupName='fineForOverdueTooltip'
                        onTogglePopup={onTogglePopup}
                    />
                )}
                {isOverdueVisible && (
                    <PaneItem
                        title='Просроченная задолженность'
                        titleSize='secondary-large'
                        text={renderAmount(
                            {
                                ...overdueDebt,
                                amount: (overdueDebt?.amount ?? 0) + (overdueInterest?.amount ?? 0),
                            },
                            !isClearOverdraft,
                        )}
                        textForTooltip={
                            <React.Fragment>
                                <div>
                                    <AmountPure value={overdueDebt} />
                                    &nbsp;&mdash;&nbsp;просроченный <br />
                                </div>
                                <Typography.Text view='primary-small' color='primary'>
                                    основной долг
                                    <br />
                                </Typography.Text>
                                {isAmountGreaterThanZero(overdueInterest) && (
                                    <React.Fragment>
                                        <div>
                                            <AmountPure value={overdueInterest} />
                                            &nbsp;&mdash;&nbsp;просроченные <br />
                                        </div>
                                        <Typography.Text view='primary-small' color='primary'>
                                            проценты
                                            <br />
                                        </Typography.Text>
                                    </React.Fragment>
                                )}
                            </React.Fragment>
                        }
                        withTooltip={!isClearOverdraft}
                        popupName='arrearsTooltip'
                        onTogglePopup={onTogglePopup}
                    />
                )}
                {productType !== ECreditProducts.OVERDRAFT && !!totalInterestOverdue && (
                    <PaneItem
                        className={cn('pane-item', { inflexible: true })}
                        title='Начислено процентов'
                        titleSize='secondary-large'
                        text={<PaneAmount value={totalInterestOverdue} />}
                        withTooltip={true}
                        textForTooltip={
                            <Typography.Text view='primary-small' color='primary'>
                                {interestTooltipText[productType]}
                            </Typography.Text>
                        }
                        popupName='interestTooltip'
                        onTogglePopup={onTogglePopup}
                    />
                )}
                {productType !== ECreditProducts.OVERDRAFT && !!totalFine && (
                    <PaneItem
                        className={cn('pane-item', { inflexible: true })}
                        title='Неустойка за просрочку'
                        titleSize='secondary-large'
                        text={<PaneAmount value={totalFine} />}
                        textForTooltip={
                            <React.Fragment>
                                <Typography.Text view='primary-small' color='primary'>
                                    Неустойка: <br />
                                </Typography.Text>
                                {!!fineDebt && (
                                    <div>
                                        <AmountPure value={fineDebt} />
                                        &nbsp;&mdash; по основному долгу
                                    </div>
                                )}
                                {!!fineInterest && (
                                    <div>
                                        <AmountPure value={fineInterest} />
                                        &nbsp;&mdash; по процентам
                                    </div>
                                )}
                            </React.Fragment>
                        }
                        withTooltip={true}
                        popupName='fineForOverdueTooltip'
                        onTogglePopup={onTogglePopup}
                    />
                )}
                {!!fromDate && productType !== ECreditProducts.OVERDRAFT && (
                    <PaneItem
                        title='Дата открытия'
                        text={
                            <Typography.Title view='xsmall' font='system' tag='h2'>
                                {dateToCustomFormat(currentTime, fromDate, DAY_MONTH_YEAR_FORMAT)}
                            </Typography.Title>
                        }
                    />
                )}
                {!!toDate && productType !== ECreditProducts.OVERDRAFT && (
                    <PaneItem
                        title='Дата окончания'
                        text={
                            <Typography.Title view='xsmall' font='system' tag='h2'>
                                {dateToCustomFormat(currentTime, toDate, DAY_MONTH_YEAR_FORMAT)}
                            </Typography.Title>
                        }
                    />
                )}
            </PaneGroup>
        </div>
    );
};

export default connect(mapStateToProps)(ProductWithTranchesDebtPane);
