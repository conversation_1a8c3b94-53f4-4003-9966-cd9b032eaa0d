import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Notification } from '@alfalab/core-components/notification';
import { NBSP } from 'arui-private/lib/formatters';

import OverdraftRepaymentModal from '#/src/components/overdraft-repayment-modal';
import OverdraftSettingPane, {
    type OverdraftPaneStatus,
} from '#/src/components/overdraft-setting-pane';
import { OverdraftRepaymentDescription, OverdraftRepaymentText } from '#/src/constants/overdraft';
import {
    clearOverdraftRepaymentStatus,
    getOverdraftRepaymentStatusStart,
    setOverdraftRepaymentStart,
} from '#/src/ducks/credit-products/actions';
import { RepaymentStatusCode } from '#/src/ducks/credit-products/reducer/overdraft-change-repayment';
import {
    parentOverdraftDealIdSelector,
    parentOverdraftRepaymentSelector,
    parentOverdraftRepaymentTypeSelector,
} from '#/src/ducks/credit-products/selectors/overdraft.selectors';
import { ERepaymentStatus, ERepaymentType } from '#/src/types/overdraft';

const inWorkStatus: OverdraftPaneStatus = {
    text: 'Заявка на изменение типа погашения в работе',
    view: 'warning',
};

const OverdraftRepaymentTypePane: React.FC = () => {
    const dispatch = useDispatch();
    const repaymentType = useSelector(parentOverdraftRepaymentTypeSelector);
    const repaymentData = useSelector(parentOverdraftRepaymentSelector);
    const dealId = useSelector(parentOverdraftDealIdSelector);

    const [modalOpen, setModalOpen] = useState(false);

    useEffect(() => {
        if (!repaymentData && dealId) {
            dispatch(getOverdraftRepaymentStatusStart(dealId));
        }
    }, [repaymentData, dealId]);

    const handleClose = useCallback(() => {
        setModalOpen(false);
    }, []);

    const handleOpen = useCallback(() => {
        setModalOpen(true);
    }, []);

    const currentRepaymentType: ERepaymentType = useMemo(
        () => (repaymentType === 'N' ? ERepaymentType.EOD : ERepaymentType.Online),
        [repaymentType],
    );

    const handleAcceptRepayment = useCallback(
        (repayment: ERepaymentType) => {
            if (dealId) {
                dispatch(setOverdraftRepaymentStart(dealId, repayment));

                setModalOpen(false);
            }
        },
        [dealId],
    );

    const clearRepaymentStatus = useCallback(() => {
        if (dealId) {
            dispatch(clearOverdraftRepaymentStatus(dealId));
        }
    }, [dealId]);

    useEffect(() => {
        if (repaymentData?.changeStatus === ERepaymentStatus.SET_SUCCESSFUL) {
            setModalOpen(false);
        }
    }, [repaymentData, clearRepaymentStatus]);

    const paneStatus: OverdraftPaneStatus | undefined = useMemo(() => {
        if (
            repaymentData?.statusData?.statusCode === RepaymentStatusCode.NotFound ||
            repaymentData?.statusData?.statusCode === RepaymentStatusCode.Decline ||
            !repaymentData?.statusData?.statusCode
        ) {
            return undefined;
        }

        return inWorkStatus;
    }, [repaymentData]);

    const notificationError: string = useMemo(
        () =>
            repaymentData?.changeData?.statusCode === RepaymentStatusCode.ServerError ||
            !repaymentData?.changeData?.error
                ? 'Попробуйте ещё раз позже'
                : repaymentData?.changeData?.error,
        [repaymentData],
    );

    return (
        <React.Fragment>
            <OverdraftSettingPane
                title='Тип погашения:'
                settingValue={OverdraftRepaymentText[currentRepaymentType]}
                description={OverdraftRepaymentDescription[currentRepaymentType]}
                status={paneStatus}
                onEditClick={handleOpen}
                pending={false}
            />
            <OverdraftRepaymentModal
                open={modalOpen}
                handleClose={handleClose}
                handleAcceptRepayment={handleAcceptRepayment}
                currentRepaymentType={currentRepaymentType}
            />
            <Notification
                badge='positive'
                title='Заявка принята'
                visible={repaymentData?.changeData?.statusCode === RepaymentStatusCode.New}
                onClose={clearRepaymentStatus}
                onCloseTimeout={clearRepaymentStatus}
            >
                Она будет обработана в течение 8 часов
            </Notification>
            <Notification
                badge='negative'
                title={`Не удалось изменить тип${NBSP}погашения`}
                visible={
                    !!repaymentData?.changeData?.error ||
                    repaymentData?.changeStatus === ERepaymentStatus.ERROR
                }
                onClose={clearRepaymentStatus}
                onCloseTimeout={clearRepaymentStatus}
            >
                {notificationError}
            </Notification>
        </React.Fragment>
    );
};

export default memo(OverdraftRepaymentTypePane);
