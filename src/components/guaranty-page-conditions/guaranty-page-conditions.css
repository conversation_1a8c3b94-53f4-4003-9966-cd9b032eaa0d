@import '@alfalab/core-components/vars';

.guaranty {
    &__conditions-container {
        padding: var(--gap-32);
        display: grid;
        gap: var(--gap-24);
        margin-top: var(--gap-24);

        @media (min-width: 700px) {
            grid-template-columns: 1fr 1fr;
            gap: var(--gap-16);
        }

        @media (min-width: 1350px) {
            grid-template-columns: 1fr 1fr 1fr 1fr;
        }

        @media (max-width: 699px) {
            gap: var(--gap-24);
        }
    }

    &__condition-item {
        width: 232px;

        @media (min-width: 1250px) {
            &:not(:first-child):not(:last-child) {
                border-left: 1px solid var(--color-dark-indigo-15-flat);
                padding-left: var(--gap-16);
            }
        }

        @media (min-width: 700px) {
            &:nth-child(even) {
                border-left: 1px solid var(--color-dark-indigo-15-flat);
                padding-left: var(--gap-16);
            }
        }

        @media (max-width: 1249px) and (min-width: 700px) {
            &:first-child,
            &:nth-child(2) {
                margin-bottom: var(--gap-20);
            }

            &#tranche_term,
            &#tranche_date {
                margin: var(--gap-0);
            }
        }
    }

    &__hide-text {
        overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
