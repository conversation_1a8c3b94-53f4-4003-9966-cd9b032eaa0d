import React from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type Amount } from 'thrift-services/entities';
import { type UnixEpoch } from 'thrift-services/utils';

import { Tooltip } from '@alfalab/core-components/tooltip';
import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate } from 'arui-private/background-plate';

import { currentTimeSelector } from '#/src/ducks/settings/selectors';

import { DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';
import AmountPure from '../ui/amount-pure';

import './guaranty-page-conditions.css';

type TOwnProps = {
    feeRate?: number;
    fromDate?: UnixEpoch;
    toDate?: UnixEpoch;
    sum?: Amount;
    beneficiary?: string;
    insuranceDate?: UnixEpoch;
    parentDocNumber?: string;
};

type TProps = TOwnProps;

const cn = createCn('guaranty');

const GuarantyPageConditions: React.FC<TProps> = ({
    fromDate,
    toDate,
    feeRate,
    sum,
    beneficiary,
    insuranceDate,
}) => {
    const currentTime = useSelector(currentTimeSelector);

    return (
        <BackgroundPlate className={cn('conditions-container')} data-test-id='conditions-pane'>
            {!!sum && (
                <div className={cn('condition-item')} data-test-id='sum'>
                    <Typography.Text className={cn('text')} view='component' color='secondary'>
                        Сумма
                    </Typography.Text>
                    <Typography.Text tag='div' view='primary-large' weight='medium'>
                        <AmountPure value={sum} transparentMinor={false} />
                    </Typography.Text>
                </div>
            )}
            {!!insuranceDate && (
                <div className={cn('condition-item')} data-test-id='insurance-date'>
                    <Typography.Text className={cn('text')} view='component' color='secondary'>
                        Дата выдачи
                    </Typography.Text>
                    <Typography.Text tag='div' view='primary-large' weight='medium'>
                        {dateToCustomFormat(currentTime, insuranceDate, DATE_FORMAT)}
                    </Typography.Text>
                </div>
            )}
            {!!fromDate && !!toDate && (
                <div className={cn('condition-item')} data-test-id='term'>
                    <Typography.Text className={cn('text')} view='component' color='secondary'>
                        Действие гарантии
                    </Typography.Text>
                    <Typography.Text tag='div' view='primary-large' weight='medium'>
                        {dateToCustomFormat(currentTime, fromDate, DATE_FORMAT)} -{' '}
                        {dateToCustomFormat(currentTime, toDate, DATE_FORMAT)}
                    </Typography.Text>
                </div>
            )}
            {!!feeRate && (
                <div className={cn('condition-item')} data-test-id='fee-rate'>
                    <Typography.Text className={cn('text')} view='component' color='secondary'>
                        Ставка вознаграждения
                    </Typography.Text>
                    <Typography.Text tag='div' view='primary-large' weight='medium'>
                        {feeRate}% годовых
                    </Typography.Text>
                </div>
            )}
            {!!beneficiary && (
                <div className={cn('condition-item')} data-test-id='beneficiary'>
                    <Typography.Text className={cn('text')} view='component' color='secondary'>
                        Бенефициар
                    </Typography.Text>
                    <Tooltip content={beneficiary} trigger='hover' position='top' zIndex={1000}>
                        <Typography.Text
                            tag='div'
                            view='primary-large'
                            weight='medium'
                            rowLimit={1}
                        >
                            {beneficiary}
                        </Typography.Text>
                    </Tooltip>
                </div>
            )}
        </BackgroundPlate>
    );
};

export default GuarantyPageConditions;
