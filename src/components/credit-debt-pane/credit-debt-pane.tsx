import React from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type Amount } from 'thrift-services/entities';

import { Divider } from '@alfalab/core-components/divider';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';

import { earlyRepaymentButtonStates } from '#/src/constants/credit-processing';
import { type ECreditProducts } from '#/src/constants/credit-products';
import { EarlyRepaymentButton } from '#/src/containers/debt-pane-summary/debt-pane-early-repayment-button/debt-pane-early-repayment-button';
import { earlyRepaymentButtonStateSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { type ETrancheTypes } from '#/src/sagas/workers/get-tranche-credit-products-worker';

import AmountPure from '../ui/amount-pure';
import PrintOperationsButton from '../ui/print-operations-button/print-operations-button';

import './credit-debt-pane.css';

type TOwnProps = {
    type?: ECreditProducts | ETrancheTypes;
    overdueInterest?: Amount;
    overdueDebt?: Amount;
    totalDebt?: Amount;
    totalFine?: Amount;
    docNumber?: string;
    onShowEarlyPay?: () => void;
    onPrintOperationsSidebar: () => void;
    isStatementAllowed: boolean | undefined;
    productCode?: string;
};

const cn = createCn('debt-container');

const CreditDebtPane: React.FC<TOwnProps> = ({
    totalDebt,
    totalFine,
    docNumber = '',
    overdueInterest,
    overdueDebt,
    onShowEarlyPay,
    isStatementAllowed,
    onPrintOperationsSidebar,
}) => {
    const [isTablet] = useMatchMedia('--tablet-m');
    const [isMobile] = useMatchMedia('--mobile');
    const buttonState = useSelector(earlyRepaymentButtonStateSelector);
    const isEarlyRepaymentButtonHidden = buttonState === earlyRepaymentButtonStates.hidden;
    const shouldHideDebtButtons = isMobile && isEarlyRepaymentButtonHidden;

    const Container = isTablet ? BackgroundPlate : 'div';

    return (
        <Container
            {...(isTablet && {
                view: BackgroundPlateView.Primary,
                className: cn(),
            })}
            data-test-id='debt-pane'
        >
            <div className={cn('debt-heading')}>
                <div data-test-id='total-debt'>
                    <Typography.Text view='primary-medium' color='secondary'>
                        Осталось выплатить
                    </Typography.Text>
                    <Typography.Title tag='div' view='medium' font='system'>
                        <AmountPure value={totalDebt} transparentMinor={false} />
                    </Typography.Title>
                </div>
                {!shouldHideDebtButtons && (
                    <div className={cn('debt-buttons')}>
                        <EarlyRepaymentButton
                            docNumber={docNumber}
                            targetClassName={cn('button')}
                            className={cn('button')}
                            onShowEarlyPay={onShowEarlyPay}
                        />
                        {isStatementAllowed && (
                            <PrintOperationsButton onToggle={onPrintOperationsSidebar} />
                        )}
                    </div>
                )}
            </div>
            <Divider />
            <div className={cn('debt-info')}>
                <div className={cn('debt-item')} data-test-id='overdue-debt'>
                    <Typography.Text className={cn('text')} view='component' color='secondary'>
                        Основной долг
                    </Typography.Text>
                    <Typography.Title view='xsmall' font='system' tag='div'>
                        <AmountPure value={overdueDebt} transparentMinor={false} />
                    </Typography.Title>
                </div>
                <div className={cn('debt-item')} data-test-id='overdue-interest'>
                    <Typography.Text className={cn('text')} view='component' color='secondary'>
                        Проценты
                    </Typography.Text>
                    <Typography.Title view='xsmall' font='system' tag='div'>
                        <AmountPure value={overdueInterest} transparentMinor={false} />
                    </Typography.Title>
                </div>
                {(totalFine?.amount ?? 0) > 0 && (
                    <div className={cn('debt-item')} data-test-id='total-fine'>
                        <Typography.Text className={cn('text')} view='component' color='secondary'>
                            Неустойка
                        </Typography.Text>
                        <Typography.Title view='xsmall' font='system' tag='div'>
                            <AmountPure value={totalFine} transparentMinor={false} />
                        </Typography.Title>
                    </div>
                )}
            </div>
        </Container>
    );
};

export default CreditDebtPane;
