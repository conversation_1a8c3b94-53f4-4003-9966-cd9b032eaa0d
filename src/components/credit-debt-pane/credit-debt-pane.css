@import '@alfalab/core-components/vars';

.debt-container {
    padding: var(--gap-32);

    &__debt-heading {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--gap-24);

        @media (max-width: 1025px) {
            flex-direction: column;
        }
    }

    &__debt-info {
        display: grid;
        grid-template-columns: 232px 232px 232px;
        margin-top: var(--gap-32);
        gap: var(--gap-l);

        @media (max-width: 1025px) {
            grid-template-columns: 232px 232px;
        }

        @media (--small-only) {
            margin-top: var(--gap-16);
            gap: var(--gap-xl);
            grid-template-columns: 232px;
        }
    }

    &__debt-item {
        @media (min-width: 1025px) {
            &:not(:first-child) {
                border-left: 1px solid var(--color-dark-indigo-15-flat);
                padding-left: var(--gap-16);
            }
        }

        @media (max-width: 1025px) {
            &:nth-child(even) {
                border-left: 1px solid var(--color-dark-indigo-15-flat);
                padding-left: var(--gap-16);
            }
        }

        @media (--small-only) {
            &:nth-child(even) {
                border-left: none;
                padding-left: 0;
            }
        }
    }

    &__text {
        display: block;
        margin-bottom: calc(var(--gap-4) + var(--gap-2));
    }

    &__button {
        @media (--small-only) {
            width: 100%;
        }
    }

    &__debt-buttons {
        display: flex;
        align-items: center;
        gap: var(--gap-m);

        @media (max-width: 1025px) {
            margin-top: var(--gap-16);
        }

        @media (--small-only) {
            margin-top: var(--gap-16);
            flex-direction: column;
        }
    }
}
