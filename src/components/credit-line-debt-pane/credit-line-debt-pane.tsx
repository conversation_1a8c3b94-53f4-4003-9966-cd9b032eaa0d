import React, { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { Divider } from '@alfalab/core-components/divider';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';
import CorporateAmount from 'arui-private/corporate-amount';
import { NBSP } from 'arui-private/lib/formatters';

import { selectedProductSelector } from '#/src/ducks/credit-products/selectors/credit-line.selectors';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { CREDIT_LINE_IN_USE_METRICS } from '#/src/metrics';
import { GetTrancheButton } from '#/src/mmb/containers/main-page-mmb/main-page-products-mmb/main-page-credit-products-mmb/credit-products-mmb/credit-product-item-mmb/get-tranche-button';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import { DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';
import AmountPure from '../ui/amount-pure';
import { PrintOperationsButtonAdaptive } from '../ui/print-operations-button-adaptive';

import './credit-line-debt-pane.css';

const cn = createCn('credit-line-debt-pane');

export const CreditLineDebtPane = ({
    togglePrintOperationsSidebar,
}: {
    togglePrintOperationsSidebar: () => void;
}) => {
    const [isDesktop] = useMatchMedia('--desktop-m');
    const [isTablet] = useMatchMedia('--tablet-m');
    const [isMobile] = useMatchMedia('--mobile');
    const trackAlfaMetrics = useTrackAlfaMetrics();

    const currentTime = useSelector(currentTimeSelector);
    const selectedProduct = useSelector(selectedProductSelector);
    const { creditLine } = selectedProduct.product;

    const totalLoanSum = creditLine?.summary?.totalLoanSum;
    const totalDebt = creditLine?.summary?.totalDebt;
    const totalFine = creditLine?.summary?.totalFine;
    const totalInterestOverdue = creditLine?.summary?.totalInterestOverdue;

    const fromDate = creditLine?.requisites?.fromDate;
    const toDate = creditLine?.requisites?.toDate;

    const trancheEnd = creditLine?.requisites?.trancheEnd;

    const isTrancheAllowedMMB = !!creditLine?.isTrancheAllowedMMB;
    const isTrancheAllowed = !!creditLine?.isTrancheAllowed;
    const isGetTrancheButtonVisible = isTrancheAllowedMMB || isTrancheAllowed;

    const handleOnGetTrancheButtonClick = useCallback(() => {
        if (trackAlfaMetrics) {
            trackAlfaMetrics(CREDIT_LINE_IN_USE_METRICS.getTrancheButtonClick, {
                agreementNumber: creditLine?.docNumber,
                isMmb: isTrancheAllowedMMB,
            });
        }
    }, [isTrancheAllowedMMB, creditLine?.docNumber, trackAlfaMetrics]);

    const Container = isTablet ? BackgroundPlate : 'div';

    const shouldHideButtons = useMemo(() => {
        // На мобилке мы скрываем группу кнопок, когда нет кнопки "Новый транш" (выписки не доступны на мобилке)
        if (isMobile) {
            return !isGetTrancheButtonVisible;
        }

        // Также нам не нужно показывать группу кнопок, когда обе кнопки недоступны
        return !isGetTrancheButtonVisible && !creditLine?.isStatementAllowed;
    }, [creditLine?.isStatementAllowed, isGetTrancheButtonVisible, isMobile]);

    return (
        <Container
            {...(isTablet && {
                view: BackgroundPlateView.Primary,
                className: cn(),
            })}
            data-test-id='debt-pane'
        >
            <Typography.Text
                view={isTablet ? 'primary-large' : 'primary-medium'}
                color='secondary'
                dataTestId='available-amount-header'
            >
                Вам доступно
            </Typography.Text>
            <div className={cn('available-amount-container')}>
                <div className={cn('available-amount')} data-test-id='available-amount'>
                    {creditLine?.availableAmount && (
                        <Typography.Title
                            className={cn('amount')}
                            view='medium'
                            font='system'
                            tag='div'
                        >
                            <CorporateAmount
                                transparentMinor={false}
                                view='withZeroMinorPart'
                                amount={creditLine?.availableAmount}
                            />
                        </Typography.Title>
                    )}
                    {creditLine?.limit && (
                        <Typography.Text
                            view={isTablet ? 'primary-large' : 'primary-medium'}
                            dataTestId='limit'
                        >
                            {isTablet && NBSP}из{NBSP}
                            <AmountPure value={creditLine?.limit} transparentMinor={false} />
                        </Typography.Text>
                    )}
                </div>
                {!shouldHideButtons && (
                    <div className={cn('buttons')}>
                        {isGetTrancheButtonVisible && (
                            <GetTrancheButton
                                docNumber={creditLine?.docNumber ?? ''}
                                dealId={creditLine?.dealId ?? ''}
                                isMmb={isTrancheAllowedMMB}
                                size={isTablet ? 's' : 'xs'}
                                onClick={handleOnGetTrancheButtonClick}
                                nowrap={true}
                                dataTestId={`get-tranche__${creditLine?.docNumber ?? ''}`}
                                className={cn('button')}
                            />
                        )}

                        <PrintOperationsButtonAdaptive
                            isDesktop={isDesktop}
                            isTablet={isTablet}
                            isMobile={isMobile}
                            isStatementAllowed={!!creditLine?.isStatementAllowed}
                            isPrimaryButtonVisible={isGetTrancheButtonVisible}
                            togglePrintOperationsSidebar={togglePrintOperationsSidebar}
                        />
                    </div>
                )}
            </div>
            <Divider />
            <div className={cn('info-container')}>
                <div className={cn('info-container-item')} data-test-id='total-debt'>
                    <Typography.Text
                        className={cn('text')}
                        tag='div'
                        view='component'
                        color='secondary'
                    >
                        Осталось выплатить
                    </Typography.Text>
                    <Typography.Title tag='div' view='xsmall' font='system'>
                        <AmountPure value={totalDebt} transparentMinor={false} />
                    </Typography.Title>
                </div>
                <div className={cn('info-container-item')} data-test-id='total-loan-sum'>
                    <Typography.Text
                        className={cn('text')}
                        tag='div'
                        view='component'
                        color='secondary'
                    >
                        Основной долг
                    </Typography.Text>
                    <Typography.Title tag='div' view='xsmall' font='system'>
                        <AmountPure value={totalLoanSum} transparentMinor={false} />
                    </Typography.Title>
                </div>
                <div className={cn('info-container-item')} data-test-id='total-interest'>
                    <Typography.Text
                        className={cn('text')}
                        tag='div'
                        view='component'
                        color='secondary'
                    >
                        Проценты
                    </Typography.Text>
                    <Typography.Title tag='div' view='xsmall' font='system'>
                        <AmountPure value={totalInterestOverdue} transparentMinor={false} />
                    </Typography.Title>
                </div>
                {(totalFine?.amount ?? 0) > 0 && (
                    <div className={cn('info-container-item')} data-test-id='total-fine'>
                        <Typography.Text
                            className={cn('text')}
                            tag='div'
                            view='component'
                            color='secondary'
                        >
                            Неустойка
                        </Typography.Text>
                        <Typography.Title tag='div' view='xsmall' font='system'>
                            <AmountPure value={totalFine} transparentMinor={false} />
                        </Typography.Title>
                    </div>
                )}
            </div>
            <div className={cn('info-container')}>
                <div
                    className={cn('info-container-item')}
                    id='tranche_term'
                    data-test-id='tranche_term'
                >
                    <Typography.Text
                        className={cn('text')}
                        tag='div'
                        view='component'
                        color='secondary'
                    >
                        Действие договора
                    </Typography.Text>
                    <Typography.Title view='xsmall' font='system' tag='div'>
                        {dateToCustomFormat(currentTime, fromDate, DATE_FORMAT)} &ndash;{' '}
                        {dateToCustomFormat(currentTime, toDate, DATE_FORMAT)}
                    </Typography.Title>
                </div>
                <div
                    className={cn('info-container-item')}
                    id='tranche_date'
                    data-test-id='tranche_date'
                >
                    <Typography.Text
                        className={cn('text')}
                        tag='div'
                        view='component'
                        color='secondary'
                    >
                        Выдача транша доступна
                    </Typography.Text>
                    <Typography.Title view='xsmall' font='system' tag='div'>
                        До {dateToCustomFormat(currentTime, trancheEnd, DATE_FORMAT)}
                    </Typography.Title>
                </div>
            </div>
        </Container>
    );
};
