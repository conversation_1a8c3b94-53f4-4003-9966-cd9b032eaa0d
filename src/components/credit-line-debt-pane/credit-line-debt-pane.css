@import '@alfalab/core-components/vars';

.credit-line-debt-pane {
    width: 100%;
    padding: var(--gap-32);

    &__available-amount-container {
        display: flex;
        align-items: flex-end;
        margin-bottom: var(--gap-24);
        justify-content: space-between;

        @media (max-width: 1135px) {
            flex-direction: column;
            align-items: baseline;
            gap: var(--gap-16);
        }
    }

    &__available-amount {
        display: flex;
        align-items: flex-end;
        gap: var(--gap-4);

        @media (--small-only) {
            flex-direction: column;
            align-items: flex-start;
        }
    }

    &__buttons {
        display: flex;
        align-items: center;
        gap: var(--gap-16);

        @media (max-width: 1025px) {
            margin-top: var(--gap-16);
            width: 100%;
        }

        @media (--small-only) {
            width: 100%;
            margin-top: var(--gap-16);
            flex-direction: row;
        }
    }

    &__button {
        @media (--small-only) {
            width: 100%;
        }
    }

    &__amount {
        margin-right: var(--gap-4);
    }

    &__info-container {
        display: grid;
        margin-top: var(--gap-32);

        @media (min-width: 700px) {
            grid-template-columns: 232px 232px;
        }

        @media (min-width: 1250px) {
            grid-template-columns: 1fr 1fr 1fr 1fr;
        }

        @media (max-width: 699px) {
            gap: var(--gap-24);
        }

        @media (--small-only) {
            margin-top: var(--gap-16);
        }
    }

    &__info-container-item {
        width: 232px;

        @media (min-width: 1250px) {
            &:not(:first-child) {
                border-left: 1px solid var(--color-dark-indigo-15-flat);
                padding-left: var(--gap-16);
            }
        }

        @media (min-width: 700px) {
            &:nth-child(even) {
                border-left: 1px solid var(--color-dark-indigo-15-flat);
                padding-left: var(--gap-16);
            }
        }

        @media (max-width: 1249px) and (min-width: 700px) {
            &:first-child,
            &:nth-child(2) {
                margin-bottom: var(--gap-20);
            }

            &#tranche_term,
            &#tranche_date {
                margin: var(--gap-0);
            }
        }
    }

    &__text {
        margin-bottom: calc(var(--gap-4) + var(--gap-2));
    }
}
