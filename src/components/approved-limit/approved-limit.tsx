import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useMatchMedia } from '@alfalab/core-components/mq';

import { BUTTON_MARKER_PARAM_KEY, type ButtonMarkers } from '#/src/constants/button-markers';
import { initExternalRedirect } from '#/src/ducks/app/actions';
import {
    actualLimitsLimitIdSelector,
    maxActualLimitsAmountSelector,
} from '#/src/ducks/credit-products/selectors/client-limit.selectors';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { externalRedirectCreditSBSelector } from '#/src/ducks/settings/selectors';

import { ApprovedLimitBottomSheet } from './approved-limit-bottom-sheet/approved-limit-bottom-sheet';
import { ApprovedLimitModal } from './approved-limit-modal/approved-limit-modal';

type ApprovedLimitProps = {
    isOpen: boolean;
    onClose: () => void;
    activeButtonMarker?: ButtonMarkers;
};

export const ApprovedLimit: React.FC<ApprovedLimitProps> = ({
    isOpen,
    onClose,
    activeButtonMarker,
}) => {
    const [isMobile] = useMatchMedia('--mobile');
    const dispatch = useDispatch();
    const externalRedirectCreditSB = useSelector(externalRedirectCreditSBSelector);
    const maxActualLimitAmount = useSelector(maxActualLimitsAmountSelector);
    const limitId = useSelector(actualLimitsLimitIdSelector);
    const organizationId = useSelector(currentHeaderOrganizationEqIdSelector);

    const contentText = `Вам одобрен кредитный лимит ${maxActualLimitAmount}₽`;

    const redirectToCreditRequest = () => {
        dispatch(
            initExternalRedirect({
                link: externalRedirectCreditSB,
                addContextRoot: false,
                organizationId,
                parameters: {
                    [BUTTON_MARKER_PARAM_KEY]: activeButtonMarker || '',
                },
            }),
        );
    };

    const redirectToDeal = () => {
        dispatch(
            initExternalRedirect({
                link: `${externalRedirectCreditSB}/deal`,
                addContextRoot: false,
                organizationId,
                parameters: {
                    [BUTTON_MARKER_PARAM_KEY]: activeButtonMarker || '',
                    lmLimitId: limitId || '',
                },
            }),
        );
    };

    if (isMobile) {
        return (
            <ApprovedLimitBottomSheet
                isOpen={isOpen}
                onClose={onClose}
                isMobile={isMobile}
                contentText={contentText}
                redirectToCreditRequest={redirectToCreditRequest}
                redirectToDeal={redirectToDeal}
            />
        );
    }

    return (
        <ApprovedLimitModal
            isOpen={isOpen}
            onClose={onClose}
            isMobile={isMobile}
            contentText={contentText}
            redirectToCreditRequest={redirectToCreditRequest}
            redirectToDeal={redirectToDeal}
        />
    );
};
