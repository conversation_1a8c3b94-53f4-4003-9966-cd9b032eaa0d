import React from 'react';

import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';

type ApprovedLimitButtonsProps = {
    redirectToDeal: () => void;
    redirectToCreditRequest: () => void;
    isMobile: boolean;
};

export const ApprovedLimitButtons: React.FC<ApprovedLimitButtonsProps> = ({
    redirectToDeal,
    redirectToCreditRequest,
    isMobile,
}) => (
    <React.Fragment>
        <Button
            block={true}
            size={48}
            view='accent'
            onClick={redirectToDeal}
            dataTestId='credit-requests__select-product'
        >
            Выбрать продукт
        </Button>
        {isMobile && <Gap size='m' />}
        <Button
            block={true}
            size={48}
            view='secondary'
            onClick={redirectToCreditRequest}
            dataTestId='credit-requests__recalculate-limit'
        >
            Пересчитать лимит
        </Button>
    </React.Fragment>
);
