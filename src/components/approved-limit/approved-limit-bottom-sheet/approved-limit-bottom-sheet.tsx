import React from 'react';
import { createCn } from 'bem-react-classname';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Gap } from '@alfalab/core-components/gap';
import { Typography } from '@alfalab/core-components/typography';

import { ApprovedLimitButtons } from '../approved-limit-buttons/approved-limit-buttons';
import { ApprovedLimitIcon } from '../approved-limit-icon/approved-limit-icon';

import './approved-limit-bottom-sheet.css';

const cn = createCn('approved-limit-bottom-sheet');

type ApprovedLimitModalProps = {
    isOpen: boolean;
    onClose: () => void;
    contentText: string;
    redirectToCreditRequest: () => void;
    redirectToDeal: () => void;
    isMobile: boolean;
};

export const ApprovedLimitBottomSheet: React.FC<ApprovedLimitModalProps> = ({
    isOpen,
    onClose,
    contentText,
    redirectToCreditRequest,
    redirectToDeal,
    isMobile,
}) => (
    <BottomSheet open={isOpen} hasCloser={true} onClose={onClose} className={cn()}>
        <div className={cn('wrapper')}>
            <ApprovedLimitIcon iconContainerSize={48} />
            <Gap size='xl' />
            <Typography.Title className={cn('title')} tag='div' view='xsmall' font='system'>
                {contentText}
            </Typography.Title>
            <Gap size='2xl' />
            <ApprovedLimitButtons
                isMobile={isMobile}
                redirectToDeal={redirectToDeal}
                redirectToCreditRequest={redirectToCreditRequest}
            />
        </div>
    </BottomSheet>
);
