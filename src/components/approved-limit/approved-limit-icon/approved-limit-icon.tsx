import React from 'react';
import { createCn } from 'bem-react-classname';

import { type SuperEllipseProps } from '@alfalab/core-components/icon-view/components/super-ellipse/component';
import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import ExclamationCircleMIcon from '@alfalab/icons-glyph/ExclamationCircleMIcon';

import './approved-limit-icon.css';

type Props = {
    iconContainerSize: SuperEllipseProps['size'];
};

const cn = createCn('approved-limit-icon');

export const ApprovedLimitIcon: React.FC<Props> = ({ iconContainerSize }) => (
    <SuperEllipse size={iconContainerSize} shapeClassName={cn('shape')}>
        <ExclamationCircleMIcon className={cn('icon')} width={26} height={26} />
    </SuperEllipse>
);
