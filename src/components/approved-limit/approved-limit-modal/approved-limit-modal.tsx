import React from 'react';
import { createCn } from 'bem-react-classname';

import { Gap } from '@alfalab/core-components/gap';
import { Modal } from '@alfalab/core-components/modal';
import { Typography } from '@alfalab/core-components/typography';

import { ApprovedLimitButtons } from '#/src/components/approved-limit/approved-limit-buttons/approved-limit-buttons';
import { ApprovedLimitIcon } from '#/src/components/approved-limit/approved-limit-icon/approved-limit-icon';

import './approved-limit-modal.css';

const cn = createCn('approved-limit-modal');

type ApprovedLimitModalProps = {
    isOpen: boolean;
    onClose: () => void;
    contentText: string;
    redirectToCreditRequest: () => void;
    redirectToDeal: () => void;
    isMobile: boolean;
};

export const ApprovedLimitModal: React.FC<ApprovedLimitModalProps> = ({
    isOpen,
    onClose,
    contentText,
    redirectToDeal,
    redirectToCreditRequest,
    isMobile,
}) => (
    <Modal open={isOpen} hasCloser={true} onClose={onClose} size='s' className={cn()}>
        <Modal.Header />
        <Modal.Content className={cn('content')}>
            <ApprovedLimitIcon iconContainerSize={64} />
            <Gap size='xl' />
            <Typography.Title tag='div' view='small' font='system' className={cn('title')}>
                {contentText}
            </Typography.Title>
            <Gap size='m' />
        </Modal.Content>
        <Modal.Footer>
            <ApprovedLimitButtons
                isMobile={isMobile}
                redirectToDeal={redirectToDeal}
                redirectToCreditRequest={redirectToCreditRequest}
            />
        </Modal.Footer>
    </Modal>
);
