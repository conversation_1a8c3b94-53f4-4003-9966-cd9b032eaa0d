import * as React from 'react';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { Divider } from '@alfalab/core-components/divider';
import { useMatchMedia } from '@alfalab/core-components/mq';

import { BusinessInstallmentSchedulePaymentDesktopRow } from './business-installment-schedule-payment-desktop-row';
import { BusinessInstallmentSchedulePaymentMobileRow } from './business-installment-schedule-payment-mobile-row';

import './business-installment-schedule.css';

export type TCreditSchedulePaymentProps = {
    payments: TLoanPayment[];
    currentTime: Date;
};

export const BusinessInstallmentSchedulePayment: React.FC<TCreditSchedulePaymentProps> = ({
    payments,
    currentTime,
}) => {
    const [isTablet] = useMatchMedia('--tablet-m');

    if (!payments.length) return null;

    return (
        <React.Fragment>
            {payments.map((payment, index) =>
                isTablet ? (
                    <BusinessInstallmentSchedulePaymentDesktopRow
                        key={`credit-schedule-payment__${index}`}
                        payment={payment}
                        index={index}
                        currentTime={currentTime}
                    />
                ) : (
                    <React.Fragment key={`credit-schedule-payment__${index}`}>
                        <BusinessInstallmentSchedulePaymentMobileRow
                            payment={payment}
                            currentTime={currentTime}
                        />
                        {index !== payments.length - 1 && <Divider />}
                    </React.Fragment>
                ),
            )}
        </React.Fragment>
    );
};
