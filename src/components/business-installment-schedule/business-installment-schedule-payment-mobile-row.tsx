import * as React from 'react';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { Typography } from '@alfalab/core-components/typography';
import CorporateAmount from 'arui-private/corporate-amount';

import { DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';
import PaneGroup from '../ui/pane-group';
import PaneItem from '../ui/pane-item';

import { cn } from './business-installment-schedule';

import './business-installment-schedule.css';

type TProps = {
    payment: TLoanPayment;
    currentTime: Date;
};

export const BusinessInstallmentSchedulePaymentMobileRow: React.FC<TProps> = ({
    payment: { paymentDate, paymentAmount, paymentLoan, loanBalance },
    currentTime,
}) => (
    <div>
        <div className={cn('pane-group-header')}>
            {!!paymentDate && (
                <Typography.Text view='primary-medium' color='primary'>
                    {dateToCustomFormat(currentTime, paymentDate, DATE_FORMAT)}
                </Typography.Text>
            )}
            {!!paymentAmount && <CorporateAmount amount={paymentAmount} />}
        </div>
        <PaneGroup withoutBorder={true} colWidth={12} className={cn('pane-group')}>
            {!!paymentLoan && (
                <PaneItem
                    titleColor='secondary'
                    title='Основной долг'
                    text={<CorporateAmount amount={paymentLoan} />}
                />
            )}
            {!!loanBalance && (
                <PaneItem
                    titleColor='secondary'
                    title='Остаток долга'
                    text={<CorporateAmount amount={loanBalance} />}
                />
            )}
        </PaneGroup>
    </div>
);
