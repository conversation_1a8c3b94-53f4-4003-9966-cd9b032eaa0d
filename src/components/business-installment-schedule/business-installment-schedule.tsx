import React, { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { ReportFormat } from 'thrift-services/entities';
import { type TLoanPayment } from 'thrift-services/services/credit_products';
import { type UnixEpoch } from 'thrift-services/utils';

import SkeletonLabel from 'arui-private/skeleton-label';

import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import { CREDIT_IN_USE_METRICS } from '../../metrics';
import CreditScheduleRow from '../credit-schedule-row';
import PaymentSchedule from '../payment-schedule';
import NotifyPlate from '../ui/notify-plate';
import ScheduleCommonHeading from '../ui/schedule-common-heading';

import { BusinessInstallmentSchedulePayment } from './business-installment-schedule-payment';

import './business-installment-schedule.css';

export const cn = createCn('business-installment-schedule');

type TProps = {
    payedPayments: TLoanPayment[];
    futurePayments: TLoanPayment[];
    isFuturePaymentsBlocked?: boolean;
    docNumber: string;
    todayDate?: UnixEpoch | null;
    onDownloadButtonClick: (fileType?: ReportFormat) => void;
    onTogglePopup?: (popupName?: string) => void;
    isFetching: boolean;
};

const tableHeaders = ['Дата платежа', 'Сумма платежа', 'Основной долг', 'Остаток долга'];

const BusinessInstallmentSchedule: React.FC<TProps> = ({
    isFetching,
    futurePayments,
    payedPayments,
    todayDate,
    isFuturePaymentsBlocked,
    docNumber,
    onTogglePopup,
    onDownloadButtonClick,
}) => {
    const trackAlfaMetrics = useTrackAlfaMetrics();

    const currentTime = useSelector(currentTimeSelector);

    const handleDownloadScheduleButtonClick = useCallback(
        (fileType?: ReportFormat) => {
            if (fileType) {
                trackAlfaMetrics(CREDIT_IN_USE_METRICS.downloadScheduleButtonClick, {
                    agreementNumber: docNumber,
                    fileType,
                });

                onDownloadButtonClick(fileType);
            }
        },
        [docNumber, onDownloadButtonClick, trackAlfaMetrics],
    );

    const renderSkeleton = () =>
        Array.from(Array(10).keys()).map((key) => (
            <CreditScheduleRow
                key={key}
                first={
                    <div className={cn('skeleton-row')}>
                        <SkeletonLabel size='xl' />
                    </div>
                }
                second={
                    <div className={cn('skeleton-row')}>
                        <SkeletonLabel size='xl' />
                    </div>
                }
                third={
                    <div className={cn('skeleton-row')}>
                        <SkeletonLabel size='xl' />
                    </div>
                }
                fourth={
                    <div className={cn('skeleton-row')}>
                        <SkeletonLabel size='xl' />
                    </div>
                }
                fifth={
                    <div className={cn('skeleton-row')}>
                        <SkeletonLabel size='xl' />
                    </div>
                }
            />
        ));

    if (isFuturePaymentsBlocked) {
        return (
            <React.Fragment>
                <NotifyPlate
                    title='Ведутся технические работы'
                    text='Обновленные графики платежей будут доступны до окончания льготного периода.'
                    className={cn('notify-plate')}
                />
                <PaymentSchedule
                    todayDate={todayDate}
                    payedPayments={
                        <BusinessInstallmentSchedulePayment
                            payments={payedPayments}
                            currentTime={currentTime}
                        />
                    }
                    skeleton={renderSkeleton()}
                    tableHeaders={tableHeaders}
                    isFetching={isFetching}
                />
            </React.Fragment>
        );
    }

    return (
        <PaymentSchedule
            heading={
                <ScheduleCommonHeading
                    paymentsCount={payedPayments.length + futurePayments.length}
                    onTogglePopup={onTogglePopup}
                    isCanBeDownloaded={true}
                    downloadOptions={[{ key: ReportFormat.PDF }]}
                    onDownloadButtonClick={handleDownloadScheduleButtonClick}
                />
            }
            todayDate={todayDate}
            futurePayments={
                <BusinessInstallmentSchedulePayment
                    payments={futurePayments}
                    currentTime={currentTime}
                />
            }
            payedPayments={
                <BusinessInstallmentSchedulePayment
                    payments={payedPayments}
                    currentTime={currentTime}
                />
            }
            skeleton={renderSkeleton()}
            tableHeaders={tableHeaders}
            isFetching={isFetching}
        />
    );
};

export default BusinessInstallmentSchedule;
