@import '@alfalab/core-components/vars';

.business-installment-schedule {
    &__pane-group {
        padding: 0;

        &-header {
            display: flex;
            justify-content: space-between;
            margin: var(--gap-20) 0 var(--gap-12) 0;
            align-items: center;
        }
    }

    &__title {
        background-color: var(--color-dark-indigo-05);
    }

    &__future-payments {
        padding-bottom: var(--gap-12);
        @mixin promo-system_small;
    }

    &__dashed-line {
        border-bottom: 1px dashed color(var(--color-dark-indigo-07) a(0.3));
    }

    &__payed-payments {
        padding-top: var(--gap-24);
    }

    &__payed-payments-heading {
        padding-bottom: var(--gap-12);

        &_mute {
            opacity: var(--opacity-minor);
        }
    }

    &__skeleton-wrapper {
        display: flex;
        justify-content: space-between;
        height: 200px;
    }

    &__skeleton-row {
        width: 100%;
    }

    &__notify-plate {
        margin-bottom: var(--gap-24);
    }
}
