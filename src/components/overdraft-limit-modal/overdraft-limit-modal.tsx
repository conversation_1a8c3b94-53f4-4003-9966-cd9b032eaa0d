import React, { useState } from 'react';
import { createCn } from 'bem-react-classname';
import { CURRENCY } from 'corporate-services/lib/currency';
import { type Amount as AmountType } from 'thrift-services/entities';

import { Amount } from '@alfalab/core-components/amount';
import { AmountInput, type AmountInputProps } from '@alfalab/core-components/amount-input';
import { Button } from '@alfalab/core-components/button';
import { Modal } from '@alfalab/core-components/modal';
import { Typography } from '@alfalab/core-components/typography';
import { NBSP } from 'arui-private/lib/formatters';

import { ELimitStatus } from '#/src/types/overdraft';
import { convertAmountToString } from '#/src/utils/number-helpers';

import './overdraft-limit-modal.css';

const cn = createCn('overdraft-limit-modal');

enum InputError {
    'RANGE' = 'range',
    'EQUAL' = 'equal',
    'UNDEFINED' = 'undefined',
}

type Props = {
    open: boolean;
    handleClose: () => void;
    limit?: AmountType;
    maxLimit?: AmountType;
    minLimit?: AmountType;
    handleOverdraftLimitSet: (limit: AmountType) => void;
    editingStatus: ELimitStatus;
    hasClientLimit: boolean;
};

function validateInput(
    inputValue?: number,
    maxLimit?: AmountType,
    minLimit?: AmountType,
    currentLimit?: AmountType,
): InputError | undefined {
    if (inputValue === undefined) {
        return InputError.UNDEFINED;
    }
    if (inputValue < (minLimit?.amount ?? 0) || inputValue > (maxLimit?.amount ?? 0)) {
        return InputError.RANGE;
    }
    if (currentLimit?.amount && inputValue === currentLimit.amount) {
        return InputError.EQUAL;
    }

    return undefined;
}

const OverdraftLimitModal: React.FC<Props> = ({
    open,
    handleClose,
    limit,
    maxLimit,
    minLimit,
    handleOverdraftLimitSet,
    editingStatus,
    hasClientLimit,
}) => {
    const [inputLimit, setInputLimit] = useState<number | undefined>(limit?.amount);
    const [error, setError] = useState<InputError | undefined>();

    const isSetLimitFetching = editingStatus === ELimitStatus.IN_PROGRESS;

    const handleSave = () => {
        const validationError = validateInput(inputLimit, maxLimit, minLimit, limit);

        setError(validationError);

        if (validationError) {
            return;
        }

        const newLimit: AmountType = {
            amount: inputLimit ?? 0,
            currency: CURRENCY.RUR,
        };

        handleOverdraftLimitSet(newLimit);
    };

    const onInputChange: AmountInputProps['onChange'] = (_, payload) => {
        setInputLimit(payload.value ?? undefined);
    };

    const isPermissionsLow = !hasClientLimit && limit === null;

    return (
        <Modal open={open} onClose={handleClose} className={cn()}>
            <Modal.Header hasCloser={true}>
                <Typography.Title tag='div' view='small' font='system'>
                    Персональный лимит
                </Typography.Title>
            </Modal.Header>
            <Modal.Content>
                {isPermissionsLow ? (
                    <div className={cn('limit-plug')}>
                        Для установки ограничения лимита обратитесь к руководителю организации.
                    </div>
                ) : (
                    <AmountInput
                        onChange={onInputChange}
                        bold={false}
                        value={inputLimit}
                        integersOnly={true}
                        className={cn('input')}
                        placeholder='Не установлен'
                    />
                )}
                {error && (
                    <Typography.Text tag='div' view='secondary-medium' color='negative'>
                        {error === InputError.RANGE && (
                            <React.Fragment>
                                Введите лимит{NBSP}от{NBSP}
                                <Amount
                                    value={minLimit?.amount ?? 0}
                                    currency='RUR'
                                    minority={100}
                                />
                                {NBSP}до{NBSP}
                                <Amount
                                    value={maxLimit?.amount ?? 0}
                                    currency='RUR'
                                    minority={100}
                                />
                            </React.Fragment>
                        )}
                        {error === InputError.EQUAL && 'Введите лимит отличный от текущего'}
                        {error === InputError.UNDEFINED && 'Вы не указали лимит'}
                    </Typography.Text>
                )}
                <Typography.Text
                    tag='div'
                    view='secondary-medium'
                    className={cn('text')}
                    color='secondary'
                >
                    Персональный лимит — это персональное ограничение, которое вы оставляете для
                    {NBSP}себя. Доступная сумма овердрафта может быть ниже, но{NBSP}никогда не{NBSP}
                    будет выше.
                    <br />
                    <br />
                    Вы можете установить ограничение в{NBSP}размере
                    {minLimit !== undefined &&
                        ` от${NBSP}${convertAmountToString(minLimit, true, true)}`}
                    {maxLimit !== undefined &&
                        ` до${NBSP}${convertAmountToString(maxLimit, true, true)}`}
                </Typography.Text>
            </Modal.Content>
            <Modal.Footer>
                <Button
                    view='primary'
                    size='s'
                    onClick={handleSave}
                    loading={isSetLimitFetching}
                    disabled={isSetLimitFetching || isPermissionsLow}
                >
                    Сохранить
                </Button>
                <Button
                    view='tertiary'
                    size='s'
                    onClick={handleClose}
                    disabled={isSetLimitFetching}
                >
                    Отменить
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default OverdraftLimitModal;
