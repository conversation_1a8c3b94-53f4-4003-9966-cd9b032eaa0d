.guaranty-line-conditions {
    padding: var(--gap-32);

    &__info-container {
        display: flex;
    }

    &__info-container-item {
        width: 232px;

        &:first-child {
            padding-right: var(--gap-16);
        }

        &:not(:first-child) {
            border-left: 1px solid var(--color-dark-indigo-15-flat);
            padding-left: var(--gap-16);
        }
    }

    @media (--tablet), (--mobile) {
        &__info-container {
            flex-direction: column;
            gap: var(--gap-xl);
        }

        &__info-container-item {
            &:not(:first-child) {
                border-left: none;
                padding-left: 0;
            }
        }
    }
}
