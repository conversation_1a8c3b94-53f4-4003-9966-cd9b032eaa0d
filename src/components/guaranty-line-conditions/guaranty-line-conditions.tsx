import React from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type UnixEpoch } from 'thrift-services/utils';

import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate } from 'arui-private/background-plate';

import { DATE_FORMAT } from '#/src/constants/date';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';

import { dateToCustomFormat } from '../../utils/date';

import './guaranty-line-conditions.css';

type TOwnProps = {
    docNumber?: string;
    fromDate?: UnixEpoch;
    toDate?: UnixEpoch;
};

const cn = createCn('guaranty-line-conditions');

const GuarantyLineConditions = ({ fromDate, toDate, docNumber }: TOwnProps) => {
    const currentTime = useSelector(currentTimeSelector);

    return (
        <BackgroundPlate className={cn()}>
            <div className={cn('info-container')}>
                <div className={cn('info-container-item')}>
                    <Typography.Text
                        className={cn('text')}
                        tag='div'
                        view='component'
                        color='secondary'
                    >
                        Договор
                    </Typography.Text>
                    <Typography.Text view='primary-large' weight='medium'>
                        № {docNumber}
                    </Typography.Text>
                </div>
                <div className={cn('info-container-item')}>
                    <Typography.Text tag='div' view='component' color='secondary'>
                        Действие договора
                    </Typography.Text>
                    <Typography.Text view='primary-large' weight='medium'>
                        {dateToCustomFormat(currentTime, fromDate, DATE_FORMAT)}
                        &nbsp;-&nbsp;
                        {dateToCustomFormat(currentTime, toDate, DATE_FORMAT)}
                    </Typography.Text>
                </div>
            </div>
        </BackgroundPlate>
    );
};

export default GuarantyLineConditions;
