@import '@alfalab/core-components/vars';

.credit-offer-plate {
    position: relative;
    padding: var(--gap-16) var(--gap-24) var(--gap-24);
    background-color: var(--color-white);
    border: 1px solid var(--color-light-border-primary);
    border-radius: var(--border-radius-8);
    transition:
        border 350ms,
        box-shadow 350ms;
    cursor: pointer;

    @media (--desktop-l) {
        padding: var(--gap-32);
    }

    &:hover {
        border: 1px solid var(--color-dark-indigo-30-flat);
    }

    &_view_positive {
        background-color: var(--color-light-bg-positive-muted);
        border: 1px solid var(--color-light-graphic-positive);

        &:hover {
            border: 1px solid var(--color-light-graphic-positive-shade-30);
        }
    }
}
