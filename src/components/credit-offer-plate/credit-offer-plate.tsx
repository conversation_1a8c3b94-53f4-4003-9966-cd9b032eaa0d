import React from 'react';

import { creditOfferPlatePlateCn } from './credit-offer-plate-plate-cn';

type Props = {
    children?: React.ReactNode;
    view?: 'default' | 'positive';
    onClick?: (e: React.MouseEvent) => void;
    onKeyDown?: (event: React.KeyboardEvent<Element>) => void | undefined;
};

export const CreditOfferPlate: React.FC<Props> = ({
    children,
    view = 'default',
    onKeyDown,
    onClick,
}) => (
    <div
        className={creditOfferPlatePlateCn({ view })}
        role='button'
        tabIndex={0}
        onKeyDown={onKeyDown}
        onClick={onClick}
    >
        {children}
    </div>
);
