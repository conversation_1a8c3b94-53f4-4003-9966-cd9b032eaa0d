import React, { useCallback, useMemo, useState } from 'react';
import { createCn } from 'bem-react-classname';

import { Notification } from '@alfalab/core-components/notification';
import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate } from 'arui-private/background-plate';

import SignedCreditDocumentsItem from '#/src/components/signed-credit-documents-item';
import SignedCreditDocumentsSkeleton from '#/src/components/skeletons/signed-credit-documents-skeleton';
import { type SignedDocument } from '#/src/types/signed-document';
import { downloadSignedBinaryFileByExtension } from '#/src/utils/binary-download';

import './signed-credit-documents.css';

type Props = {
    signedDocuments: SignedDocument[];
    isFetching: boolean;
    showTitle?: boolean;
    limitedWidth?: boolean;
};

const cn = createCn('signed-credit-documents');

const SignedCreditDocuments: React.FC<Props> = ({
    signedDocuments,
    isFetching,
    showTitle = true,
    limitedWidth = true,
}) => {
    const [isVisibleNotification, setNotificationVisibility] = useState(false);

    const handleOpenNotification = useCallback(() => {
        setNotificationVisibility(true);
    }, []);

    const handleCloseNotification = useCallback(() => {
        setNotificationVisibility(false);
    }, []);

    const handleDownloadButtonClick = (content: SignedDocument['body'], name: string) => {
        handleOpenNotification();
        downloadSignedBinaryFileByExtension(content, name, 'pdf');
    };

    const filteredDocuments = useMemo(
        () => signedDocuments.filter((document) => !!document?.body?.data),
        [signedDocuments],
    );

    if (isFetching) {
        return <SignedCreditDocumentsSkeleton />;
    }

    return (
        <BackgroundPlate className={cn({ limitedWidth })}>
            <Notification
                visible={isVisibleNotification}
                badge='positive'
                autoCloseDelay={8000}
                block={true}
                title='Идет загрузка файла'
                onClose={handleCloseNotification}
            >
                Не покидайте страницу, иначе загрузка прервётся
            </Notification>
            {showTitle && (
                <Typography.Title
                    className={cn('title')}
                    view='xsmall'
                    weight='bold'
                    tag='div'
                    font='system'
                >
                    Кредитная документация
                </Typography.Title>
            )}
            <div className={cn('documents')}>
                {filteredDocuments.length > 0 &&
                    filteredDocuments.map((doc) => (
                        <SignedCreditDocumentsItem
                            key={doc.name}
                            name={doc.name || ''}
                            content={doc?.body}
                            onDownloadButtonClick={handleDownloadButtonClick}
                        />
                    ))}
            </div>
        </BackgroundPlate>
    );
};

export default SignedCreditDocuments;
