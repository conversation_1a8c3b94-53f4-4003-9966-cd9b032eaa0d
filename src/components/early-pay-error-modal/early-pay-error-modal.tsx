import React from 'react';

import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { Modal } from '@alfalab/core-components/modal';
import { ExclamationCompactMIcon } from '@alfalab/icons-glyph/ExclamationCompactMIcon';
import { StatusScreen } from 'arui-private/status-screen';

type TProps = {
    isModalVisible: boolean;
    handleCloseModal: () => void;
    title: string;
    text: string;
};

export const EarlyPayErrorModal = ({ title, text, isModalVisible, handleCloseModal }: TProps) => (
    <Modal open={isModalVisible} hasCloser={true} onClose={handleCloseModal}>
        <StatusScreen
            subtitle={text}
            title={title}
            useBackgroundPlate={true}
            secondaryButtonProps={{ label: 'Понятно', props: { onClick: handleCloseModal } }}
            topAddons={
                <SuperEllipse size={64} backgroundColor='#FA9313'>
                    <ExclamationCompactMIcon color='#FFFFFF' />
                </SuperEllipse>
            }
        />
    </Modal>
);
