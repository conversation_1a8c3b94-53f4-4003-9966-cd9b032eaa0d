import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type Amount } from 'thrift-services/entities';

import { Notification } from '@alfalab/core-components/notification';
import { NBSP } from 'arui-private/lib/formatters';

import OverdraftLimitModal from '#/src/components/overdraft-limit-modal';
import OverdraftSettingPane from '#/src/components/overdraft-setting-pane';
import { type trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import { limitNotificationClose } from '#/src/ducks/credit-products/actions';
import { OVER_IN_USE_METRICS } from '#/src/metrics';
import { ELimitStatus } from '#/src/types/overdraft';
import { convertAmountToString } from '#/src/utils/number-helpers';

import './overdraft-limit-pane.css';

type Props = {
    limit?: Amount;
    pending: boolean;
    maxLimit?: Amount;
    minLimit?: Amount;
    trackAlfaMetrics: typeof trackAlfaMetrics;
    editingStatus: ELimitStatus;
    handleOverdraftLimitSet: (limit: Amount) => void;
    hasClientLimit: boolean;
};

const cn = createCn('overdraft-limit-pane');

export const OverdraftLimitPane: React.FC<Props> = ({
    pending,
    limit,
    maxLimit,
    minLimit,
    handleOverdraftLimitSet,
    editingStatus,
    hasClientLimit,
    trackAlfaMetrics,
}) => {
    const dispatch = useDispatch();
    const [modalOpen, setModalOpen] = useState(false);

    const handleLimitNotificationClose = useCallback(() => {
        dispatch(limitNotificationClose());
    }, []);

    const isSetLimitSuccess = useMemo(
        () => editingStatus === ELimitStatus.SET_SUCCESSFUL,
        [editingStatus],
    );
    const isSetLimitError = useMemo(() => editingStatus === ELimitStatus.ERROR, [editingStatus]);

    const handleClose = useCallback(() => {
        setModalOpen(false);
        trackAlfaMetrics(OVER_IN_USE_METRICS.limitCancel);
    }, []);

    const handleOpen = useCallback(() => {
        setModalOpen(true);
        trackAlfaMetrics(OVER_IN_USE_METRICS.limitEdit);
    }, []);

    useEffect(() => {
        if (isSetLimitSuccess) {
            setModalOpen(false);
        }
    }, [isSetLimitSuccess]);

    return (
        <React.Fragment>
            <OverdraftSettingPane
                title='Персональный лимит:'
                settingValue={limit ? convertAmountToString(limit, true, true) : 'Не установлен'}
                description={`Персональный лимит — это сумма, которая будет доступна вам из лимита овердрафта. Вы сможете уйти в минус только на эту сумму. Банк не${NBSP}увеличит размер персонального лимита, даже если обороты компании вырастут`}
                onEditClick={handleOpen}
                pending={pending}
            />
            <OverdraftLimitModal
                open={modalOpen}
                handleClose={handleClose}
                limit={limit}
                maxLimit={maxLimit}
                minLimit={minLimit}
                handleOverdraftLimitSet={handleOverdraftLimitSet}
                editingStatus={editingStatus}
                hasClientLimit={hasClientLimit}
            />
            {(isSetLimitSuccess || isSetLimitError) && (
                <Notification
                    visible={true}
                    badge={isSetLimitSuccess ? 'positive' : 'negative'}
                    title={
                        isSetLimitSuccess
                            ? 'Ограничение будет установлено в течение месяца'
                            : 'Возникла ошибка при установке лимита овердрафта. Попробуйте позже'
                    }
                    onCloseTimeout={handleLimitNotificationClose}
                    onClose={handleLimitNotificationClose}
                    contentClassName={cn('notification-content')}
                    titleClassName={cn('notification-title')}
                />
            )}
        </React.Fragment>
    );
};
