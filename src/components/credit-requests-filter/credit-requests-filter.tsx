import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname/create-cn';

import { useMatchMedia } from '@alfalab/core-components/mq/useMatchMedia';
import { BaseOption, type OptionShape } from '@alfalab/core-components/select/shared';

import { CustomOption } from '#/src/components/ui/custom-option-with-radio/custom-option-with-radio';
import { FilterComponent } from '#/src/components/ui/filter-component/filter-component';
import {
    PRODUCTS_OPTIONS,
    REQUESTS_STATUS_OPTIONS,
    Status,
} from '#/src/constants/credit-document-circulation';
import {
    applyRequestsFilter,
    getCreditRequestsListStart,
} from '#/src/ducks/credit-requests/actions';
import { creditRequestsFiltersSelector } from '#/src/ducks/credit-requests/selectors';
import { getCategoryStart } from '#/src/ducks/organization/actions';
import { useHoldingControls } from '#/src/utils/hooks/use-holding-controls';

import './credit-requests-filter.css';

const cn = createCn('credit-requests-filter');

export const CreditRequestsFilters = () => {
    const dispatch = useDispatch();
    const [isMobile] = useMatchMedia('--mobile');

    const { initialCompanyValue, isShowCompanyFilter, options } = useHoldingControls();

    const { status, products, company } = useSelector(creditRequestsFiltersSelector);

    const [selectedStatus, setSelectedStatus] = useState(status);
    const [selectedProducts, setSelectedProducts] = useState(products);

    const currentCompany = company || initialCompanyValue || [options[0]];

    const updateFilters = useCallback(
        (data = {}) => {
            dispatch(
                applyRequestsFilter({
                    status: selectedStatus,
                    products: selectedProducts,
                    company: currentCompany,
                    ...data,
                }),
            );
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [selectedStatus, selectedProducts, currentCompany],
    );

    const handleResetProduct = useCallback(() => {
        setSelectedProducts([]);
        updateFilters({ products: [] });
    }, [updateFilters]);

    const handleCompanyOnChange = useCallback(
        (value: OptionShape[]) => {
            dispatch(getCreditRequestsListStart(value[0].key));
            dispatch(getCategoryStart(value[0].key));
            updateFilters({ company: value });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [updateFilters],
    );

    const handleProductOnChange = useCallback(
        (multipleProduct) => {
            setSelectedProducts(multipleProduct);
            updateFilters({ products: multipleProduct });
        },
        [updateFilters],
    );

    const handleStatusOnChange = useCallback(
        (value: OptionShape[]) => {
            const newStatus = value[0]?.key as Status;

            setSelectedStatus(newStatus);
            updateFilters({ status: newStatus });
        },
        [updateFilters],
    );

    useEffect(() => {
        if (status === Status.Active && !products.length) {
            setSelectedStatus(status);
            setSelectedProducts(products);
        }
    }, [status, products]);

    return (
        <div className={cn()} data-test-id='request-filter-main-menu'>
            {isShowCompanyFilter && (
                <FilterComponent
                    label='Компания'
                    searchPlaceholder='Название'
                    selected={currentCompany}
                    onChange={handleCompanyOnChange}
                    options={options}
                    hasSearch={true}
                    multiple={false}
                    isBlock={isMobile}
                    dataTestId='requests-filter-company-select'
                />
            )}

            <FilterComponent
                label='Сортировка'
                multiple={false}
                optionsListWidth='content'
                options={REQUESTS_STATUS_OPTIONS}
                onChange={handleStatusOnChange}
                selected={selectedStatus}
                withoutLabelInFilterContent={true}
                Option={isMobile ? CustomOption : BaseOption}
                dataTestId='request-filter-status-select'
            />

            <FilterComponent
                label='Продукт'
                showClear={true}
                selected={selectedProducts}
                onChange={handleProductOnChange}
                options={PRODUCTS_OPTIONS}
                popperClassName={cn('product-popper')}
                onClear={handleResetProduct}
                dataTestId='request-filter-product-select'
            />
        </div>
    );
};
