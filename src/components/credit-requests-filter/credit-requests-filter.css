@import '@alfalab/core-components/vars';

.credit-requests-filter {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: var(--gap-32);

    @media (--mobile) {
        flex-wrap: nowrap;
        overflow-x: scroll;
        scrollbar-width: none;
        &::-webkit-scrollbar {
            display: none;
        }
    }

    &__product-popper {
        width: 280px;

        div[class*='select__footer'] {
            button[class*='button__primary'] {
                background-color: var(--color-dark-neutral-200);
            }
        }
    }
}
