@import '@alfalab/core-components/vars';

.overdraft-repayment-procedure-collapse {
    flex-basis: 100%;
    padding: 0 0 var(--gap-24) 0;

    @media (--mobile-m) {
        padding-left: var(--gap-8);
        padding-right: var(--gap-8);
    }

    @media (--desktop-m) {
        padding-left: var(--gap-12);
        padding-right: var(--gap-12);
    }

    &__title {
        @mixin headline-system_xsmall;
        display: flex;
        align-items: center;
        width: fit-content;
        cursor: pointer;
        user-select: none;
    }

    &__icon {
        margin-left: var(--gap-4);

        &_expanded {
            transform: rotate(180deg);
        }
    }

    &__collapse {
        padding-top: 0;

        &_expanded {
            padding-top: var(--gap-16);
        }
    }

    &__collapse-title {
        @mixin paragraph_primary_medium;
    }
}
