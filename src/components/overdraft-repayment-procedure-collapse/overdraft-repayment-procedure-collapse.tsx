import React, { useState } from 'react';
import { createCn } from 'bem-react-classname';

import { Collapse } from '@alfalab/core-components/collapse';
import { Typography } from '@alfalab/core-components/typography';
import { ChevronDownMIcon } from '@alfalab/icons-glyph/ChevronDownMIcon';

import './overdraft-repayment-procedure-collapse.css';

export const OverdraftRepaymentProcedureCollapse: React.FC = () => {
    const cn = createCn('overdraft-repayment-procedure-collapse');

    const [expanded, setExpanded] = useState(false);

    const handleExpandCollapse = () => {
        setExpanded((prev) => !prev);
    };

    return (
        <div className={cn()}>
            <Typography.Title
                tag='div'
                view='xsmall'
                weight='bold'
                className={cn('title')}
                onClick={handleExpandCollapse}
            >
                Порядок погашения овердрафта
                <ChevronDownMIcon
                    className={cn('icon', {
                        expanded,
                    })}
                />
            </Typography.Title>
            <Collapse
                expanded={expanded}
                className={cn('collapse', {
                    expanded,
                })}
            >
                <Typography.Title
                    tag='div'
                    view='xsmall'
                    weight='regular'
                    className={cn('collapse-title')}
                >
                    Деньги списываются автоматически в такой последовательности:
                </Typography.Title>
                1. Просроченная задолженность (проценты + основной долг)
                <br />
                2. Проценты за овердрафт (начисляются каждый день; нужно оплатить в течение 60 дней
                с даты получения транша)
                <br />
                3. Задолженность по очередному траншу (оплатить в течение 60 дней с даты получения
                транша)
                <br />
                4. Неустойка за просроченную задолженность (можно погасить, только если нет
                основного долга)
            </Collapse>
        </div>
    );
};
