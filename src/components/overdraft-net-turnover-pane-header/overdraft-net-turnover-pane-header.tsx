import React, { memo } from 'react';
import { createCn } from 'bem-react-classname';
import { type Amount } from 'thrift-services/entities';

import { List } from '@alfalab/core-components/list';
import { Tooltip } from '@alfalab/core-components/tooltip';
import { Typography } from '@alfalab/core-components/typography';
import { ChevronDownMIcon } from '@alfalab/icons-glyph/ChevronDownMIcon';
import { InformationCircleMIcon } from '@alfalab/icons-glyph/InformationCircleMIcon';
import { NBSP } from 'arui-private/lib/formatters';

import AmountPure from '#/src/components/ui/amount-pure';
import { OverdraftUnaccountedTurnover } from '#/src/constants/overdraft';

import './overdraft-net-turnover-pane-header.css';

const cn = createCn('overdraft-net-turnover-pane-header');

type TProps = {
    expanded: boolean;
    paddingBottomXl: boolean;
    toggleExpanded: () => void;
    maxAmount: Amount;
};

const TooltipContent = (
    <div>
        <Typography.Text view='primary-small'>
            Не{NBSP}входят в{NBSP}обороты:
        </Typography.Text>
        <List tag='ul' className={cn('list')}>
            {OverdraftUnaccountedTurnover.map((el, index) => (
                <Typography.Text view='primary-small' key={index}>
                    {el}
                </Typography.Text>
            ))}
        </List>
    </div>
);

export const OverdraftNetTurnoverPaneHeader: React.FC<TProps> = memo(
    ({ expanded, paddingBottomXl, toggleExpanded, maxAmount }) => (
        <div
            className={cn({
                paddingBottomXl,
            })}
            onClick={toggleExpanded}
        >
            <Typography.Title
                view='small'
                weight='bold'
                tag='div'
                font='system'
                className={cn('title')}
            >
                Требования к{NBSP}оборотам:{NBSP}
                <AmountPure value={maxAmount} view='default' transparentMinor={false} />
                <Tooltip
                    content={TooltipContent}
                    position='right'
                    fallbackPlacements={['bottom', 'left']}
                    actionButtonTitle='Понятно'
                    trigger='hover'
                    targetClassName={cn('tooltip-wrapper')}
                >
                    <InformationCircleMIcon className={cn('info-icon')} />
                </Tooltip>
            </Typography.Title>
            <Typography.Text
                view='primary-small'
                weight='medium'
                tag='div'
                className={cn('expand-status')}
            >
                {expanded ? 'Свернуть' : 'Развернуть'}
                <ChevronDownMIcon className={cn('arrow', { expanded })} />
            </Typography.Text>
        </div>
    ),
);
