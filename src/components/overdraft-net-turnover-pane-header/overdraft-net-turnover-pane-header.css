@import '@alfalab/core-components/vars';

.overdraft-net-turnover-pane-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: var(--gap-32);
    user-select: none;
    transition: padding-bottom 0.3s ease;
    gap: var(--gap-xs);

    &_paddingBottomXl {
        padding-bottom: var(--gap-24);
    }

    &__title {
        display: flex;
        align-items: center;
    }

    &__list {
        li:not(:first-of-type) {
            margin-top: 0;
        }
    }

    &__tooltip-wrapper {
        display: flex;
        align-items: center;
        margin-left: var(--gap-2);
    }

    &__info-icon {
        width: 20px;
        height: 20px;
        color: var(--color-light-graphic-secondary);
    }

    &__expand-status {
        display: flex;
        align-items: center;
    }

    &__arrow {
        margin-left: var(--gap-4);

        &_expanded {
            transform: rotate(180deg);
        }
    }
}
