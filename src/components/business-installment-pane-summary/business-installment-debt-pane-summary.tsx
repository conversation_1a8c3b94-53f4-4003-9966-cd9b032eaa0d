import React from 'react';
import { createCn } from 'bem-react-classname';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Typography } from '@alfalab/core-components/typography';

import AmountPure from '#/src/components/ui/amount-pure';

import './business-installment-debt-pane-summary.css';

const cn = createCn('business-installment-debt-pane-summary');

type OwnProps = {
    label: string | React.ReactNode;
    amount: TAmount;
    isCompressed?: boolean;
    isFineExists?: boolean;
};

export const BusinessInstallmentDebtPaneSummary: React.FC<OwnProps> = ({
    label,
    amount,
    isCompressed = false,
    isFineExists = false,
}) => (
    <div className={cn({ compressed: isCompressed })}>
        <div className={cn('debt-wrapper', { red: isFineExists })}>
            <Typography.Text color='secondary' className={cn('label')} view='primary-large'>
                {label}
            </Typography.Text>
            <div className={cn('amount')}>
                {!!amount && (
                    <Typography.Title view='medium' font='system' tag='h3' id='at-amount-to-pay'>
                        <AmountPure bold='full' value={amount} />
                    </Typography.Title>
                )}
            </div>
        </div>
    </div>
);
