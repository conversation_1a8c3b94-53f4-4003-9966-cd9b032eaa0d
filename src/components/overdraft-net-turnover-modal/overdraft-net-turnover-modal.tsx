import React from 'react';
import { createCn } from 'bem-react-classname';
import { type Amount } from 'thrift-services/entities';

import { Button } from '@alfalab/core-components/button';
import { List } from '@alfalab/core-components/list';
import { ModalMobile } from '@alfalab/core-components/modal/mobile';
import { Tooltip } from '@alfalab/core-components/tooltip';
import { Typography } from '@alfalab/core-components/typography';
import { InformationCircleMIcon } from '@alfalab/icons-glyph/InformationCircleMIcon';
import { NBSP } from 'arui-private/lib/formatters';

import { OverdraftNetTurnoverPaneContent } from '#/src/components/overdraft-net-turnover-pane-content';
import { type TTurnoverProps } from '#/src/components/overdraft-net-turnover-pane-content/overdraft-net-turnover-pane-content';
import AmountPure from '#/src/components/ui/amount-pure';
import { OverdraftUnaccountedTurnover } from '#/src/constants/overdraft';

import './overdraft-net-turnover-modal.css';

type TProps = {
    open: boolean;
    onClose: () => void;
    maintainAmount?: Amount;
} & TTurnoverProps;

const cn = createCn('overdraft-net-turnover-modal');

const TooltipContent = (
    <div>
        <Typography.Text view='primary-medium'>
            Не{NBSP}входят в{NBSP}обороты:
        </Typography.Text>
        <List tag='ul' className={cn('list')}>
            {OverdraftUnaccountedTurnover.map((el, index) => (
                <Typography.Text view='primary-medium' key={index}>
                    {el}
                </Typography.Text>
            ))}
        </List>
    </div>
);

export const OverdraftNetTurnoverModal: React.FC<TProps> = ({
    open,
    onClose,
    maintainAmount,
    ...turnoverProps
}) => (
    <ModalMobile open={open} onClose={onClose}>
        <ModalMobile.Header hasBackButton={true} onClose={onClose} onBack={onClose} />
        <ModalMobile.Content className={cn('content')}>
            <div className={cn('maintain-turnover')}>
                <Typography.TitleMobile tag='div' view='small' font='system'>
                    Требования к оборотам:
                    <br />
                    <AmountPure value={maintainAmount} view='default' transparentMinor={false} />
                </Typography.TitleMobile>
                <Tooltip
                    content={TooltipContent}
                    position='right'
                    fallbackPlacements={['bottom', 'left']}
                    actionButtonTitle='Понятно'
                    trigger='hover'
                >
                    <InformationCircleMIcon className={cn('info-icon')} />
                </Tooltip>
            </div>
            <OverdraftNetTurnoverPaneContent {...turnoverProps} />
        </ModalMobile.Content>
        <ModalMobile.Footer>
            <Button view='secondary' onClick={onClose} block={true}>
                Понятно
            </Button>
        </ModalMobile.Footer>
    </ModalMobile>
);
