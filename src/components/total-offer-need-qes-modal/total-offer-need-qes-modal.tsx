import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { Button } from '@alfalab/core-components/button';
import { Link } from '@alfalab/core-components/link';
import { Modal } from '@alfalab/core-components/modal';
import { Typography } from '@alfalab/core-components/typography';

import {
    type ECreditOffersCampaignCode,
    TotalOffersCampaignCode,
} from '#/src/constants/credit-offers';
import { goToCreditFormsApp } from '#/src/ducks/app/actions';
import {
    isOnlineSigningAvailableSelector,
    isTotalOfferSelector,
    preferredProductSelector,
    productCodeSelector,
    stageActiveCreditCaseSelector,
} from '#/src/ducks/attach-documents/selectors';
import {
    createCallbackRequest,
    setCallbackRequestCreatedModalVisible,
    setContinueActiveCaseModalVisible,
    setContinueSFAActiveCaseModalVisible,
    setTotalOfferNeedQESModalVisible,
} from '#/src/ducks/credit-processing/actions';
import {
    getClickedOfferSelector,
    isTotalOfferNeedQESModalVisibleSelector,
} from '#/src/ducks/credit-processing/selectors';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { getIsShowContinueCaseModal } from '#/src/utils/active-credit-case';

import './total-offer-need-qes-modal.css';

const cn = createCn('total-offer-need-qes-modal');

export const TotalOfferNeedQESModal: React.FC = () => {
    const dispatch = useDispatch();

    const isModalVisible = useSelector(isTotalOfferNeedQESModalVisibleSelector);
    const clickedOffer = useSelector(getClickedOfferSelector);
    const isActiveTotalOffer = useSelector(isTotalOfferSelector);
    const activeProductCode = useSelector(productCodeSelector);
    const preferredProduct = useSelector(preferredProductSelector);
    const stage = useSelector(stageActiveCreditCaseSelector);
    const isOnlineSigningAvailable = useSelector(isOnlineSigningAvailableSelector);
    const organizationId = useSelector(currentHeaderOrganizationEqIdSelector);

    const isTotalOffer = TotalOffersCampaignCode.includes(
        clickedOffer?.campaignCode as ECreditOffersCampaignCode,
    );

    const handleCloseModal = () => {
        dispatch(setTotalOfferNeedQESModalVisible(false));
    };

    const handleConfirmQESExistence = () => {
        if (isOnlineSigningAvailable) {
            handleCloseModal();
            dispatch(setContinueSFAActiveCaseModalVisible(true));

            return;
        }
        if (stage) {
            const isShowContinueCaseModal = getIsShowContinueCaseModal(
                activeProductCode,
                clickedOffer?.type,
                clickedOffer?.campaignCode,
                isActiveTotalOffer,
                preferredProduct,
            );

            if (isShowContinueCaseModal) {
                handleCloseModal();
                dispatch(setContinueActiveCaseModalVisible(true));

                return;
            }
        }

        dispatch(
            goToCreditFormsApp({
                offerType: clickedOffer?.type,
                organizationId,
            }),
        );
    };

    const handleCreateCallbackRequest = () => {
        handleCloseModal();

        dispatch(createCallbackRequest({ isTotalOffer, productCode: clickedOffer?.productCode }));
        dispatch(setCallbackRequestCreatedModalVisible(true));
    };

    return (
        <Modal
            open={isModalVisible}
            hasCloser={true}
            onClose={handleCloseModal}
            className={cn()}
            size='l'
        >
            <Modal.Header>
                <Typography.Title view='small' tag='h4' font='system' color='primary'>
                    Оформить кредит онлайн можно только с помощью КЭП
                </Typography.Title>
            </Modal.Header>
            <Modal.Content>
                <Typography.Text color='primary' tag='p'>
                    Если у вас нет КЭП, кредит можно получить в офисе банка. Отправьте заявку
                    менеджеру, чтобы согласовать время встречи.
                </Typography.Text>
                <Typography.Text color='primary' tag='p'>
                    Получите КЭП для руководителей или физлиц, чтобы подписывать документы и
                    операции в интернет-банке.
                </Typography.Text>
                <Link
                    view='default'
                    target='_blank'
                    href='https://alfabank.ru/help/t/corp/alfaforbusiness/sposobi-podpisaniya/usb-klyuch-i-sertifikat-ot-kriptopro/kak-poluchit-usb-klyuch-i-sertifikat-kep/'
                >
                    Что такое подпись КЭП?
                </Link>
            </Modal.Content>
            <Modal.Footer className={cn('footer')}>
                <Button
                    className={cn('button')}
                    size='s'
                    view='primary'
                    onClick={handleConfirmQESExistence}
                >
                    У меня есть КЭП
                </Button>
                <Button
                    className={cn('button')}
                    size='s'
                    view='filled'
                    onClick={handleCreateCallbackRequest}
                >
                    Отправить заявку менеджеру
                </Button>
            </Modal.Footer>
        </Modal>
    );
};
