@import '@alfalab/core-components/vars';

.credit-document-circulation-offer-pane {
    display: flex;
    position: relative;
    flex-direction: column;
    padding: var(--gap-16) var(--gap-24) var(--gap-24);
    border: 1px solid var(--color-dark-indigo-15-flat);
    background-color: var(--color-white);
    border-radius: var(--border-radius-8);
    transition:
        border 350ms,
        box-shadow 350ms;

    @media (--tablet-m) {
        background-repeat: no-repeat;
        background-image: url('./images/credit-document-circulation-offer.svg');
        background-position: right 5% bottom -1px;
        background-size: 300px;
    }

    @media (--desktop-m) {
        background-position: right 15% bottom -1px;
    }

    &__title {
        padding-bottom: var(--gap-12);
        width: 300px;

        @media (--mobile) {
            width: 100%;

            @mixin headline-system_xsmall;
        }
    }

    &__text {
        padding-bottom: var(--gap-24);

        @media (--tablet-m) {
            width: 400px;
            padding-bottom: var(--gap-16);
        }

        @media (--desktop-s) {
            width: 420px;
        }

        @media (--mobile) {
            @mixin paragraph_primary_small;
        }
    }

    &__button {
        width: 100%;

        @media (--tablet-m) {
            width: auto;
        }

        &_primary {
            margin-right: var(--gap-12);
        }

        margin: var(--gap-12) 0 0 0;
    }
}
