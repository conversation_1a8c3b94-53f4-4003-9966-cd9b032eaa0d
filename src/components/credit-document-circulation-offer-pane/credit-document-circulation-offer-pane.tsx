import React from 'react';
import { createCn } from 'bem-react-classname';

import { Button } from '@alfalab/core-components/button';
import { Typography } from '@alfalab/core-components/typography';

import classNames from '#/src/utils/class-names';

import './credit-document-circulation-offer-pane.css';

type TProps = {
    onExtraButtonClick: (event?: React.MouseEvent) => void;
    onButtonClick: (event?: React.MouseEvent) => void;
    buttonText: string;
    extraButtonText: string;
    className?: string;
};

const cn = createCn('credit-document-circulation-offer-pane');

const CreditDocumentCirculationOfferPane: React.FC<TProps> = ({
    onExtraButtonClick,
    onButtonClick,
    buttonText,
    extraButtonText,
    className,
}) => (
    <div className={classNames(cn(), className)} role='button'>
        <Typography.Title view='small' font='system' tag='div' className={cn('title')}>
            Документооборот по&nbsp;кредитным сделкам
        </Typography.Title>
        <Typography.Text className={cn('text')} color='primary'>
            Подача заявок на&nbsp;выдачу кредитов, кредитных линий, гарантий, и&nbsp;электронное
            подписание договоров.
        </Typography.Text>
        <div className={cn('buttons-wrapper')}>
            <Button
                view='primary'
                size='xs'
                className={cn('button', { primary: true })}
                onClick={onButtonClick}
            >
                {buttonText}
            </Button>
            {extraButtonText && onExtraButtonClick && (
                <Button
                    view='secondary'
                    size='xs'
                    className={cn('button')}
                    onClick={onExtraButtonClick}
                >
                    {extraButtonText}
                </Button>
            )}
        </div>
    </div>
);

export default CreditDocumentCirculationOfferPane;
