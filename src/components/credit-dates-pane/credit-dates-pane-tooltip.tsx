import React, { useMemo } from 'react';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Typography } from '@alfalab/core-components/typography';

import { PanePopupAmount } from '#/src/view-utils/credit-pane';

import { cn } from './credit-dates-pane';

import './credit-dates-pane.css';

type TProps = {
    debtToPay?: TAmount;
    interestToPay?: TAmount;
    overdueDebt?: TAmount;
    overdueInterest?: TAmount;
    totalFine: TAmount;
    isFineExists: boolean;
    isOverdueDebtExists?: boolean;
    isOverdueInterestExists?: boolean;
};

export const CreditDatesPaneTooltip: React.FC<TProps> = ({ debtToPay, interestToPay }: TProps) => {
    const content = useMemo(
        () =>
            [
                {
                    visible: !!debtToPay,
                    value: debtToPay,
                    text: 'основной долг',
                },
                {
                    visible: !!interestToPay,
                    value: interestToPay,
                    text: 'проценты',
                },
            ]
                .filter(({ visible }) => !!visible)
                .map(({ value, text }) => (
                    <PanePopupAmount
                        key={text}
                        className={cn('popup-amount')}
                        value={value}
                        text={text}
                    />
                )),
        [debtToPay, interestToPay],
    );

    return (
        <React.Fragment>
            <Typography.Text view='primary-small' className={cn('popup-label')} color='primary'>
                Что входит в платёж по кредиту:
                <br />
            </Typography.Text>
            {content}
        </React.Fragment>
    );
};
