import React from 'react';
import { createCn } from 'bem-react-classname';
import { type Amount as TAmount } from 'thrift-services/entities';

import { useMatchMedia } from '@alfalab/core-components/mq';
import { TitleView, VIEWS } from 'arui-private/title-view';

import { CreditDifferentDatesPane } from './credit-different-dates-pane';
import { CreditSameDatesPane } from './credit-same-dates-pane';

import './credit-dates-pane.css';

type TOwnProps = {
    isFineExists: boolean;
    isOverdueDebtExists?: boolean;
    isOverdueInterestExists?: boolean;
    totalToPay?: TAmount;
    totalLoanSumToPay?: TAmount;
    totalInterestSumToPay?: TAmount;
    totalFine?: TAmount;
    totalOverdueAndFine?: TAmount;
    debtToPay?: TAmount;
    interestToPay?: TAmount;
    overdueDebt?: TAmount;
    overdueInterest?: TAmount;
    onTogglePopup?: (popupName?: string) => void;
    payDebtTillDate?: { seconds: number };
    payInterestTillDate?: { seconds: number };
    currentTime: Date;
    isPaymentOfMainDebtAndInterestAtDifferentDates: boolean;
    isAnnuityScheduleType?: boolean;
};

type TProps = TOwnProps;

export const cn = createCn('credit-dates-pane');

const CreditDatesPane: React.FC<TProps> = ({
    payDebtTillDate,
    payInterestTillDate,
    totalToPay,
    totalLoanSumToPay,
    totalInterestSumToPay,
    totalFine,
    currentTime,
    totalOverdueAndFine,
    onTogglePopup,
    debtToPay,
    interestToPay,
    overdueDebt,
    overdueInterest,
    isFineExists,
    isOverdueDebtExists,
    isOverdueInterestExists,
    isPaymentOfMainDebtAndInterestAtDifferentDates,
    isAnnuityScheduleType,
}) => {
    const [isTablet] = useMatchMedia('--tablet-m');

    const conditionForDifferentDatesPane =
        isPaymentOfMainDebtAndInterestAtDifferentDates && !isAnnuityScheduleType;

    return (
        <div className={cn()}>
            {isTablet && (
                <TitleView view={VIEWS.SMALL} tag='h2'>
                    Платежи
                </TitleView>
            )}
            <div className={cn('wrapper')}>
                {conditionForDifferentDatesPane ? (
                    <CreditDifferentDatesPane
                        payDebtTillDate={payDebtTillDate}
                        payInterestTillDate={payInterestTillDate}
                        totalLoanSumToPay={totalLoanSumToPay}
                        totalInterestSumToPay={totalInterestSumToPay}
                        currentTime={currentTime}
                        overdueDebt={overdueDebt}
                        overdueInterest={overdueInterest}
                        totalFine={totalFine}
                        totalOverdueAndFine={totalOverdueAndFine}
                    />
                ) : (
                    <CreditSameDatesPane
                        payDebtTillDate={
                            isAnnuityScheduleType ? payInterestTillDate : payDebtTillDate
                        }
                        currentTime={currentTime}
                        totalToPay={totalToPay}
                        onTogglePopup={onTogglePopup}
                        debtToPay={debtToPay}
                        interestToPay={interestToPay}
                        overdueDebt={overdueDebt}
                        overdueInterest={overdueInterest}
                        totalFine={totalFine}
                        isFineExists={isFineExists}
                        isOverdueDebtExists={isOverdueDebtExists}
                        isOverdueInterestExists={isOverdueInterestExists}
                        totalOverdueAndFine={totalOverdueAndFine}
                    />
                )}
            </div>
        </div>
    );
};

export default CreditDatesPane;
