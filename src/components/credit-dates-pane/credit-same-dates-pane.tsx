import React from 'react';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Typography } from '@alfalab/core-components/typography';

import { SPACE_DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';
import AmountPure from '../ui/amount-pure';
import { BillCard } from '../ui/bill-card/bill-card';

import './credit-dates-pane.css';

type TProps = {
    isFineExists: boolean;
    isOverdueDebtExists?: boolean;
    isOverdueInterestExists?: boolean;
    totalToPay?: TAmount;
    totalInterestSumToPay?: TAmount;
    totalFine?: TAmount;
    totalOverdueAndFine?: TAmount;
    debtToPay?: TAmount;
    interestToPay?: TAmount;
    overdueDebt?: TAmount;
    overdueInterest?: TAmount;
    onTogglePopup?: (popupName?: string) => void;
    payDebtTillDate?: { seconds: number };
    currentTime: Date;
};

export const CreditSameDatesPane: React.FC<TProps> = ({
    payDebtTillDate,
    totalToPay,
    totalFine,
    currentTime,
    debtToPay,
    interestToPay,
    overdueDebt,
    overdueInterest,
    totalOverdueAndFine,
}) => {
    if (!totalToPay) return null;

    return (
        <React.Fragment>
            {(totalOverdueAndFine?.amount ?? 0) > 0 && (
                <BillCard
                    title='Просрочено'
                    isDanger={true}
                    text={<AmountPure value={totalOverdueAndFine} transparentMinor={false} />}
                    textForTooltip={
                        <div>
                            <Typography.Text tag='div'>
                                <AmountPure
                                    bold='full'
                                    value={overdueDebt}
                                    transparentMinor={false}
                                />
                                &nbsp;— основной долг
                            </Typography.Text>
                            <Typography.Text tag='div'>
                                <AmountPure
                                    bold='full'
                                    value={overdueInterest}
                                    transparentMinor={false}
                                />
                                &nbsp;— проценты
                            </Typography.Text>
                            <Typography.Text tag='div'>
                                <AmountPure
                                    bold='full'
                                    value={totalFine}
                                    transparentMinor={false}
                                />
                                &nbsp;— неустойка
                            </Typography.Text>
                        </div>
                    }
                    subText='Пополните счёт'
                />
            )}
            {(totalToPay?.amount ?? 0) > 0 && (
                <BillCard
                    title='Ближайший'
                    text={<AmountPure value={totalToPay} transparentMinor={false} />}
                    textForTooltip={
                        <div>
                            <Typography.Text tag='div'>
                                <AmountPure
                                    bold='full'
                                    value={debtToPay}
                                    transparentMinor={false}
                                />
                                &nbsp;— основной долг
                            </Typography.Text>
                            <Typography.Text tag='div'>
                                <AmountPure
                                    bold='full'
                                    value={interestToPay}
                                    transparentMinor={false}
                                />
                                &nbsp;— проценты
                            </Typography.Text>
                        </div>
                    }
                    subText={dateToCustomFormat(currentTime, payDebtTillDate, SPACE_DATE_FORMAT)}
                />
            )}
        </React.Fragment>
    );
};
