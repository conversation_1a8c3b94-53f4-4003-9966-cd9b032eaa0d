import React, { useEffect } from 'react';
import { createCn } from 'bem-react-classname';

import { Button } from '@alfalab/core-components/button';
import { Typography } from '@alfalab/core-components/typography';
import { FileUpload } from 'arui-private/file-upload';

import './styles.css';

const cn = createCn('attach-documents-page');

const hintsFileUpload = [
    'Размер одного файла не должен превышать 5 МБ',
    'Общий размер загруженных файлов не больше 20 МБ',
];

type Props = {
    header: React.ReactNode;
    analystFullName?: string;
    analystMobilePhone?: string;
    analystWorkPhone?: string;
    analystAdditionalPhone?: string;
    analystEmail?: string;
    docsDescription?: string;
    documents: React.ComponentProps<typeof FileUpload>['fileUploadItems'];
    isOverflowAllowedSizeDocuments: boolean;
    isProcessButtonsDisabled: boolean;
    onDocumentUpload: (files: File[]) => void;
    onDocumentDelete: (documentId: string) => void;
    onSendButtonClick: () => void;
    onBackButtonClick: () => void;
    onFirstPageRender: () => void;
};

const AttachDocumentsPage: React.FC<Props> = ({
    header,
    docsDescription,
    documents,
    isOverflowAllowedSizeDocuments,
    isProcessButtonsDisabled,
    onDocumentUpload,
    onDocumentDelete,
    onSendButtonClick,
    onBackButtonClick,
    onFirstPageRender,
}) => {
    useEffect(() => {
        onFirstPageRender();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const error = isOverflowAllowedSizeDocuments ? 'Файлы превысили допустимый размер' : '';

    return (
        <div className={cn()}>
            {header}
            <div className={cn('upload-files')}>
                <FileUpload
                    description={<div className={cn('description')}>{docsDescription}</div>}
                    attachButtonText='Прикрепить файл'
                    attachHint='.pdf, .jpeg, .jpg, .png, .doc, .docx, .xls, .xlsx'
                    accept='.pdf,.jpeg,.jpg,.png,.doc,.docx,.xls,.xlsx'
                    hints={hintsFileUpload}
                    fileUploadItems={documents}
                    block={true}
                    error={error}
                    onFileUpload={onDocumentUpload}
                    onFileDelete={onDocumentDelete}
                    disabled={isOverflowAllowedSizeDocuments}
                />
                <div className={cn('info')}>
                    <Typography.Title
                        className={cn('info-title')}
                        view='xsmall'
                        weight='bold'
                        tag='div'
                        font='system'
                    >
                        Дальнейшие шаги
                    </Typography.Title>
                    <Typography.Text tag='div' view='primary-medium'>
                        Мы проверим ваши файлы на наличие вирусов и передадим кредитному аналитику
                        для&nbsp;рассмотрения. В ближайшее время с вами свяжется сотрудник и сообщит
                        о&nbsp;решении банка.
                    </Typography.Text>
                </div>
                <div className={cn('buttons-group')}>
                    <Button
                        className={cn('button')}
                        onClick={onSendButtonClick}
                        size='s'
                        view='primary'
                        disabled={isProcessButtonsDisabled}
                    >
                        Отправить
                    </Button>
                    <Button
                        className={cn('button')}
                        onClick={onBackButtonClick}
                        size='s'
                        view='tertiary'
                    >
                        Вернуться назад
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default AttachDocumentsPage;
