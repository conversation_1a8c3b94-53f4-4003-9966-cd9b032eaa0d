@import '@alfalab/core-components/vars';

.attach-documents-page {
    &__upload-files {
        max-width: 813px;
        padding: var(--gap-32);
        background-color: var(--color-white);
        border: 1px solid var(--color-dark-indigo-15-flat);
        border-radius: var(--border-radius-8);
    }

    &__info {
        margin-top: var(--gap-32);
        margin-bottom: var(--gap-32);
    }

    &__info-title {
        margin-bottom: var(--gap-16);
    }

    &__subheading {
        display: flex;
        flex-direction: column;
    }

    &__description {
        white-space: pre-line;
        margin-bottom: var(--gap-24);
    }

    &__buttons-group {
        display: flex;
        flex-direction: column;

        @media (--tablet-s) {
            flex-direction: row;
        }
    }

    &__button {
        & + & {
            margin-left: 0;
            margin-top: var(--gap-20);

            @media (--tablet-s) {
                margin-left: var(--gap-24);
                margin-top: 0;
            }
        }
    }
}
