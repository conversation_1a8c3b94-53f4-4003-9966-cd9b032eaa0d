import * as React from 'react';
import { type UnixEpoch } from 'thrift-services/utils';

import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';

import { DAY_MONTH_YEAR_FORMAT } from '#/src/constants/date';
import { NBSP } from '#/src/constants/unicode-symbols';
import { dateToCustomFormat } from '#/src/utils/date';

import { type TCreditSchedulePaymentProps } from '../credit-schedule/credit-schedule-payment';
import { type TGuarantySchedulePaymentProps } from '../guaranty-schedule/guaranty-schedule-payment';

import { cn } from './payment-schedule';

import './payment-schedule.css';

type TProps = {
    payedPayments?: React.ReactElement<TCreditSchedulePaymentProps>;
    futurePayments?: React.ReactElement<TCreditSchedulePaymentProps>;
    payments?: React.ReactElement<TGuarantySchedulePaymentProps>;
    heading?: React.ReactNode;
    todayDate?: UnixEpoch | null;
    currentTime: Date;
};

export const PaymentScheduleMobile: React.FC<TProps> = ({
    payedPayments,
    futurePayments,
    payments,
    heading,
    todayDate,
    currentTime,
}) => (
    <div className={cn()}>
        {heading}
        <BackgroundPlate
            view={BackgroundPlateView.Secondary}
            className={cn('mobile')}
            data-test-id='payment-schedule-mobile'
        >
            {(!!futurePayments?.props?.payments?.length || !!payments?.props?.payments?.length) && (
                <React.Fragment>
                    <div className={cn('plate')}>
                        <Typography.Text view='caps' weight='bold' tag='div'>
                            График платежей на{NBSP}
                            {dateToCustomFormat(currentTime, todayDate, DAY_MONTH_YEAR_FORMAT)}
                        </Typography.Text>
                    </div>
                    {futurePayments || payments}
                </React.Fragment>
            )}
            {!!payedPayments?.props?.payments?.length && (
                <React.Fragment>
                    <div className={cn('plate')}>
                        <Typography.Text view='caps' weight='bold' tag='div'>
                            Оплаченные платежи
                        </Typography.Text>
                    </div>
                    {payedPayments}
                </React.Fragment>
            )}
        </BackgroundPlate>
    </div>
);
