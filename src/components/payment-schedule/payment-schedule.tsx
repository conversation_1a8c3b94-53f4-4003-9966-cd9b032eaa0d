import * as React from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type UnixEpoch } from 'thrift-services/utils';

import { useMatchMedia } from '@alfalab/core-components/mq';

import { currentTimeSelector } from '#/src/ducks/settings/selectors';

import { type TCreditSchedulePaymentProps } from '../credit-schedule/credit-schedule-payment';
import { type TGuarantySchedulePaymentProps } from '../guaranty-schedule/guaranty-schedule-payment';

import { PaymentScheduleDesktop } from './payment-schedule-desktop';
import { PaymentScheduleMobile } from './payment-schedule-mobile';

import './payment-schedule.css';

export const cn = createCn('payment-schedule');

type TProps = {
    payedPayments?: React.ReactElement<TCreditSchedulePaymentProps>;
    futurePayments?: React.ReactElement<TCreditSchedulePaymentProps>;
    payments?: React.ReactElement<TGuarantySchedulePaymentProps>;
    isFetching?: boolean;
    tableHeaders: string[];
    heading?: React.ReactNode;
    skeleton: React.ReactNode;
    todayDate?: UnixEpoch | null;
};

const PaymentSchedule: React.FC<TProps> = ({
    isFetching = false,
    payedPayments,
    futurePayments,
    payments,
    skeleton,
    tableHeaders,
    heading,
    todayDate,
}) => {
    const [isTablet] = useMatchMedia('--tablet-m');
    const currentTime = useSelector(currentTimeSelector);

    if (isFetching) {
        return <React.Fragment>{skeleton}</React.Fragment>;
    }

    if (isTablet) {
        return (
            <PaymentScheduleDesktop
                tableHeaders={tableHeaders}
                payedPayments={payedPayments}
                futurePayments={futurePayments}
                payments={payments}
                heading={heading}
                todayDate={todayDate}
                currentTime={currentTime}
            />
        );
    }

    return (
        <PaymentScheduleMobile
            payedPayments={payedPayments}
            futurePayments={futurePayments}
            payments={payments}
            heading={heading}
            todayDate={todayDate}
            currentTime={currentTime}
        />
    );
};

export default PaymentSchedule;
