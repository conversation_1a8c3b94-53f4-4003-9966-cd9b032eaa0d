@import '@alfalab/core-components/vars';

.payment-schedule {
    margin-bottom: var(--gap-8);

    &__mobile {
        border: 0;
        padding: var(--gap-16);
        margin-bottom: var(--gap-8);
    }

    &__plate {
        background: var(--color-light-bg-secondary);
        padding: var(--gap-12) var(--gap-8);
        height: auto;

        @media (--small-only) {
            margin: var(--gap-8) 0 var(--gap-4) 0;
            padding: 0;
        }
    }

    &__table-title {
        margin-bottom: var(--gap-8);
    }

    &__future-payments {
        padding-bottom: var(--gap-12);
        @mixin promo-system_small;

        &_dashed {
            border-bottom: 1px dashed color(var(--color-dark-indigo-07) a(0.3));
        }
    }

    &__payed-payments {
        padding-top: var(--gap-16);
    }

    &__payed-payments-heading {
        padding: 0 0 var(--gap-12) var(--gap-8);
    }
}
