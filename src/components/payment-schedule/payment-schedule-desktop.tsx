import * as React from 'react';
import { type UnixEpoch } from 'thrift-services/utils';

import { Table } from '@alfalab/core-components/table';
import { Typography } from '@alfalab/core-components/typography';

import { DAY_MONTH_YEAR_FORMAT } from '#/src/constants/date';
import { NBSP } from '#/src/constants/unicode-symbols';
import { dateToCustomFormat } from '#/src/utils/date';

import { type TCreditSchedulePaymentProps } from '../credit-schedule/credit-schedule-payment';
import { type TGuarantySchedulePaymentProps } from '../guaranty-schedule/guaranty-schedule-payment';

import { cn } from './payment-schedule';

import './payment-schedule.css';

type TProps = {
    payedPayments?: React.ReactElement<TCreditSchedulePaymentProps>;
    futurePayments?: React.ReactElement<TCreditSchedulePaymentProps>;
    payments?: React.ReactElement<TGuarantySchedulePaymentProps>;
    tableHeaders: string[];
    heading?: React.ReactNode;
    todayDate?: UnixEpoch | null;
    currentTime: Date;
};

export const PaymentScheduleDesktop: React.FC<TProps> = ({
    payedPayments,
    futurePayments,
    payments,
    tableHeaders,
    heading,
    todayDate,
    currentTime,
}) => (
    <div className={cn()}>
        {heading}
        <div className={cn('table-body')}>
            <Table dataTestId='payment-schedule'>
                <Table.THead>
                    {tableHeaders.map((header, index) => (
                        <Table.THeadCell
                            key={`payment-schedule-header__${index}`}
                            textAlign={index === 0 ? 'left' : 'right'}
                            title={header}
                        >
                            {header}
                        </Table.THeadCell>
                    ))}
                </Table.THead>
                <Table.TBody>
                    {(!!futurePayments?.props?.payments?.length ||
                        !!payments?.props?.payments?.length) && (
                        <React.Fragment>
                            <Table.TRow withoutBorder={true}>
                                <Table.TCell colSpan={100} className={cn('plate')}>
                                    <Typography.Text view='caps' weight='bold' tag='div'>
                                        График платежей на{NBSP}
                                        {dateToCustomFormat(
                                            currentTime,
                                            todayDate,
                                            DAY_MONTH_YEAR_FORMAT,
                                        )}
                                    </Typography.Text>
                                </Table.TCell>
                            </Table.TRow>
                            {futurePayments || payments}
                        </React.Fragment>
                    )}
                    {!!payedPayments?.props?.payments?.length && (
                        <React.Fragment>
                            <Table.TRow withoutBorder={true}>
                                <Table.TCell colSpan={100} className={cn('plate')}>
                                    <Typography.Text view='caps' weight='bold' tag='div'>
                                        Оплаченные платежи
                                    </Typography.Text>
                                </Table.TCell>
                            </Table.TRow>
                            {payedPayments}
                        </React.Fragment>
                    )}
                </Table.TBody>
            </Table>
        </div>
    </div>
);
