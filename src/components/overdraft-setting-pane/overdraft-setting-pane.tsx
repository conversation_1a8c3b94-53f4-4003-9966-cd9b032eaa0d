import React, { memo } from 'react';
import { createCn } from 'bem-react-classname';

import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';
import { PencilMIcon } from '@alfalab/icons-glyph/PencilMIcon';

import './overdraft-setting-pane.css';

const cn = createCn('overdraft-setting-pane');

export type OverdraftPaneStatus = {
    text: string;
    view: 'success' | 'warning';
};

type Props = {
    title: string;
    description: string;
    settingValue: string;
    status?: OverdraftPaneStatus;
    onEditClick: () => void;
    pending?: boolean;
};

const OverdraftSettingPane: React.FC<Props> = ({
    title,
    settingValue,
    onEditClick,
    description,
    status,
    pending,
}) => (
    <Skeleton className={cn()} animate={true} visible={pending}>
        <div className={cn('header')}>
            <div className={cn('header-content')}>
                <Typography.Title
                    className={cn('title')}
                    tag='div'
                    view='xsmall'
                    weight='bold'
                    font='system'
                >
                    {title}
                </Typography.Title>
                <Typography.Text className={cn('value')} view='primary-medium'>
                    {settingValue}
                </Typography.Text>
            </div>
            <div className={cn('header-button')} onClick={onEditClick}>
                <PencilMIcon className={cn('icon')} />
            </div>
        </div>
        <div className={cn('content')}>
            <Typography.Text className={cn('content-text')} view='primary-small'>
                {description}
            </Typography.Text>
        </div>
        {!!status && (
            <div
                className={cn('status', {
                    view: status.view,
                })}
            >
                <Typography.Text view='component'>{status.text}</Typography.Text>
            </div>
        )}
    </Skeleton>
);

export default memo(OverdraftSettingPane);
