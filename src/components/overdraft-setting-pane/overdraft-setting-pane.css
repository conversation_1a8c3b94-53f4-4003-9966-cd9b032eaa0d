@import '@alfalab/core-components/vars';

.overdraft-setting-pane {
    background-color: var(--color-white);
    box-shadow: var(--shadow-xs);
    border-radius: var(--border-radius-8);
    box-sizing: border-box;
    padding: var(--gap-24);
    color: var(--color-dark-indigo);
    height: 100%;

    &__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--gap-16);
    }

    &__title {
        margin-right: var(--gap-12);

        @mixin headline-system_xsmall;
    }

    &__value {
        @mixin paragraph_component_primary;
    }

    &__header-content {
        display: flex;
        align-items: flex-end;
    }

    &__header-button {
        width: 24px;
        height: 24px;
        cursor: pointer;
    }

    &__link {
        margin-top: var(--gap-12);
    }

    &__status {
        margin-top: var(--gap-16);

        &_view_success {
            color: var(--color-static-status-green);
        }

        &_view_warning {
            color: var(--color-static-status-orange);
        }
    }
}
