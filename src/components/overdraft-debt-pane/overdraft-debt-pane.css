@import '@alfalab/core-components/vars';

.overdraft-debt-pane {
    position: relative;
    background-color: var(--color-white);
    border: 1px solid var(--color-dark-indigo-15-flat);
    border-radius: var(--border-radius-8);
    transition:
        border 350ms,
        box-shadow 350ms;
    box-sizing: border-box;
    padding: var(--gap-32) var(--gap-32) var(--gap-8);
    margin: 0 0 var(--gap-24) 0;

    &_overdue {
        border-left: 4px solid var(--color-red-error);
    }

    &_compressed {
        padding-bottom: 0;
    }

    @media (--small-only) {
        padding-left: var(--gap-24);
        width: auto;
    }
}
