import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators, type Dispatch } from 'redux';
import { type CreditProductProjectionRequest } from 'corp-core-credit-products-api-typescript-services';

import { getTrancheClosePaymentAmountStart } from '#/src/ducks/credit-products/actions';
import { type ProductByType } from '#/src/ducks/credit-products/reducer/selected-credit-product';
import { closestTranchePaymentAmountSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';

import { ECreditProducts } from '../../constants/credit-products';
import { type ApplicationState } from '../../ducks/application-state';
import { checkIsFaultyCreditProduct } from '../../utils/credit-products-mappers';
import ProductWithTranchesDebtPane from '../product-with-tranches-debt-pane';

import './overdraft-debt-pane.css';

function mapStateToProps(state: ApplicationState) {
    return {
        currentTime: currentTimeSelector(state),
        organizationId: currentOrganizationEqIdSelector(state),
        closestTranchePaymentAmount: closestTranchePaymentAmountSelector(state),
    };
}

function mapDispatchToProps(dispatch: Dispatch) {
    return {
        ...bindActionCreators(
            {
                getTrancheClosePaymentAmountStart,
            },
            dispatch,
        ),
    };
}

type OwnProps = {
    overdraft: ProductByType['overdraft'];
    isOverdue: boolean;
    showOnlyFine: boolean;
    withTranches: boolean;
    isFromMMB: boolean;
    onTogglePopup?: (popupName: string) => void;
    isClearOverdraft: boolean;
};

type Props = OwnProps & ReturnType<typeof mapStateToProps> & ReturnType<typeof mapDispatchToProps>;

const OverdraftDebtPane: React.FC<Props> = ({
    overdraft,
    closestTranchePaymentAmount,
    withTranches,
    isClearOverdraft,
    isFromMMB,
    organizationId,
    onTogglePopup,
    getTrancheClosePaymentAmountStart,
}) => {
    useEffect(() => {
        const projection: CreditProductProjectionRequest = {
            withFault: true,
            withDocs: true,
        };

        if (withTranches && overdraft?.docNumber) {
            getTrancheClosePaymentAmountStart(overdraft.docNumber, organizationId, projection);
        }
    }, []);

    if (checkIsFaultyCreditProduct(overdraft)) {
        return null;
    }

    const {
        totalFine,
        fineDebt,
        fineInterest,
        actualDate,
        totalInterestOverdue,
        totalToPay,
        debts,
    } = overdraft || {};

    return (
        <ProductWithTranchesDebtPane
            productType={ECreditProducts.OVERDRAFT}
            actualDate={actualDate}
            totalToPay={totalToPay}
            totalInterestOverdue={totalInterestOverdue}
            totalFine={totalFine}
            fineDebt={fineDebt}
            fineInterest={fineInterest}
            onTogglePopup={onTogglePopup}
            closestTranchePaymentAmount={closestTranchePaymentAmount}
            overdueDebt={debts?.loan?.overdueDebt}
            overdueInterest={debts?.interest?.overdueInterest}
            debtToPay={debts?.loan?.debtToPay}
            withTranches={withTranches}
            isClearOverdraft={isClearOverdraft}
            isFromMMB={isFromMMB}
            eliminateNegativeMargins={true}
        />
    );
};

export default connect(mapStateToProps, mapDispatchToProps)(OverdraftDebtPane);
