import * as React from 'react';
import { createCn } from 'bem-react-classname';

import './credit-schedule-row.css';

type TOwnProps = {
    first: React.ReactNode | string;
    second: React.ReactNode | string;
    third: React.ReactNode | string;
    fourth: React.ReactNode | string;
    fifth: React.ReactNode | string;
    withHover?: boolean;
};

const cn = createCn('schedule-row');

const CreditScheduleRow: React.FC<TOwnProps> = ({
    first,
    second,
    third,
    fourth,
    fifth,
    withHover = false,
}) => (
    <div className={cn({ animated: withHover })}>
        <div className={cn('cell', { first: true })}>{first}</div>
        <div className={cn('cell', { second: true })}>{second}</div>
        <div className={cn('cell', { third: true })}>{third}</div>
        <div className={cn('cell', { fourth: true })}>{fourth}</div>
        <div className={cn('cell', { fifth: true })}>{fifth}</div>
    </div>
);

export default CreditScheduleRow;
