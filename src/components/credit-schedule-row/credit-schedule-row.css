@import '@alfalab/core-components/vars';

.schedule-row {
    display: flex;
    justify-content: space-between;
    padding: var(--gap-12) var(--gap-8) var(--gap-12);
    border-radius: var(--border-radius-8);

    @media (--small-only) {
        padding-right: 0;
    }

    &__cell {
        display: flex;
        justify-content: flex-end;
        margin-right: var(--gap-8);

        @mixin paragraph_primary_medium;

        &_first {
            justify-content: flex-start;
            flex: 1 5 170px;
        }

        &_second {
            flex: 1 5 200px;

            @media (--small-only) {
                margin-right: 0;
            }
        }

        &_third {
            display: none;

            @media (--desktop-l) {
                display: flex;
                flex: 1 1.5 200px;
            }
        }

        &_fourth {
            display: none;

            @media (--desktop-l) {
                display: flex;
                flex: 1 1 150px;
            }
        }

        &_fifth {
            display: none;

            @media (--medium) {
                display: flex;
                flex: 2 7 250px;
            }
        }

        &:last-of-type {
            margin-right: 0;
        }
    }

    &_animated {
        &:hover {
            background-color: var(--color-dark-indigo-05);
        }
    }
}
