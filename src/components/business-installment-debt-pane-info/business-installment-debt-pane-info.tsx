import React from 'react';
import { createCn } from 'bem-react-classname';
import { type Amount as TAmount } from 'thrift-services/entities';

import { PaneAmount } from '#/src/view-utils/credit-pane';

import PaneGroup from '../ui/pane-group';
import PaneItem from '../ui/pane-item';

import './business-installment-debt-pane-info.css';

export const cn = createCn('business-installment-debt-pane-info');

type TOwnProps = {
    debt: TAmount;
    totalFine: TAmount;
    isFineExists: boolean;
    onTogglePopup?: (popupName?: string) => void;
};

type TProps = TOwnProps;

const BusinessInstallmentDebtPaneInfo: React.FC<TProps> = ({
    debt,
    isFineExists,
    totalFine,
    onTogglePopup,
}: TProps) => {
    const tooltipText = (
        <p className={cn('popup-text')}>{`0,5% за каждый день\nпросроченного платежа`}</p>
    );

    return (
        <div className={cn()}>
            <PaneGroup
                className={cn('pane-group')}
                justify='left'
                withoutBorder={true}
                withDivider={isFineExists}
            >
                <PaneItem
                    withTooltip={true}
                    title='Основной долг'
                    key='total-amount-to-pay'
                    titleSize='secondary-large'
                    titleColor='secondary'
                    className={cn('pane-item')}
                    text={!!debt && <PaneAmount value={debt} weight='bold' />}
                />
                {isFineExists && (
                    <PaneItem
                        key='fine-for-overdue'
                        title='Неустойка за просрочку'
                        titleSize='secondary-large'
                        className={cn('pane-item', { inflexible: true })}
                        text={
                            !!totalFine && (
                                <PaneAmount color='accent' value={totalFine} weight='bold' />
                            )
                        }
                        withTooltip={true}
                        tooltipPosition='top'
                        textForTooltip={tooltipText}
                        popupName='fineForOverdueTooltip'
                        onTogglePopup={onTogglePopup}
                    />
                )}
            </PaneGroup>
        </div>
    );
};

export default BusinessInstallmentDebtPaneInfo;
