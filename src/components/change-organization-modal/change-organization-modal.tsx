import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Modal } from '@alfalab/core-components/modal';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Space } from '@alfalab/core-components/space';
import { Typography } from '@alfalab/core-components/typography';
import { NBSP } from 'arui-private/lib/formatters';

import { setChangeOrganizationModalVisible } from '#/src/ducks/organization/actions';
import {
    changeOrganizationModalSelector,
    getCurrentSharedOrganizationShortNameSelector,
} from '#/src/ducks/organization/selectors';
import { useHoldingControls } from '#/src/utils/hooks/use-holding-controls';

export const CHANGE_ORGANIZATION_MODAL_CONTINUE_FLAG = 'CHANGE_ORGANIZATION_MODAL_CONTINUE_FLAG';

export const ChangeOrganizationModal = () => {
    const dispatch = useDispatch();
    const [isMobile] = useMatchMedia('--mobile');

    const { isGroup, value } = useHoldingControls();

    const { isVisible, organizationName, organizationId, action } = useSelector(
        changeOrganizationModalSelector,
    );
    const currentOrganizationName = useSelector(getCurrentSharedOrganizationShortNameSelector);

    const companyName = isGroup
        ? `группы компаний ${value?.groupInfo?.groupName}`
        : `${currentOrganizationName}`;

    const handleCloseModal = useCallback(() => {
        dispatch(
            setChangeOrganizationModalVisible({
                isVisible: false,
            }),
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleChangeOrganization = useCallback(() => {
        if (action) {
            dispatch({
                ...action,
                organizationId,
                [CHANGE_ORGANIZATION_MODAL_CONTINUE_FLAG]: true,
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [organizationId, action]);

    if (isMobile) {
        return (
            <BottomSheet
                title='Переключить компанию?'
                open={isVisible}
                onClose={handleCloseModal}
                hasCloser={true}
                actionButton={
                    <Space fullWidth={true} direction='horizontal'>
                        <Button view='secondary' size='s' onClick={handleCloseModal} block={true}>
                            Отменить
                        </Button>
                        <Button
                            view='primary'
                            size='s'
                            onClick={handleChangeOrganization}
                            block={true}
                        >
                            Переключить
                        </Button>
                    </Space>
                }
            >
                <Typography.Text tag='p' color='primary'>
                    {`Вы${NBSP}работаете от${NBSP}${companyName}, а${NBSP}хотите посмотреть данные по${NBSP}${organizationName}`}
                </Typography.Text>
            </BottomSheet>
        );
    }

    return (
        <Modal open={isVisible} hasCloser={true} onClose={handleCloseModal} size='s'>
            <Modal.Header hasCloser={true} title='Переключить компанию?' />
            <Modal.Content>
                <Typography.Text tag='p' defaultMargins={false} color='primary'>
                    {`Вы${NBSP}работаете от${NBSP}${companyName}, а${NBSP}хотите посмотреть данные по${NBSP}${organizationName}`}
                </Typography.Text>
            </Modal.Content>
            <Modal.Footer>
                <Modal.Controls
                    primary={
                        <Button view='primary' size='s' onClick={handleChangeOrganization}>
                            Переключить
                        </Button>
                    }
                    secondary={
                        <Button view='secondary' size='s' onClick={handleCloseModal}>
                            Отменить
                        </Button>
                    }
                />
            </Modal.Footer>
        </Modal>
    );
};
