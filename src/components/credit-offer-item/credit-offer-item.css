@import '@alfalab/core-components/vars';

.offer-item {
    &__heading-section {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        flex-wrap: wrap;

        @media (--medium) {
            flex-wrap: nowrap;
        }
    }

    &__heading-content {
        display: flex;
        flex-direction: column;
    }

    &__heading-text {
        margin-top: var(--gap-8);
    }

    &__heading-link {
        width: 100%;
        margin-top: var(--gap-12);
        margin-left: 0;

        @media (--medium) {
            margin-top: 0;
            width: auto;
            margin-left: var(--gap-8);
        }

        &_credit-limit {
            display: block;
            color: var(--color-light-text-primary);

            @media (--small-only) {
                display: none;
            }

            &-mobile {
                display: none;
                color: var(--color-light-text-primary);

                @media (--small-only) {
                    display: block;
                    margin-top: var(--gap-24);
                }

                @media (--mobile) {
                    text-align: center;
                }
            }
        }
    }

    &__offers-section {
        margin-top: var(--gap-16);
    }

    &__subscription-section {
        display: inline-block;
        margin-top: var(--gap-24);
        color: var(--color-dark-text-secondary-inverted-hover);

        @media (--xlarge) {
            margin-top: var(--gap-32);
        }
    }
}
