import React, { useCallback, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Link } from '@alfalab/core-components/link';
import { Typography } from '@alfalab/core-components/typography';
import { TitleView, VIEWS } from 'arui-private/title-view';

import { CreditOfferItemBadge } from '#/src/components/credit-offer-item-badge';
import { CreditOfferItemRows } from '#/src/components/credit-offer-item-rows';
import { CreditOfferPlate } from '#/src/components/credit-offer-plate';
import {
    ECreditOffers,
    type ECreditOffersCampaignCode,
    OffersWithSubscriptionAvailable,
    TotalOffersCampaignCode,
} from '#/src/constants/credit-offers';
import { isCodeDefinitelyApproved } from '#/src/constants/credit-products';
import { LAQUO, NBSP, RAQUO } from '#/src/constants/unicode-symbols';
import { isCreditCalculatorActiveSelector } from '#/src/ducks/credit-calculator/selectors';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { currentRoleSelector } from '#/src/ducks/user/selectors';
import { CREDIT_OFFERS_METRICS, CREDITSB_METRICS } from '#/src/metrics';
import { clickOfferByCreditCalculator } from '#/src/metrics/credit-calculator';
import { writeDataToLocalStorageCreditLimit } from '#/src/utils/credit-offers-helpers';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';
import { useLandingModalLink } from '#/src/utils/hooks/use-landing-modal-link';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import { creditOfferItemCn } from './credit-offer-item-cn';

type Props = {
    productInfo: TMappedCreditOffer;
    title: string | React.ReactNode;
    onDetailedButtonClick?(): void;
    onGoToQuestionnaireButtonClick?(offer: TMappedCreditOffer): void;
    isCommonCheckBlockedOverdraft?: boolean;
    isBusinessCreditCard?: boolean;
};

export const CreditOfferItem = ({
    productInfo,
    onGoToQuestionnaireButtonClick,
    onDetailedButtonClick,
    title,
    isCommonCheckBlockedOverdraft,
    isBusinessCreditCard,
}: Props) => {
    const trackAlfaMetrics = useTrackAlfaMetrics();
    const currentTime = useSelector(currentTimeSelector);
    const currentRole = useSelector(currentRoleSelector);
    const isCreditCalculatorActive = useSelector(isCreditCalculatorActiveSelector);

    const handleDetailedClick = useLandingModalLink(productInfo, onDetailedButtonClick);
    const handleGoToQuestionnaireClick = useLandingModalLink(
        productInfo,
        onGoToQuestionnaireButtonClick,
    );

    const { type, preApproved, campaignCode, expiryDate, productCode } = productInfo;

    const additionalOfferExpiryDate = productInfo.additionalOffer?.expiryDate;
    const isBusinessCreditLimitOffer = type === ECreditOffers.CREDIT_LIMIT;
    const isTotalOffer = TotalOffersCampaignCode.includes(
        campaignCode as ECreditOffersCampaignCode,
    );
    const earlierExpiryDate =
        preApproved &&
        additionalOfferExpiryDate &&
        additionalOfferExpiryDate.seconds < expiryDate.seconds
            ? additionalOfferExpiryDate
            : expiryDate;
    const isSubscriptionAvailable = useMemo(
        () =>
            !isCodeDefinitelyApproved(campaignCode) &&
            OffersWithSubscriptionAvailable.includes(type),
        [campaignCode, type],
    );

    const handleDetailedButtonClick = useCallback(
        (event: React.MouseEvent, preventDetailedClick?: boolean) => {
            if (trackAlfaMetrics) {
                trackAlfaMetrics(
                    CREDIT_OFFERS_METRICS[
                        preApproved
                            ? 'clickPreApprovedCreditPaneDetailedButton'
                            : 'clickStandardToLandingMetric'
                    ],
                    {
                        userRole: currentRole,
                        typeOffer: preApproved ? campaignCode : type,
                        preApprovedOffer: preApproved,
                        campaignCodeCommon: campaignCode,
                        productIdCommon: type,
                    },
                );

                if (type === ECreditOffers.CREDIT_LIMIT) {
                    trackAlfaMetrics(CREDITSB_METRICS.clickDetailedButtonBanner, {});
                }

                if (isCreditCalculatorActive) {
                    trackAlfaMetrics(clickOfferByCreditCalculator, {});
                }
            }

            if (
                (isSubscriptionAvailable || OffersWithSubscriptionAvailable.includes(type)) &&
                !preventDetailedClick
            ) {
                onDetailedButtonClick?.();
            } else {
                handleDetailedClick(event);
            }

            event.stopPropagation();
        },
        [
            trackAlfaMetrics,
            isSubscriptionAvailable,
            type,
            preApproved,
            currentRole,
            campaignCode,
            isCreditCalculatorActive,
            onDetailedButtonClick,
            handleDetailedClick,
        ],
    );

    const handleGoToQuestionnaireButtonClick = useCallback(
        (
            event: React.MouseEvent,
            offer: TMappedCreditOffer,
            isPreApprovedCreditLimit?: boolean,
        ) => {
            trackAlfaMetrics(
                CREDIT_OFFERS_METRICS[
                    offer.preApproved
                        ? 'clickPreApprovedCreditToFormsMetric'
                        : 'clickStandardToFormsMetric'
                ],
                {
                    userRole: currentRole,
                    typeOffer: offer.preApproved ? offer.campaignCode : offer.type,
                    amount: offer.maximumAmount?.amount,
                    preApprovedOffer: offer.preApproved,
                    ...(offer.preApproved && { isTotalOffer }),
                    campaignCodeCommon: campaignCode,
                    productIdCommon: type,
                },
            );

            if (isCreditCalculatorActive) {
                trackAlfaMetrics(clickOfferByCreditCalculator, {});
            }

            if (isBusinessCreditLimitOffer) {
                writeDataToLocalStorageCreditLimit({
                    offerId: offer.id,
                    isPreApproved: isPreApprovedCreditLimit,
                });
                trackAlfaMetrics(
                    CREDITSB_METRICS[
                        isPreApprovedCreditLimit
                            ? 'clickRedirectButtonBanner'
                            : 'clickAnotherTermsBanner'
                    ],
                    { deviceScreenName: 'Banner PA' },
                );
            }

            if (isSubscriptionAvailable || OffersWithSubscriptionAvailable.includes(type)) {
                onGoToQuestionnaireButtonClick?.(offer);
            } else {
                handleGoToQuestionnaireClick(offer);
            }

            event.stopPropagation();
        },
        [
            trackAlfaMetrics,
            currentRole,
            isTotalOffer,
            campaignCode,
            type,
            isCreditCalculatorActive,
            isBusinessCreditLimitOffer,
            isSubscriptionAvailable,
            onGoToQuestionnaireButtonClick,
            handleGoToQuestionnaireClick,
        ],
    );

    const handlePaneClick = useCallback(
        (event: React.MouseEvent) => {
            if (isCommonCheckBlockedOverdraft) return false;

            handleDetailedButtonClick(event);
        },
        [isCommonCheckBlockedOverdraft, handleDetailedButtonClick],
    );

    const handleLinkClick = useCallback(
        (event: React.MouseEvent) => {
            if (isBusinessCreditLimitOffer) {
                handleGoToQuestionnaireButtonClick(event, productInfo);
            } else {
                handleDetailedButtonClick(event);
            }
        },
        [
            handleDetailedButtonClick,
            handleGoToQuestionnaireButtonClick,
            isBusinessCreditLimitOffer,
            productInfo,
        ],
    );

    const handleSubscriptionClick = useCallback(
        (event: React.MouseEvent) => {
            handleDetailedButtonClick(event, true);
        },
        [handleDetailedButtonClick],
    );

    useEffect(() => {
        if (trackAlfaMetrics) {
            if (isBusinessCreditLimitOffer) {
                trackAlfaMetrics(CREDITSB_METRICS.showCreditLimitBanner, {
                    deviceScreenName: 'Banner PA',
                });
            }

            trackAlfaMetrics(
                CREDIT_OFFERS_METRICS[
                    preApproved ? 'showPreApprovedCreditOfferPane' : 'showStandardCreditOfferPane'
                ],
                {
                    type,
                    campaignCode,
                    productCode,
                    campaignCodeCommon: campaignCode,
                    productIdCommon: type,
                },
            );
        }
    }, [
        campaignCode,
        isBusinessCreditLimitOffer,
        preApproved,
        productCode,
        trackAlfaMetrics,
        type,
    ]);

    const subtitle = isBusinessCreditCard
        ? `Предложение по${NBSP}карте ${LAQUO}Альфа - Бизнес${NBSP}Кредит${RAQUO}`
        : productInfo?.text;

    return (
        <CreditOfferPlate
            view={preApproved && isTotalOffer ? 'positive' : 'default'}
            onClick={handlePaneClick}
        >
            <div className={creditOfferItemCn('heading-section')}>
                <div className={creditOfferItemCn('heading-content')}>
                    <CreditOfferItemBadge
                        offerType={type}
                        preApproved={preApproved}
                        campaignCode={campaignCode}
                        currentTime={currentTime}
                        earlierExpiryDate={earlierExpiryDate}
                    />

                    <TitleView view={VIEWS.MEDIUM} tag='h3' subtitle={subtitle}>
                        {title}
                    </TitleView>
                </div>

                <Link
                    view='secondary'
                    className={creditOfferItemCn('heading-link', {
                        'credit-limit': isBusinessCreditLimitOffer,
                    })}
                    onClick={handleLinkClick}
                    pseudo={isBusinessCreditLimitOffer}
                >
                    {isBusinessCreditLimitOffer
                        ? `Хочу${NBSP}другие${NBSP} условия`
                        : `Все${NBSP}условия${NBSP}и${NBSP} лимиты`}
                </Link>
            </div>

            <div className={creditOfferItemCn('offers-section')}>
                <CreditOfferItemRows
                    productInfo={productInfo}
                    isCommonCheckBlockedOverdraft={isCommonCheckBlockedOverdraft}
                    onGoToQuestionnaireButtonClick={handleGoToQuestionnaireButtonClick}
                    onDetailedButtonClick={handleDetailedButtonClick}
                />
            </div>
            {isBusinessCreditLimitOffer ? (
                <Link
                    view='secondary'
                    className={creditOfferItemCn('heading-link', {
                        'credit-limit-mobile': true,
                    })}
                    onClick={handleLinkClick}
                    pseudo={isBusinessCreditLimitOffer}
                >
                    {`Хочу${NBSP}другие${NBSP} условия`}
                </Link>
            ) : null}
            {isSubscriptionAvailable && (
                <Typography.Text
                    view='primary-small'
                    className={creditOfferItemCn('subscription-section')}
                >
                    Ставка снизится, если подключить подписку при оформлении.{NBSP}
                    <Link view='secondary' onClick={handleSubscriptionClick} pseudo={true}>
                        Как работает подписка
                    </Link>
                </Typography.Text>
            )}
        </CreditOfferPlate>
    );
};
