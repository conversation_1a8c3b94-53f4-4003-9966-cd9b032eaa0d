import React from 'react';
import { createCn } from 'bem-react-classname';

import { Typography } from '@alfalab/core-components/typography';
import SkeletonLabel from 'arui-private/skeleton-label';

import './signed-credit-documents-skeleton.css';

const cn = createCn('signed-credit-documents-skeleton');

const SignedCreditDocumentsSkeleton: React.FC = () => (
    <div className={cn()}>
        <Typography.Title
            className={cn('title')}
            view='xsmall'
            weight='bold'
            tag='div'
            font='system'
        >
            Кредитная документация
        </Typography.Title>
        {Array.from(Array(3).keys()).map((key) => (
            <div className={cn('skeleton-row')} key={key}>
                <SkeletonLabel size='xl' />
            </div>
        ))}
    </div>
);

export default SignedCreditDocumentsSkeleton;
