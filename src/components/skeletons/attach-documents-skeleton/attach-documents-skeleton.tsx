import * as React from 'react';
import { createCn } from 'bem-react-classname';

import SkeletonLabel from 'arui-private/skeleton-label';

import './attach-documents-skeleton.css';

const cn = createCn('attach-documents-skeleton');

const AttachDocumentsSkeleton: React.FC = () => (
    <div className={cn()}>
        <div className={cn('title')}>
            <SkeletonLabel size='l' />
        </div>
        <div className={cn('subtitle')}>
            <SkeletonLabel size='s' />
        </div>
        <div className={cn('description')}>
            <SkeletonLabel size='3xl' />
        </div>
        <div className={cn('info')}>
            <SkeletonLabel size='s' />
            <SkeletonLabel size='s' />
            <SkeletonLabel size='s' />
        </div>
        <div className={cn('title', { half: true })}>
            <SkeletonLabel size='l' />
        </div>
        <div className={cn('description')}>
            <SkeletonLabel size='3xl' />
        </div>
        <div className={cn('button')}>
            <SkeletonLabel size='2xl' />
        </div>
        <div className={cn('button')}>
            <SkeletonLabel size='2xl' />
        </div>
    </div>
);

export default AttachDocumentsSkeleton;
