import * as React from 'react';
import { createCn } from 'bem-react-classname';

import SkeletonLabel from 'arui-private/skeleton-label';

import './credit-holidays-form-skeleton.css';

const cn = createCn('credit-holidays-form-skeleton');

const CreditHolidaysFormSkeleton: React.FC = () => (
    <div className={cn()}>
        <div className={cn('title')}>
            <SkeletonLabel size='l' />
        </div>
        <div className={cn('row')}>
            <div className={cn('item')}>
                <SkeletonLabel size='2xl' />
            </div>
            <div className={cn('item')}>
                <SkeletonLabel size='2xl' />
            </div>
        </div>
        <div className={cn('row')}>
            <div className={cn('item')}>
                <SkeletonLabel size='2xl' />
            </div>
        </div>
        <div className={cn('row')}>
            <div className={cn('item', { block: true })}>
                <SkeletonLabel size='3xl' />
            </div>
        </div>
        <div className={cn('title')}>
            <SkeletonLabel size='l' />
        </div>
        <div className={cn('row')}>
            <div className={cn('item')}>
                <SkeletonLabel size='2xl' />
            </div>
        </div>
        <div className={cn('row')}>
            <div className={cn('item')}>
                <SkeletonLabel size='2xl' />
            </div>
            <div className={cn('item')}>
                <SkeletonLabel size='2xl' />
            </div>
        </div>
        <div className={cn('row')}>
            <div className={cn('item')}>
                <SkeletonLabel size='2xl' />
            </div>
            <div className={cn('item')}>
                <SkeletonLabel size='2xl' />
            </div>
        </div>
        <div className={cn('title')}>
            <SkeletonLabel size='l' />
        </div>
        <div className={cn('row')}>
            <div className={cn('item')}>
                <SkeletonLabel size='3xl' />
            </div>
        </div>
        <div className={cn('row')}>
            <div className={cn('item', { block: true })}>
                <SkeletonLabel size='3xl' />
            </div>
        </div>

        <div className={cn('button')}>
            <SkeletonLabel size='3xl' />
        </div>
    </div>
);

export default CreditHolidaysFormSkeleton;
