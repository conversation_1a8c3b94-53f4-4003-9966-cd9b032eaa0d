import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type Requisites } from 'corp-core-credit-products-api-typescript-services';
import { type Amount } from 'thrift-services/entities';

import { Collapse } from '@alfalab/core-components/collapse';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Plate } from '@alfalab/core-components/plate';
import { StatusBadge } from '@alfalab/core-components/status-badge';
import { NBSP } from 'arui-private/lib/formatters';

import { OverdraftNetTurnoverPaneContent } from '#/src/components/overdraft-net-turnover-pane-content';
import { OverdraftNetTurnoverPaneHeader } from '#/src/components/overdraft-net-turnover-pane-header';
import { getOverdraftNetTurnoverStart } from '#/src/ducks/credit-products/actions';
import { parentOverdraftNetTurnoverSelector } from '#/src/ducks/credit-products/selectors/overdraft.selectors';
import { isOverdraftNetTurnsFeatureSelector } from '#/src/ducks/organization/selectors';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { OverdraftNetTurnoverButton } from '#/src/mobile/components/overdraft-net-turnover-button';
import { dateToCustomFormat } from '#/src/utils/date';
import { convertAmountToString } from '#/src/utils/number-helpers';

import './overdraft-net-turnover-pane.css';

const cn = createCn('overdraft-net-turnover-pane');

type TProps = {
    overdraftLimit?: Amount;
    docNumber?: string;
    requisites?: Requisites;
};

const TURNOVER_ALERT_FROM_DAY = 20;

export const OverdraftNetTurnoverPane: React.FC<TProps> = ({
    overdraftLimit,
    docNumber,
    requisites,
}) => {
    const dispatch = useDispatch();
    const turnover = useSelector(parentOverdraftNetTurnoverSelector);
    const isTurnoverFeatureActive = useSelector(isOverdraftNetTurnsFeatureSelector);
    const currentDate = useSelector(currentTimeSelector);
    const [expanded, setExpanded] = useState(false);

    const [isMobile] = useMatchMedia('--mobile');

    const isNeedToLoadTurnover = useMemo(
        () =>
            (!turnover || (!turnover.pending && !turnover.loaded && !turnover.error)) &&
            isTurnoverFeatureActive,
        [turnover, isTurnoverFeatureActive],
    );

    useEffect(() => {
        if (isNeedToLoadTurnover && docNumber && requisites?.account) {
            dispatch(getOverdraftNetTurnoverStart(docNumber, requisites.account));
        }
    }, [isNeedToLoadTurnover, docNumber, requisites]);

    const maxAmount: Amount = useMemo(
        () => ({
            amount: (turnover?.turnoverData?.maintainTurn ?? 0) * 100,
            currency: {
                code: 810,
                fullName: 'Российский рубль',
                minorUnits: 100,
                mnemonicCode: 'RUR',
                unicodeSymbol: '₽',
            },
        }),
        [turnover],
    );

    const neededAmount: Amount = useMemo(
        () => ({
            ...maxAmount,
            amount: Math.max(
                Math.round(
                    ((maxAmount?.amount ?? 0) -
                        (turnover?.turnoverData?.actualNetTurn ?? 0) * 100) /
                        100,
                ) * 100,
                0,
            ),
        }),
        [turnover, maxAmount],
    );

    const isTurnoverWarning = useMemo(
        () =>
            currentDate.getDate() >= TURNOVER_ALERT_FROM_DAY &&
            (turnover?.turnoverData?.actualNetTurn ?? 0) <
                (turnover?.turnoverData?.maintainTurn ?? 0),
        [turnover, currentDate],
    );

    const isTurnoverNegative = useMemo(
        () => overdraftLimit?.amount === 0 && !isTurnoverWarning && turnover?.loaded,
        [isTurnoverWarning, overdraftLimit, turnover],
    );

    const toggleExpanded = useCallback(() => {
        setExpanded((prev) => !prev);
    }, []);

    if (turnover?.error || (turnover?.loaded && !turnover?.turnoverData)) {
        return null;
    }

    if (isMobile) {
        return (
            <OverdraftNetTurnoverButton
                negative={isTurnoverNegative}
                turnoverData={turnover?.turnoverData}
                pending={turnover?.pending}
                overdraftLimit={overdraftLimit}
                currentDate={currentDate}
                maxAmount={maxAmount}
                neededAmount={neededAmount}
                warning={isTurnoverWarning}
            />
        );
    }

    return (
        <div className={cn()}>
            <OverdraftNetTurnoverPaneHeader
                expanded={expanded}
                maxAmount={maxAmount}
                toggleExpanded={toggleExpanded}
                paddingBottomXl={isTurnoverWarning || isTurnoverNegative || expanded}
            />
            {isTurnoverWarning && !expanded && (
                <div className={cn('alert')} onClick={toggleExpanded}>
                    <Plate
                        view='attention'
                        titleView='light'
                        rounded={false}
                        leftAddons={<StatusBadge view='attention-alert' />}
                        title={
                            <React.Fragment>
                                Нужно ещё {convertAmountToString(neededAmount, true, true)}
                                {turnover?.turnoverData?.asOfDay &&
                                    ` до${NBSP}конца ${dateToCustomFormat(
                                        currentDate,
                                        turnover?.turnoverData?.asOfDay,
                                        'MMMM',
                                    )}`}
                            </React.Fragment>
                        }
                    />
                </div>
            )}
            {isTurnoverNegative && !expanded && (
                <div className={cn('alert')} onClick={toggleExpanded}>
                    <Plate
                        view='negative'
                        titleView='light'
                        rounded={false}
                        leftAddons={<StatusBadge view='negative-alert' />}
                        title='Лимит заблокирован'
                    />
                </div>
            )}
            <Collapse expanded={expanded}>
                <OverdraftNetTurnoverPaneContent
                    turnoverData={turnover?.turnoverData}
                    pending={turnover?.pending}
                    overdraftLimit={overdraftLimit}
                    currentDate={currentDate}
                    maxAmount={maxAmount}
                    neededAmount={neededAmount}
                    warning={isTurnoverWarning}
                />
            </Collapse>
        </div>
    );
};
