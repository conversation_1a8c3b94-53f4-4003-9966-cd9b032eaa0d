import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@alfalab/core-components/button';
import { Modal } from '@alfalab/core-components/modal';
import { Typography } from '@alfalab/core-components/typography';

import { setMangerWorksWithAppModalVisible } from '#/src/ducks/credit-processing/actions';
import { isManagerWorksModalVisible } from '#/src/ducks/credit-processing/selectors';

export const ManagerWorksWithAppModal: React.FC = () => {
    const dispatch = useDispatch();

    const isModalVisible = useSelector(isManagerWorksModalVisible);

    const handleCloseModal = useCallback(() => {
        dispatch(setMangerWorksWithAppModalVisible(false));
    }, [dispatch]);

    return (
        <Modal open={isModalVisible} hasCloser={true} onClose={handleCloseModal} size='s'>
            <Modal.Header>С заявкой работает менеджер</Modal.Header>
            <Modal.Content>
                <Typography.Text tag='div'>
                    Подписать документы онлайн не получится,
                </Typography.Text>
                <Typography.Text tag='div'>дождитесь звонка менеджера</Typography.Text>
            </Modal.Content>
            <Modal.Footer>
                <Button size='s' view='secondary' onClick={handleCloseModal}>
                    Понятно
                </Button>
            </Modal.Footer>
        </Modal>
    );
};
