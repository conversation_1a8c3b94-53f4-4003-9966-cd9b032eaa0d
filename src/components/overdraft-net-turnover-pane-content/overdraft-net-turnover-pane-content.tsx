import React, { useMemo } from 'react';
import { createCn } from 'bem-react-classname';
import { type Amount } from 'thrift-services/entities';
import { type TTurnoverData } from 'thrift-services/services/credit_products';

import { Grid } from '@alfalab/core-components/grid';
import { Link } from '@alfalab/core-components/link';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { StatusBadge } from '@alfalab/core-components/status-badge';
import { Typography } from '@alfalab/core-components/typography';
import { NBSP } from 'arui-private/lib/formatters';

import AmountPure from '#/src/components/ui/amount-pure';
import { TurnoverProgress } from '#/src/components/ui/turnover-progress';
import OVERDRAFT_LIMIT from '#/src/static-files/overdraft/overdraft-limit-alfabank.pdf';

import './overdraft-net-turnover-pane-content.css';

const cn = createCn('overdraft-net-turnover-pane-content');

export type TTurnoverProps = {
    pending: boolean;
    warning: boolean;
    overdraftLimit?: Amount;
    neededAmount: Amount;
    maxAmount: Amount;
    turnoverData?: TTurnoverData;
    currentDate: Date;
};

export const OverdraftNetTurnoverPaneContent: React.FC<TTurnoverProps> = ({
    turnoverData,
    pending,
    overdraftLimit,
    currentDate,
    neededAmount,
    maxAmount,
    warning,
}) => {
    const currentAmount = useMemo(
        () => ({
            amount: (turnoverData?.actualNetTurn ?? 0) * 100,
            currency: {
                code: 810,
                fullName: 'Российский рубль',
                minorUnits: 100,
                mnemonicCode: 'RUR',
                unicodeSymbol: '₽',
            },
        }),
        [turnoverData],
    );

    return (
        <Grid.Row className={cn()} gutter={24}>
            <Grid.Col width={{ desktop: 6, mobile: { xs: 12 } }} className={cn('content-col')}>
                <Skeleton visible={pending} animate={true}>
                    <TurnoverProgress
                        currentAmount={currentAmount}
                        maxAmount={maxAmount}
                        asOfDay={turnoverData?.asOfDay}
                        neededAmount={neededAmount}
                        currentDate={currentDate}
                        warning={warning}
                    />
                </Skeleton>
            </Grid.Col>
            <Grid.Col width={{ desktop: 6, mobile: { xs: 12 } }} className={cn('content-col')}>
                <Typography.Text
                    view='primary-large'
                    weight='medium'
                    tag='div'
                    className={cn('current-limit', {
                        negative: overdraftLimit?.amount === 0,
                    })}
                >
                    Ваш лимит{NBSP}
                    <AmountPure value={overdraftLimit} view='default' transparentMinor={false} />
                    {overdraftLimit?.amount === 0 && (
                        <StatusBadge
                            view='negative-alert'
                            size={16}
                            className={cn('current-limit-alert')}
                        />
                    )}
                </Typography.Text>
                <Typography.Text view='primary-medium' tag='div' className={cn('limit-guide')}>
                    Пересматривается раз в{NBSP}месяц. Чтобы сохранить или{NBSP}восстановить его,
                    поддерживайте обороты
                </Typography.Text>
                <Link href={OVERDRAFT_LIMIT} target='_blank' className={cn('details-link')}>
                    <Typography.Text view='primary-medium' weight='medium'>
                        Что ещё влияет на лимит
                    </Typography.Text>
                </Link>
                {/* INFO: Потребуется в следующих итерациях */}
                {/* <Space direction={'vertical'} className={cn('file-list')} fullWidth={true}> */}
                {/*    <div className={cn('file-list-item')}> */}
                {/*        <Space */}
                {/*            size={12} */}
                {/*            direction={'horizontal'} */}
                {/*            className={cn('file-content')} */}
                {/*        > */}
                {/*            <DocumentExcelMIcon /> */}
                {/*            <Link href={'#'}> */}
                {/*                <Typography.Text view={'primary-small'}> */}
                {/*                    Выписка чистого кредитного оборота.xls */}
                {/*                </Typography.Text> */}
                {/*            </Link> */}
                {/*        </Space> */}
                {/*        <Space size={12} direction={'horizontal'}> */}
                {/*            <Typography.Text view={'primary-small'}>1 МБ</Typography.Text> */}
                {/*            <Link href={'#'}> */}
                {/*                <DownloadMBlackIcon /> */}
                {/*            </Link> */}
                {/*        </Space> */}
                {/*    </div> */}
                {/* </Space> */}
            </Grid.Col>
        </Grid.Row>
    );
};
