@import '@alfalab/core-components/vars';

.overdraft-net-turnover-pane-content {
    padding: 0;
    margin-top: var(--gap-24);

    @media (--tablet-s) {
        padding: 0 var(--gap-32) var(--gap-32);
        margin-top: 0;
    }

    &__content-col {
        &:not(:first-of-type) {
            margin-top: var(--gap-16);

            @media (--desktop-s) {
                margin-top: 0;
            }
        }
    }

    &__current-limit {
        margin-bottom: var(--gap-8);

        &_negative {
            display: flex;
            align-items: center;
            color: var(--color-static-text-negative);
        }
    }

    &__current-limit-alert {
        margin-left: 6px;
    }

    &__limit-guide {
        color: var(--color-light-text-secondary);
    }

    &__details-link {
        display: inline-block;
        margin-top: var(--gap-16);
    }

    &__file-list {
        margin-top: var(--gap-16);

        &-item {
            display: flex;
            align-items: center;
        }
    }

    &__file-content {
        flex: 1;
    }
}
