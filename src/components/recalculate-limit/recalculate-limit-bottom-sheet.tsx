import React from 'react';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Gap } from '@alfalab/core-components/gap';

import { RecalculateLimitButtons } from '#/src/components/recalculate-limit/recalculate-limit-buttons';

import { type RecalculateLimitProps } from './recalculate-limit';
import { RecalculateLimitContent } from './recalculate-limit-content';

type RecalculateLimitBottomSheetProps = RecalculateLimitProps & {
    onClick: () => void;
    isDesktop: boolean;
    title: string;
};

export const RecalculateLimitBottomSheet = ({
    open,
    handleClose,
    onClick,
    isDesktop,
    title,
}: RecalculateLimitBottomSheetProps) => (
    <BottomSheet title={title} open={open} hasCloser={true} onClose={handleClose}>
        <Gap size='xs' />
        <RecalculateLimitContent />
        <Gap size='xl' />
        <RecalculateLimitButtons
            buttonsSize='m'
            isDesktop={isDesktop}
            onClick={onClick}
            handleClose={handleClose}
            isBlock={true}
        />
    </BottomSheet>
);
