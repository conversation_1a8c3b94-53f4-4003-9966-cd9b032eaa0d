import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useMatchMedia } from '@alfalab/core-components/mq';

import { BUTTON_MARKER_PARAM_KEY, ButtonMarkers } from '#/src/constants/button-markers';
import { initExternalRedirect } from '#/src/ducks/app/actions';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { externalRedirectCreditSBSelector } from '#/src/ducks/settings/selectors';

import { RecalculateLimitBottomSheet } from './recalculate-limit-bottom-sheet';
import { RecalculateLimitModal } from './recalculate-limit-modal';

export type RecalculateLimitProps = {
    open: boolean;
    handleClose: () => void;
};

export const RecalculateLimit = ({ open, handleClose }: RecalculateLimitProps) => {
    const [isDesktop] = useMatchMedia('--desktop');
    const dispatch = useDispatch();
    const externalRedirectCreditSB = useSelector(externalRedirectCreditSBSelector);
    const organizationId = useSelector(currentHeaderOrganizationEqIdSelector);

    const handleClick = () => {
        dispatch(
            initExternalRedirect({
                link: externalRedirectCreditSB,
                addContextRoot: false,
                organizationId,
                parameters: {
                    [BUTTON_MARKER_PARAM_KEY]: ButtonMarkers.BUTTON_2,
                },
            }),
        );
    };

    if (isDesktop) {
        return (
            <RecalculateLimitModal
                title='Пересчитать лимит?'
                onClick={handleClick}
                open={open}
                handleClose={handleClose}
            />
        );
    }

    return (
        <RecalculateLimitBottomSheet
            title='Пересчитать лимит?'
            isDesktop={isDesktop}
            onClick={handleClick}
            open={open}
            handleClose={handleClose}
        />
    );
};
