import React from 'react';

import { Gap } from '@alfalab/core-components/gap';
import { Modal } from '@alfalab/core-components/modal';
import { Typography } from '@alfalab/core-components/typography';

import { RecalculateLimitButtons } from '#/src/components/recalculate-limit/recalculate-limit-buttons';

import { type RecalculateLimitProps } from './recalculate-limit';
import { RecalculateLimitContent } from './recalculate-limit-content';

type RecalculateLimitModalProps = RecalculateLimitProps & {
    onClick: () => void;
    title: string;
};

export const RecalculateLimitModal = ({
    open,
    handleClose,
    onClick,
    title,
}: RecalculateLimitModalProps) => (
    <Modal open={open} hasCloser={true} onClose={handleClose} size='m'>
        <Modal.Header>
            <Typography.Title tag='div' view='small' font='system'>
                {title}
            </Typography.Title>
        </Modal.Header>
        <Gap size='xs' />
        <Modal.Content>
            <RecalculateLimitContent />
        </Modal.Content>
        <Modal.Footer>
            <RecalculateLimitButtons handleClose={handleClose} buttonsSize='s' onClick={onClick} />
        </Modal.Footer>
    </Modal>
);
