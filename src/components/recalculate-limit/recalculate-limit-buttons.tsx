import React from 'react';

import { Button } from '@alfalab/core-components/button';
import { type ButtonProps } from '@alfalab/core-components/button/typings';
import { Gap } from '@alfalab/core-components/gap';

type RecalculateLimitButtonsProps = {
    isBlock?: boolean;
    onClick: () => void;
    handleClose: () => void;
    isDesktop?: boolean;
    buttonsSize: ButtonProps['size'];
};

export const RecalculateLimitButtons = ({
    isBlock,
    onClick,
    isDesktop,
    buttonsSize,
    handleClose,
}: RecalculateLimitButtonsProps) => (
    <React.Fragment>
        <Button view='primary' size={buttonsSize} block={isBlock} onClick={onClick}>
            Пересчитать
        </Button>
        {!isDesktop && <Gap size='m' />}
        <Button view='secondary' size={buttonsSize} block={isBlock} onClick={handleClose}>
            Отменить
        </Button>
    </React.Fragment>
);
