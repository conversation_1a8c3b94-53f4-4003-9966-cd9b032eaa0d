import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { Modal } from '@alfalab/core-components/modal';
import { Typography } from '@alfalab/core-components/typography';
import connectedAnimationJson from 'arui-private/lottie-animation/animations/generation-grey-connected.json';
import { LottieAnimation } from 'arui-private/lottie-animation/lottie-animation';

import { setCallbackRequestCreatedModalVisible } from '#/src/ducks/credit-processing/actions';
import {
    callbackRequestStatusSelector,
    isCallbackRequestCreatedModalVisibleSelector,
} from '#/src/ducks/credit-processing/selectors';
import { CallbackRequestStatusTypes } from '#/src/ducks/credit-processing/types';

import './callback-request-created-modal.css';

const cn = createCn('callback-request-created-modal');

export const CallbackRequestCreatedModal: React.FC = () => {
    const dispatch = useDispatch();

    const isModalVisible = useSelector(isCallbackRequestCreatedModalVisibleSelector);
    const callbackRequestStatus = useSelector(callbackRequestStatusSelector);

    const callbackRequestHasAlreadyCreated =
        callbackRequestStatus === CallbackRequestStatusTypes.CALLBACK_REQUEST_HAS_ALREADY_CREATED;

    const handleCloseModal = () => {
        dispatch(setCallbackRequestCreatedModalVisible(false));
    };

    return (
        <Modal
            open={isModalVisible}
            hasCloser={true}
            onClose={handleCloseModal}
            className={cn()}
            size='l'
        >
            <Modal.Header className={cn('header')} />
            <Modal.Content className={cn('content')}>
                <LottieAnimation
                    className={cn('status-icon')}
                    animationData={connectedAnimationJson}
                />
                <Typography.Title
                    view='small'
                    tag='h4'
                    font='system'
                    color='primary'
                    className={cn('title')}
                >
                    {callbackRequestHasAlreadyCreated
                        ? 'Заявка уже отправлена в банк. Дождитесь когда с вами свяжется менеджер'
                        : 'Заявка отправлена в банк, с вами свяжется менеджер'}
                </Typography.Title>
                <Typography.Text color='primary' className={cn('description')}>
                    {callbackRequestHasAlreadyCreated
                        ? 'Менеджер позвонит в течение рабочего дня'
                        : 'Звонок поступит в течение одного рабочего дня'}
                </Typography.Text>
            </Modal.Content>
        </Modal>
    );
};
