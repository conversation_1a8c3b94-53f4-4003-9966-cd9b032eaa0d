import * as React from 'react';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { Typography } from '@alfalab/core-components/typography';
import CorporateAmount from 'arui-private/corporate-amount';

import { DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';
import PaneGroup from '../ui/pane-group';
import PaneItem from '../ui/pane-item';

import { cn } from './credit-schedule';

import './credit-schedule.css';

type TProps = {
    payment: TLoanPayment;
    currentTime: Date;
};

export const CreditSchedulePaymentMobileRow: React.FC<TProps> = ({
    payment: { paymentDate, paymentAmount, paymentLoan, paymentInterest, loanBalance },
    currentTime,
}) => (
    <div>
        <div className={cn('pane-group-header')}>
            {!!paymentDate && (
                <Typography.Text view='primary-small'>
                    {dateToCustomFormat(currentTime, paymentDate, DATE_FORMAT)}
                </Typography.Text>
            )}
            {!!paymentAmount && (
                <Typography.Text view='primary-small' weight='bold'>
                    <CorporateAmount amount={paymentAmount} transparentMinor={false} />
                </Typography.Text>
            )}
        </div>
        <PaneGroup withoutBorder={true} colWidth={12} className={cn('pane-group')}>
            {!!paymentLoan && (
                <PaneItem
                    title='Основной долг'
                    titleSize='secondary-medium'
                    titleColor='secondary'
                    text={<CorporateAmount amount={paymentLoan} transparentMinor={false} />}
                    textSize='primary-small'
                    textWeight='regular'
                />
            )}
            {!!paymentInterest && (
                <PaneItem
                    title='Проценты'
                    titleSize='secondary-medium'
                    titleColor='secondary'
                    text={<CorporateAmount amount={paymentInterest} transparentMinor={false} />}
                    textSize='primary-small'
                    textWeight='regular'
                />
            )}
            {!!loanBalance && (
                <PaneItem
                    title='Остаток долга'
                    titleSize='secondary-medium'
                    titleColor='secondary'
                    text={<CorporateAmount amount={loanBalance} transparentMinor={false} />}
                    textSize='primary-small'
                    textWeight='regular'
                />
            )}
        </PaneGroup>
    </div>
);
