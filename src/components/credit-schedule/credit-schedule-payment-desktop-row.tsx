import * as React from 'react';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { Table } from '@alfalab/core-components/table';
import { Typography } from '@alfalab/core-components/typography';
import CorporateAmount from 'arui-private/corporate-amount';

import { DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';

import './credit-schedule.css';

type TProps = {
    payment: TLoanPayment;
    index: number;
    currentTime: Date;
};

export const CreditSchedulePaymentDesktopRow: React.FC<TProps> = ({
    payment: { paymentDate, paymentAmount, paymentLoan, paymentInterest, loanBalance },
    currentTime,
    index,
}) => (
    <Table.TRow withoutBorder={index === 0}>
        <Table.TCell>
            {!!paymentDate && (
                <Typography.Text view='primary-medium' color='primary'>
                    {dateToCustomFormat(currentTime, paymentDate, DATE_FORMAT)}
                </Typography.Text>
            )}
        </Table.TCell>
        <Table.TCell>{!!paymentAmount && <CorporateAmount amount={paymentAmount} />}</Table.TCell>
        <Table.TCell>{!!paymentLoan && <CorporateAmount amount={paymentLoan} />}</Table.TCell>
        <Table.TCell>
            {!!paymentInterest && <CorporateAmount amount={paymentInterest} />}
        </Table.TCell>
        <Table.TCell>{!!loanBalance && <CorporateAmount amount={loanBalance} />}</Table.TCell>
    </Table.TRow>
);
