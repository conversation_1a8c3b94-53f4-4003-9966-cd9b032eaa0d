import React, { useCallback } from 'react';
import { connect } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type ReportFormat } from 'thrift-services/entities';
import { type TLoanPayment } from 'thrift-services/services/credit_products';
import { type UnixEpoch } from 'thrift-services/utils';

import SkeletonLabel from 'arui-private/skeleton-label';

import { type ApplicationState } from '#/src/ducks/application-state';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import { CREDIT_IN_USE_METRICS } from '../../metrics';
import CreditScheduleRow from '../credit-schedule-row';
import PaymentSchedule from '../payment-schedule';
import NotifyPlate from '../ui/notify-plate';
import ScheduleCommonHeading from '../ui/schedule-common-heading';

import { CreditSchedulePayment } from './credit-schedule-payment';

import './credit-schedule.css';

export const cn = createCn('credit-schedule');

type TOwnProps = {
    payedPayments: TLoanPayment[];
    futurePayments: TLoanPayment[];
    isFuturePaymentsBlocked?: boolean;
    isNotReadyScheduleOfPromoDeal?: boolean;
    docNumber: string;
    todayDate?: UnixEpoch | null;
    onDownloadButtonClick: (fileType?: ReportFormat) => void;
    onTogglePopup?: (popupName?: string) => void;
    isFetching: boolean;
};

function mapStateToProps(state: ApplicationState) {
    return {
        currentTime: currentTimeSelector(state),
    };
}

type TStateProps = ReturnType<typeof mapStateToProps>;
type TProps = TOwnProps & TStateProps;

const tableHeaders = [
    'Дата платежа',
    'Сумма платежа',
    'Основной долг',
    'Проценты',
    'Остаток долга',
];

const CreditSchedule: React.FC<TProps> = ({
    isFetching,
    futurePayments,
    payedPayments,
    todayDate,
    isFuturePaymentsBlocked,
    isNotReadyScheduleOfPromoDeal,
    docNumber,
    currentTime,
    onTogglePopup,
    onDownloadButtonClick,
}) => {
    const trackAlfaMetrics = useTrackAlfaMetrics();

    const handleDownloadScheduleButtonClick = useCallback(
        (fileType?: ReportFormat) => {
            if (fileType) {
                trackAlfaMetrics(CREDIT_IN_USE_METRICS.downloadScheduleButtonClick, {
                    agreementNumber: docNumber,
                    fileType,
                });

                onDownloadButtonClick(fileType);
            }
        },
        [docNumber, onDownloadButtonClick, trackAlfaMetrics],
    );

    const renderSkeleton = () =>
        Array.from(Array(10).keys()).map((key) => (
            <CreditScheduleRow
                key={key}
                first={
                    <div className={cn('skeleton-row')}>
                        <SkeletonLabel size='xl' />
                    </div>
                }
                second={
                    <div className={cn('skeleton-row')}>
                        <SkeletonLabel size='xl' />
                    </div>
                }
                third={
                    <div className={cn('skeleton-row')}>
                        <SkeletonLabel size='xl' />
                    </div>
                }
                fourth={
                    <div className={cn('skeleton-row')}>
                        <SkeletonLabel size='xl' />
                    </div>
                }
                fifth={
                    <div className={cn('skeleton-row')}>
                        <SkeletonLabel size='xl' />
                    </div>
                }
            />
        ));

    if (isNotReadyScheduleOfPromoDeal) {
        return (
            <NotifyPlate
                title='Вам не нужно платить за кредит первые 3 месяца'
                text='График платежей появится с четвёртого месяца пользования кредитом.'
                className={cn('notify-plate')}
            />
        );
    }

    if (isFuturePaymentsBlocked) {
        return (
            <React.Fragment>
                <NotifyPlate
                    title='Ведутся технические работы'
                    text='Обновленные графики платежей будут доступны до окончания льготного периода.'
                    className={cn('notify-plate')}
                />
                <PaymentSchedule
                    todayDate={todayDate}
                    payedPayments={
                        <CreditSchedulePayment payments={payedPayments} currentTime={currentTime} />
                    }
                    skeleton={renderSkeleton()}
                    tableHeaders={tableHeaders}
                    isFetching={isFetching}
                />
            </React.Fragment>
        );
    }

    return (
        <PaymentSchedule
            heading={
                <ScheduleCommonHeading
                    paymentsCount={payedPayments.length + futurePayments.length}
                    onTogglePopup={onTogglePopup}
                    isCanBeDownloaded={true}
                    onDownloadButtonClick={handleDownloadScheduleButtonClick}
                />
            }
            todayDate={todayDate}
            futurePayments={
                <CreditSchedulePayment payments={futurePayments} currentTime={currentTime} />
            }
            payedPayments={
                <CreditSchedulePayment payments={payedPayments} currentTime={currentTime} />
            }
            skeleton={renderSkeleton()}
            tableHeaders={tableHeaders}
            isFetching={isFetching}
        />
    );
};

export default connect(mapStateToProps)(CreditSchedule);
