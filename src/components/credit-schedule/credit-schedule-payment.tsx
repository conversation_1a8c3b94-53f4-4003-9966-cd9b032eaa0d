import * as React from 'react';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { Divider } from '@alfalab/core-components/divider';
import { useMatchMedia } from '@alfalab/core-components/mq';

import { CreditSchedulePaymentDesktopRow } from './credit-schedule-payment-desktop-row';
import { CreditSchedulePaymentMobileRow } from './credit-schedule-payment-mobile-row';

import './credit-schedule.css';

export type TCreditSchedulePaymentProps = {
    payments: TLoanPayment[];
    currentTime: Date;
};

export const CreditSchedulePayment: React.FC<TCreditSchedulePaymentProps> = ({
    payments,
    currentTime,
}) => {
    const [isTablet] = useMatchMedia('--tablet-m');

    if (!payments.length) return null;

    return (
        <React.Fragment>
            {payments.map((payment, index) =>
                isTablet ? (
                    <CreditSchedulePaymentDesktopRow
                        key={`credit-schedule-payment__${index}`}
                        payment={payment}
                        index={index}
                        currentTime={currentTime}
                    />
                ) : (
                    <React.Fragment key={`credit-schedule-payment__${index}`}>
                        <CreditSchedulePaymentMobileRow
                            payment={payment}
                            currentTime={currentTime}
                        />
                        {index !== payments.length - 1 && <Divider />}
                    </React.Fragment>
                ),
            )}
        </React.Fragment>
    );
};
