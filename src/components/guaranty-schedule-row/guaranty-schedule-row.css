@import '@alfalab/core-components/vars';

.guaranty-schedule-row {
    display: flex;
    justify-content: space-between;
    padding: var(--gap-12) var(--gap-8) var(--gap-12);
    width: 50%;
    border-radius: var(--border-radius-8);

    @media (--small-only) {
        width: 100%;
    }

    &__cell {
        display: flex;
        flex-basis: 100%;
        justify-content: flex-start;
        margin-right: var(--gap-12);

        @mixin paragraph_primary_medium;

        &:last-of-type {
            margin-right: 0;
        }

        &_second {
            justify-content: flex-end;
        }
    }

    &_animated {
        &:hover {
            background-color: var(--color-dark-indigo-05);
        }
    }
}
