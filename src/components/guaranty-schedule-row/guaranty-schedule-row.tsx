import * as React from 'react';
import { createCn } from 'bem-react-classname';

import './guaranty-schedule-row.css';

type TOwnProps = {
    first: React.ReactNode | string;
    second: React.ReactNode | string;
    withHover?: boolean;
};

const cn = createCn('guaranty-schedule-row');

const GuarantyScheduleRow: React.FC<TOwnProps> = ({ first, second, withHover = false }) => (
    <div className={cn({ animated: withHover })}>
        <div className={cn('cell')}>{first}</div>
        <div className={cn('cell', { second: true })}>{second}</div>
    </div>
);

export default GuarantyScheduleRow;
