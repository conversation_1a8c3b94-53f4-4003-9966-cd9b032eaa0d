import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';

import { Button } from '@alfalab/core-components/button';
import connectedSvg from 'arui-private/icons/connected.svg';

import StatusPageSkeleton from '#/src/components/ui/status-page-skeleton';
import { goToHome } from '#/src/ducks/app/actions';

const DocumentsSentPage: React.FC = () => {
    const dispatch = useDispatch();
    const goToHomePage = useCallback(() => {
        dispatch(goToHome());
    }, [dispatch]);

    return (
        <StatusPageSkeleton
            icon={<img src={connectedSvg} width='320' height='240' alt='Отправлено' />}
            title='Документы отправлены'
            description='После рассмотрения документов с вами свяжется кредитный аналитик'
            actionButton={
                <Button onClick={goToHomePage} size='s' view='tertiary'>
                    Перейти на главную
                </Button>
            }
        />
    );
};

export default DocumentsSentPage;
