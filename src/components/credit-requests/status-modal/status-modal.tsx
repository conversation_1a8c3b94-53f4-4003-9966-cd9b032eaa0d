import React from 'react';
import { createCn } from 'bem-react-classname';
import { CommonCreditRequestType } from 'corp-credit-request-api-typescript-services';

import { Modal } from '@alfalab/core-components/modal';
import { Status } from '@alfalab/core-components/status';
import { Typography } from '@alfalab/core-components/typography';
import { NBSP } from 'arui-private/lib/formatters';

import {
    ECreditDocumentStatusesColors,
    ECreditProductsNames,
    ERequestsStatusName,
} from '#/src/constants/credit-document-circulation';
import { type TDocumentStatuses } from '#/src/containers/documents-list/documents-list';
import { type TCreditRequestListItem } from '#/src/ducks/credit-requests/types';
import { useGetStatusModalLogic } from '#/src/utils/hooks/use-get-status-modal-logic';

import { PaymentInfo } from '../payment-info';
import { ProgressBar } from '../progress-bar';

import { StatusModalFooterContent } from './status-modal-footer-content';

import './status-modal.css';

export type StatusModalProps = {
    isModalVisible: boolean;
    onCancel: () => void;
    onContinue: () => void;
    onDeleteWelcome: () => void;
    onNewRequestClick: () => void;
    data: TCreditRequestListItem;
    currentTime: Date;
    documentStatuses: TDocumentStatuses;
};

const cn = createCn('status-modal');

export const StatusModal: React.FC<StatusModalProps> = ({
    isModalVisible,
    onCancel,
    onContinue,
    onDeleteWelcome,
    onNewRequestClick,
    data,
    currentTime,
    documentStatuses,
}) => {
    const {
        isFetching,
        isAvailableProgressStages,
        singleProgressStage,
        progressStages,
        currentActiveStep,
        isCreditRequestApproved,
        shortRequestNumber,
        formattedDate,
        clientStatus,
        description,
        handleStepPositive,
    } = useGetStatusModalLogic(data, currentTime);

    const { type, productName } = data;
    const isWelcome =
        type === CommonCreditRequestType.Welcome && clientStatus === ERequestsStatusName.FAILURE;

    const clientStatusColor = clientStatus
        ? ECreditDocumentStatusesColors[
              clientStatus.toLowerCase() as keyof typeof ECreditDocumentStatusesColors
          ]
        : undefined;

    const clientStatusText =
        clientStatus && documentStatuses[clientStatus]
            ? documentStatuses[clientStatus]
            : clientStatus;

    return (
        <Modal
            open={isModalVisible}
            hasCloser={true}
            onClose={onCancel}
            size={isWelcome ? 'm' : 800}
            breakpoint={599}
            className={cn()}
            dataTestId='status-modal'
        >
            <Modal.Header contentClassName={cn('header')}>
                <div className={cn('header-content', { 'welcome-title': isWelcome })}>
                    <Typography.Title view='medium' font='system' tag='h4' color='primary'>
                        {isWelcome
                            ? `Заявка на ${productName}`
                            : ECreditProductsNames.CREDIT_APPLICATION}
                    </Typography.Title>
                    <Typography.Text view='primary-medium' tag='div' color='secondary'>
                        {isWelcome ? shortRequestNumber : formattedDate}
                        {NBSP}
                        {NBSP}
                        {NBSP}
                        {isWelcome ? formattedDate : shortRequestNumber}
                    </Typography.Text>
                </div>
            </Modal.Header>

            <Modal.Content className={cn('content')}>
                {isWelcome ? (
                    <div className={cn('welcome-wrapper')}>
                        {!!clientStatusText && (
                            <Status
                                view='muted-alt'
                                shape='rounded'
                                color={clientStatusColor}
                                className={cn('status')}
                            >
                                {clientStatusText}
                            </Status>
                        )}
                        <Typography.Text tag='div' view='primary-medium'>
                            {description}
                        </Typography.Text>
                    </div>
                ) : (
                    <React.Fragment>
                        <div className={cn('step-wrapper')}>
                            <ProgressBar
                                clientStatus={clientStatus}
                                descriptionInfo={description}
                                currentTime={currentTime}
                                documentStatuses={documentStatuses}
                                isFetching={isFetching}
                                isAvailableProgressStages={isAvailableProgressStages}
                                singleProgressStage={singleProgressStage}
                                progressStages={progressStages}
                                handleStepPositive={handleStepPositive}
                                activeStep={currentActiveStep}
                            />
                        </div>
                        <PaymentInfo data={data} isFetching={isFetching} />
                    </React.Fragment>
                )}
            </Modal.Content>
            <Modal.Footer
                className={cn('footer-content', {
                    'is-loading': isFetching,
                })}
            >
                <StatusModalFooterContent
                    onCancel={isWelcome ? onDeleteWelcome : onCancel}
                    onContinue={onContinue}
                    onNewRequestClick={onNewRequestClick}
                    data={data}
                    isWelcome={isWelcome}
                    isFetching={isFetching}
                    isCreditRequestApproved={isCreditRequestApproved}
                />
            </Modal.Footer>
        </Modal>
    );
};
