import React from 'react';

import { Button } from '@alfalab/core-components/button';
import { Skeleton } from '@alfalab/core-components/skeleton';

import { EProductCodes } from '#/src/constants/credit-products';
import { MAIN_PAGE_TABS } from '#/src/containers/main-page/main-page-tabs';
import { type TCreditRequestListItem } from '#/src/ducks/credit-requests/types';
import { getStatusModalActionButtonTexts } from '#/src/utils/credit-request';

type Props = {
    isCreditRequestApproved: boolean;
    isMobile?: boolean;
    isWelcome?: boolean;
    isFetching: boolean;
    onCancel: () => void;
    onContinue: () => void;
    onNewRequestClick: () => void;
    data: TCreditRequestListItem;
};

export const StatusModalFooterContent: React.FC<Props> = ({
    onCancel,
    onContinue,
    onNewRequestClick,
    data,
    isCreditRequestApproved,
    isFetching,
    isWelcome,
    isMobile = false,
}) => {
    const { clientStatus, isAvailableForCurrentChannel, productCode } = data;
    const isAutoCreditButtonEnabled =
        isAvailableForCurrentChannel && productCode === EProductCodes.UL04;

    const [continueButtonText, cancelButtonText] = getStatusModalActionButtonTexts(
        isAvailableForCurrentChannel,
        clientStatus,
    );

    const goToTab = (tab: HTMLElement | null) => {
        if (tab) {
            tab.click();

            const headerOffset = 60;
            const elementRect = tab.getBoundingClientRect();
            const scrollTop = window.scrollY + elementRect.top - headerOffset;

            window.scrollTo({ top: scrollTop, behavior: 'smooth' });
        }
    };

    const handleGoToCreditProductsTab = () => {
        goToTab(document.getElementById(MAIN_PAGE_TABS.creditProducts));
    };

    const isCreditProductsTabExist =
        document.getElementById(MAIN_PAGE_TABS.creditProducts) !== null;

    if (isFetching && !isWelcome) {
        return <Skeleton visible={true}>~</Skeleton>;
    }

    if (isMobile) {
        return (
            <React.Fragment>
                {isCreditRequestApproved ? (
                    <React.Fragment>
                        {isCreditProductsTabExist && (
                            <Button
                                size={56}
                                view='secondary'
                                block={true}
                                onClick={handleGoToCreditProductsTab}
                            >
                                Мои продукты
                            </Button>
                        )}
                        {isAutoCreditButtonEnabled ? (
                            <Button size={56} block={true} view='primary' onClick={onContinue}>
                                В заявку
                            </Button>
                        ) : (
                            <Button
                                size={56}
                                view='primary'
                                block={true}
                                onClick={onNewRequestClick}
                            >
                                Новая заявка
                            </Button>
                        )}
                    </React.Fragment>
                ) : (
                    <React.Fragment>
                        {(!!continueButtonText || isWelcome) && (
                            <Button size={56} block={true} view='primary' onClick={onContinue}>
                                {isWelcome ? 'Создать новую' : continueButtonText}
                            </Button>
                        )}
                        {(!!cancelButtonText || isWelcome) && (
                            <Button size={56} block={true} view='secondary' onClick={onCancel}>
                                {isWelcome ? 'Удалить' : cancelButtonText}
                            </Button>
                        )}
                    </React.Fragment>
                )}
            </React.Fragment>
        );
    }

    return (
        <React.Fragment>
            {isCreditRequestApproved ? (
                <React.Fragment>
                    {isAutoCreditButtonEnabled ? (
                        <Button size={48} view='primary' onClick={onContinue}>
                            В заявку
                        </Button>
                    ) : (
                        <Button size={48} view='primary' onClick={onNewRequestClick}>
                            Новая заявка
                        </Button>
                    )}

                    {isCreditProductsTabExist && (
                        <Button size={48} view='secondary' onClick={handleGoToCreditProductsTab}>
                            Мои продукты
                        </Button>
                    )}
                </React.Fragment>
            ) : (
                <React.Fragment>
                    {(!!continueButtonText || isWelcome) && (
                        <Button size={48} view='primary' onClick={onContinue}>
                            {isWelcome ? 'Создать новую' : continueButtonText}
                        </Button>
                    )}

                    {(!!cancelButtonText || isWelcome) && (
                        <Button size={48} view='secondary' onClick={onCancel}>
                            {isWelcome ? 'Удалить' : cancelButtonText}
                        </Button>
                    )}
                </React.Fragment>
            )}
        </React.Fragment>
    );
};
