import React from 'react';
import { createCn } from 'bem-react-classname';
import { CommonCreditRequestType } from 'corp-credit-request-api-typescript-services';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { ModalMobile } from '@alfalab/core-components/modal/mobile';
import { Status } from '@alfalab/core-components/status';
import { Typography } from '@alfalab/core-components/typography';
import { NBSP } from 'arui-private/lib/formatters';

import {
    ECreditDocumentStatusesColors,
    ECreditProductsNames,
    ERequestsStatusName,
} from '#/src/constants/credit-document-circulation';
import { useGetStatusModalLogic } from '#/src/utils/hooks/use-get-status-modal-logic';

import { PaymentInfo } from '../../payment-info';
import { ProgressBar } from '../../progress-bar';
import { type StatusModalProps } from '../status-modal';
import { StatusModalFooterContent } from '../status-modal-footer-content';

import './status-modal-mobile.css';

const cn = createCn('status-modal-mobile');

export const StatusModalMobile: React.FC<StatusModalProps> = ({
    isModalVisible,
    onCancel,
    onContinue,
    onDeleteWelcome,
    onNewRequestClick,
    data,
    currentTime,
    documentStatuses,
}) => {
    const {
        isFetching,
        isAvailableProgressStages,
        singleProgressStage,
        progressStages,
        currentActiveStep,
        isCreditRequestApproved,
        shortRequestNumber,
        formattedDate,
        clientStatus,
        description,
        handleStepPositive,
    } = useGetStatusModalLogic(data, currentTime);

    const { type, productName } = data;
    const isWelcome =
        type === CommonCreditRequestType.Welcome && clientStatus === ERequestsStatusName.FAILURE;

    const clientStatusColor = clientStatus
        ? ECreditDocumentStatusesColors[
              clientStatus.toLowerCase() as keyof typeof ECreditDocumentStatusesColors
          ]
        : undefined;

    const clientStatusText =
        clientStatus && documentStatuses[clientStatus]
            ? documentStatuses[clientStatus]
            : clientStatus;

    if (isWelcome) {
        return (
            <BottomSheet
                title={`Заявка на ${productName}`}
                open={isModalVisible}
                onClose={onCancel}
                hasCloser={true}
                actionButton={
                    <div className={cn('welcome-footer-content')}>
                        <Button size={56} block={true} view='primary' onClick={onContinue}>
                            Создать новую
                        </Button>
                        <Button size={56} block={true} view='secondary' onClick={onDeleteWelcome}>
                            Удалить
                        </Button>
                    </div>
                }
            >
                <Typography.Text
                    view='primary-small'
                    tag='div'
                    color='secondary'
                    className={cn('welcome-request-info')}
                >
                    {shortRequestNumber}
                    {NBSP}
                    {NBSP}
                    {NBSP}
                    {formattedDate}
                </Typography.Text>
                {!!clientStatusText && (
                    <div>
                        <Status
                            view='muted-alt'
                            color={clientStatusColor}
                            className={cn('welcome-status')}
                            shape='rounded'
                        >
                            {clientStatusText}
                        </Status>
                    </div>
                )}
                <Typography.Text tag='div' view='primary-medium'>
                    {description}
                </Typography.Text>
            </BottomSheet>
        );
    }

    return (
        <ModalMobile open={isModalVisible} onClose={onCancel} className={cn()}>
            <ModalMobile.Header
                className={cn('header-wrapper')}
                contentClassName={cn('header')}
                title={
                    <Typography.TitleMobile view='medium' font='system' tag='h4' color='primary'>
                        {ECreditProductsNames.CREDIT_APPLICATION}
                    </Typography.TitleMobile>
                }
                subtitle={
                    <Typography.Text view='primary-small' tag='div' color='secondary'>
                        {formattedDate}
                        {NBSP}
                        {NBSP}
                        {NBSP}
                        {shortRequestNumber}
                    </Typography.Text>
                }
                titleSize='compact'
                sticky={true}
                hasCloser={true}
            />
            <ModalMobile.Content className={cn('content')} flex={true}>
                <PaymentInfo data={data} isFetching={isFetching} />

                <div className={cn('step-wrapper')}>
                    <ProgressBar
                        clientStatus={clientStatus}
                        descriptionInfo={description}
                        currentTime={currentTime}
                        documentStatuses={documentStatuses}
                        isFetching={isFetching}
                        isAvailableProgressStages={isAvailableProgressStages}
                        singleProgressStage={singleProgressStage}
                        progressStages={progressStages}
                        handleStepPositive={handleStepPositive}
                        activeStep={currentActiveStep}
                    />
                </div>
            </ModalMobile.Content>

            <ModalMobile.Footer
                sticky={true}
                className={cn('footer-content', {
                    'is-loading': isFetching,
                })}
            >
                <StatusModalFooterContent
                    onCancel={onCancel}
                    onContinue={onContinue}
                    onNewRequestClick={onNewRequestClick}
                    data={data}
                    isFetching={isFetching}
                    isMobile={true}
                    isCreditRequestApproved={isCreditRequestApproved}
                />
            </ModalMobile.Footer>
        </ModalMobile>
    );
};
