@import '@alfalab/core-components/vars';

.status-modal-mobile {
    &__welcome-request-info {
        padding-bottom: var(--gap-16);
    }

    &__welcome-status {
        margin-bottom: var(--gap-16);
    }

    &__welcome-footer-content {
        padding-top: var(--gap-8);
    }

    &__header-wrapper {
        padding-top: var(--gap-2s);
        padding-bottom: var(--gap-2);
    }

    &__header {
        gap: var(--gap-8);
    }

    &__content {
        display: flex;
        flex-direction: column;
        gap: var(--gap-16);
    }

    &__footer-content {
        &_is-loading > div {
            width: 100%;
            height: var(--gap-48);
        }
    }

    &__footer-content > button {
        padding: var(--gap-0) var(--gap-24);
    }

    &__request-payment-info-container {
        padding: var(--gap-24);
        background-color: var(--color-dark-accent-secondary);
        display: flex;
        flex: 1;
        gap: var(--gap-12);
        align-items: flex-start;
        justify-content: space-between;
        flex-direction: column;
        height: fit-content;
    }

    &__condition-item {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    &__condition-item-text {
        flex: 1;
    }

    &__step-status {
        width: fit-content;
        margin-top: var(--gap-8);
    }

    &__step-description {
        margin-top: 6px;
    }

    &__step-date {
        margin-top: var(--gap-4);
    }

    &__status-content {
        margin: var(--gap-12) var(--gap-0) var(--gap-8);
    }
}
