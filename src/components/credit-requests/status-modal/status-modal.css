@import '@alfalab/core-components/vars';

.status-modal {
    & &__header {
        padding-top: var(--gap-0);
        padding-bottom: var(--gap-0);
    }

    &__header-content {
        display: flex;
        flex-direction: column;
        gap: var(--gap-xs);
        padding-bottom: var(--gap-16);

        &_welcome-title > h4 {
            @mixin headline-system_small;
        }
    }

    &__content {
        display: flex;
        flex-direction: column-reverse;
        justify-content: space-between;
        align-items: stretch;
        padding-bottom: var(--gap-4);
        gap: var(--gap-16);
    }

    &__welcome-wrapper {
        display: flex;
        flex-direction: column;
    }

    &__status {
        width: fit-content;
        margin-bottom: var(--gap-20);
    }

    &__footer-content {
        &_is-loading > div {
            width: 104px;
            height: var(--gap-48);
        }
    }

    &__step-wrapper {
        min-width: 350px;
        width: 350px;
    }

    @media (--tablet) {
        width: 600px;
    }

    @media (--desktop-s) {
        &__content {
            flex-direction: row;
            padding-bottom: var(--gap-8);
            gap: var(--gap-32);
        }
    }

    @media (--desktop-l) {
        &__step-wrapper {
            min-width: 400px;
            width: 400px;
        }
    }
}
