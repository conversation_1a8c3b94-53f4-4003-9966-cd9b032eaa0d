import React from 'react';

import { useMatchMedia } from '@alfalab/core-components/mq';

import { type ButtonMarkers } from '#/src/constants/button-markers';
import { type TDocumentStatuses } from '#/src/containers/documents-list/documents-list';
import { type TCreditRequestList } from '#/src/ducks/credit-requests/types';

import { CreditRequestsMobile } from './mobile-view/credit-requests-mobile';
import { CreditRequests } from './credit-requests';

type Props = {
    creditRequestsList: TCreditRequestList;
    documentStatuses: TDocumentStatuses;
    parameters?: { [k: string]: string };
    statusScreen?: React.ReactElement;
    statusScreenView: 'full' | 'content';
    onButtonClick: (btnmarker: ButtonMarkers) => void;
};

export const CreditRequestsContainer: React.FC<Props> = (props) => {
    const [isMobile] = useMatchMedia('--mobile');

    if (isMobile) {
        return <CreditRequestsMobile {...props} />;
    }

    return <CreditRequests {...props} />;
};
