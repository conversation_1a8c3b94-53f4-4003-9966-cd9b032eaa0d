import React, { Fragment, useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { fromUnixTime } from 'date-fns';

import { Button } from '@alfalab/core-components/button';
import { DATE_FORMAT } from '@alfalab/core-components/date-input';
import { Pagination } from '@alfalab/core-components/table/components';
import { PlusCompactSIcon } from '@alfalab/icons-glyph/PlusCompactSIcon';
import { type CorporateTopbar } from 'arui-private/corporate-topbar';
import Table, {
    SortingStatuses,
    type TableColumn,
    type TableGroupedRow,
    type TableRow,
} from 'arui-private/table';
import { type TableProps } from 'arui-private/table/components';

import { ButtonMarkers } from '#/src/constants/button-markers';
import { type TDocumentStatuses } from '#/src/containers/documents-list/documents-list';
import { type TCreditRequestList } from '#/src/ducks/credit-requests/types';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { groupByField } from '#/src/utils/credit-request';
import { dateToCustomFormat } from '#/src/utils/date';
import { useGetCreditRequests } from '#/src/utils/hooks/use-get-credit-requests-logic';

import { getRowFromData } from './credit-request-group-row';
import { DEFAULT_COLUMNS, GROUPING_OPTIONS } from './credit-requests.constants';
import { CreditRequestsTopBar } from './credit-requests-topbar';
import { StatusModal } from './status-modal';

import './credit-requests.css';

export const cn = createCn('credit-requests-table');

const PER_PAGE = [10, 25, 50, 100];
const MIN_PAGINATION_LENGTH = 10;

export type CreditRequestProps = {
    creditRequestsList: TCreditRequestList;
    documentStatuses: TDocumentStatuses;
    statusScreen?: React.ReactElement;
    statusScreenView: 'full' | 'content';
    onButtonClick: (btnmarker: ButtonMarkers) => void;
};

type PaginationProps = {
    perPage: number;
    totalCount: number;
    pageIndex: number;
    handlePerPageChange: (perPage: number) => void;
    handleCurrentPageChange: (pageIndex: number) => void;
};

export const CustomPagination = ({
    perPage,
    totalCount,
    pageIndex,
    handlePerPageChange,
    handleCurrentPageChange,
}: PaginationProps) => {
    const pagesCount = totalCount ? totalCount / perPage : 0;

    return (
        <Pagination
            className={cn('pagination')}
            possiblePerPage={PER_PAGE}
            perPage={perPage}
            currentPageIndex={pageIndex}
            pagesCount={Math.ceil(pagesCount)}
            onPageChange={handleCurrentPageChange}
            onPerPageChange={handlePerPageChange}
            view='default'
        />
    );
};

export const CreditRequests: React.FC<CreditRequestProps> = ({
    creditRequestsList,
    documentStatuses,
    statusScreen,
    statusScreenView,
    onButtonClick,
}) => {
    const {
        isModalVisible,
        selectedRow,
        handleRowClick: onRowClick,
        handleCancelModal,
        handleContinueModal,
        handleDeleteWelcome,
        handleDescriptionClick,
    } = useGetCreditRequests({ creditRequestsList });

    const currentTime = useSelector(currentTimeSelector);

    const [groupingOption, setGroupingOption] = useState(GROUPING_OPTIONS[0]);
    const [columns, setColumns] = useState(DEFAULT_COLUMNS);
    const [sortingStatus, setSortingStatus] = useState(SortingStatuses.UNSET);
    const [selectedPerPage, setSelectedPerPage] = useState(PER_PAGE[0]);
    const [currentPageIndex, setCurrentPageIndex] = useState(0);

    const totalCount = creditRequestsList.length;
    const isSortingActive = sortingStatus !== SortingStatuses.UNSET;
    const isGroupingActive = groupingOption.key !== 'none';
    const isDateGrouping = groupingOption.key === 'createDt';
    const isStatusGrouping = groupingOption.key === 'clientStatus';

    const handleRowClick = useCallback(
        (_: React.MouseEvent<HTMLElement>, id: string) => {
            onRowClick(id);
        },
        [onRowClick],
    );

    const getGroupedCustomTitle = useCallback(
        (value) => {
            if (isDateGrouping) {
                return dateToCustomFormat(currentTime, fromUnixTime(Number(value)), DATE_FORMAT);
            }
            if (isStatusGrouping && documentStatuses[value]) {
                return documentStatuses[value];
            }

            return value;
        },
        [currentTime, documentStatuses, isDateGrouping, isStatusGrouping],
    );

    const { rows, rowsLength, isPaginationAvailable } = useMemo(() => {
        if (isGroupingActive) {
            const groupedRows: TableGroupedRow[] = groupByField({
                list: creditRequestsList,
                groupBy: groupingOption,
                handleCustomTitle: getGroupedCustomTitle,
            }).map(([title, rowsData]) => ({
                id: title,
                title,
                selectedGroupingOption: groupingOption,
                rows: rowsData.map((data) =>
                    getRowFromData({
                        data,
                        currentTime,
                        documentStatuses,
                        handleRowClick,
                        handleDescriptionClick,
                    }),
                ),
            }));

            return { rows: groupedRows, isPaginationAvailable: false, rowsLength: 0 };
        }

        let modifiedLists = isSortingActive
            ? [...creditRequestsList].sort((a, b) =>
                  sortingStatus === SortingStatuses.DESCENDING
                      ? (b?.createDt ?? 0) - (a?.createDt ?? 0)
                      : (a?.createDt ?? 0) - (b?.createDt ?? 0),
              )
            : [...creditRequestsList];
        const listLength = modifiedLists.length;

        const isShowPagination = listLength > MIN_PAGINATION_LENGTH;

        if (isShowPagination) {
            modifiedLists = modifiedLists
                .slice(currentPageIndex * selectedPerPage)
                .slice(0, selectedPerPage);
        }

        const rowsList: TableRow[] = modifiedLists.map((data) =>
            getRowFromData({
                data,
                currentTime,
                documentStatuses,
                handleRowClick,
                handleDescriptionClick,
            }),
        );

        return { rows: rowsList, rowsLength: listLength, isPaginationAvailable: isShowPagination };
    }, [
        creditRequestsList,
        currentPageIndex,
        currentTime,
        documentStatuses,
        getGroupedCustomTitle,
        groupingOption,
        handleDescriptionClick,
        handleRowClick,
        isGroupingActive,
        isSortingActive,
        selectedPerPage,
        sortingStatus,
    ]);

    const handleEmptyTableStatusScreenButtonClick = () => setColumns(DEFAULT_COLUMNS);

    const handleColumnsChange = useCallback((nextColumns) => setColumns(nextColumns), [setColumns]);

    const handleResetSettings = () => {
        setGroupingOption(GROUPING_OPTIONS[0]);
        setColumns(DEFAULT_COLUMNS);
    };

    const handleGroupingOptionsChange: React.ComponentProps<
        typeof CorporateTopbar
    >['onGroupingOptionsChange'] = useCallback(
        ({ selected }) => {
            if (selected.key === 'none') {
                setColumns(DEFAULT_COLUMNS);
            } else {
                const sortedColumn = columns.findIndex(
                    (column: TableColumn) => column.field === 'createDt',
                );
                const updatedColumns = [...columns];

                updatedColumns[sortedColumn].sortStatus = SortingStatuses.UNSET;
                setColumns(updatedColumns);
            }
            setGroupingOption(selected);
        },
        [columns],
    );

    const handleSort: TableProps['onSort'] = ([field, value]) => {
        if (isGroupingActive) return;

        const sortedColumn = columns.findIndex((column: TableColumn) => column.field === field);
        const updatedColumns = [...columns];

        updatedColumns[sortedColumn].sortStatus = value;

        setColumns(updatedColumns);
        setSortingStatus(value);
    };

    const handlePerPageChange = useCallback((value) => {
        setCurrentPageIndex(0);
        setSelectedPerPage(value);
    }, []);

    const handleCurrentPageChange = useCallback((pageIndex) => {
        setCurrentPageIndex(pageIndex);
    }, []);

    const extendedCustomTopBar = useMemo(
        () => (
            <CreditRequestsTopBar
                selectedGroupingOption={groupingOption}
                groupingOptions={GROUPING_OPTIONS}
                count={totalCount}
                additionalComponent={
                    <Button
                        size='xxs'
                        className={cn('redirect-button')}
                        leftAddons={<PlusCompactSIcon />}
                        onClick={() => onButtonClick(ButtonMarkers.BUTTON_3)}
                        dataTestId='credit-requests__new-request'
                    >
                        Новая заявка
                    </Button>
                }
                onReset={handleResetSettings}
                onGroupingOptionsChange={handleGroupingOptionsChange}
                dataTestId='credit-requests-topbar'
            />
        ),
        [groupingOption, totalCount, handleGroupingOptionsChange, onButtonClick],
    );

    return (
        <Fragment>
            <Table
                mode='wide'
                data-test-id='credit-requests__table'
                rows={rows}
                columns={columns}
                className={cn()}
                topbar={extendedCustomTopBar}
                onEmptyTableStatusScreenButtonClick={handleEmptyTableStatusScreenButtonClick}
                onColumnsChange={handleColumnsChange}
                onSort={handleSort}
                statusScreen={statusScreen}
                statusScreenView={statusScreenView}
                pagination={
                    isPaginationAvailable ? (
                        <CustomPagination
                            perPage={selectedPerPage}
                            totalCount={rowsLength}
                            pageIndex={currentPageIndex}
                            handlePerPageChange={handlePerPageChange}
                            handleCurrentPageChange={handleCurrentPageChange}
                        />
                    ) : null
                }
            />
            {isModalVisible && (
                <StatusModal
                    isModalVisible={isModalVisible}
                    onCancel={handleCancelModal}
                    onContinue={handleContinueModal}
                    onDeleteWelcome={handleDeleteWelcome}
                    onNewRequestClick={() => onButtonClick(ButtonMarkers.BUTTON_3)}
                    data={selectedRow}
                    currentTime={currentTime}
                    documentStatuses={documentStatuses}
                />
            )}
        </Fragment>
    );
};
