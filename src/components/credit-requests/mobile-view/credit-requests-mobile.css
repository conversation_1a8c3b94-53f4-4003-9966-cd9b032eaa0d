@import '@alfalab/core-components/vars';

.credit-requests-mobile {
    &__row {
        position: relative;
        padding: var(--gap-24) var(--gap-12);
        margin-bottom: var(--gap-16);
        border-radius: var(--border-radius-24);
    }

    &__row-header {
        display: flex;
        justify-content: space-between;
        padding-bottom: var(--gap-12);
    }

    &__row-content {
        display: grid;
        gap: var(--gap-4);
    }

    &__product-content {
        font-weight: 600;
    }

    &__amount-wrapper {
        padding-top: var(--gap-4);
        &_flexible {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }

    &__amount {
        display: flex;
        padding: var(--gap-4) var(--gap-0);
        line-height: 20px;
    }

    &__row-footer {
        padding-top: var(--gap-16);
    }

    &__topbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--gap-20) var(--gap-0);
    }

    &__redirect-wrapper {
        position: sticky;
        bottom: var(--gap-0);
        margin: var(--gap-0) calc(-1 * var(--gap-20)) calc(-1 * var(--gap-24));
        padding: var(--gap-16);
        background-color: var(--color-white);
        box-shadow: 0 -1px 0 0 var(--color-light-neutral-translucent-300);
    }

    &__load-more {
        padding: var(--gap-16);
    }

    @media (--mobile-m) {
        &__row {
            padding: var(--gap-24) var(--gap-16);
        }
    }

    @media (--mobile-l) {
        &__row {
            padding: var(--gap-24) var(--gap-20);
        }
    }
}
