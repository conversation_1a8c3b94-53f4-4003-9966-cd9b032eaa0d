import React, { useMemo } from 'react';
import { createCn } from 'bem-react-classname';
import {
    CommonCreditRequestType,
    RedirectListItemType,
} from 'corp-credit-request-api-typescript-services';
import { fromUnixTime } from 'date-fns';

import { Button } from '@alfalab/core-components/button';
import { Status } from '@alfalab/core-components/status';
import { StatusBadge } from '@alfalab/core-components/status-badge';
import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';

import AmountPure from '#/src/components/ui/amount-pure/amount-pure';
import {
    ECreditDocumentStatusesColors,
    ECreditProductsNames,
} from '#/src/constants/credit-document-circulation';
import { LINKS_TITLE } from '#/src/constants/credit-requests';
import { DATE_FORMAT } from '#/src/constants/date';
import { type TDocumentStatuses } from '#/src/containers/documents-list/documents-list';
import { type TCreditRequestListItem } from '#/src/ducks/credit-requests/types';
import { dateToCustomFormat } from '#/src/utils/date';

import './credit-requests-mobile.css';

const cn = createCn('credit-requests-mobile');

type GroupRowProps = {
    data: TCreditRequestListItem;
    currentTime: Date;
    documentStatuses: TDocumentStatuses;
    handleRowClick: (id: string) => void;
    handleDescriptionClick: (id: string) => void;
};

export const CreditRequestsGroupRowMobile: React.FC<GroupRowProps> = ({
    data,
    documentStatuses,
    currentTime,
    handleRowClick,
    handleDescriptionClick,
}) => {
    const {
        id,
        type,
        createDt,
        description,
        isAvailableForCurrentChannel,
        productName,
        limit,
        clientStatus,
        redirect,
    } = data;

    const redirectLinkData = redirect?.find(
        (redirectItem) => redirectItem.type === RedirectListItemType.Main,
    );
    const isMmb = type === CommonCreditRequestType.Mmb;
    const lowerMessage = description?.toLocaleLowerCase() || '';
    const clientStatusLowerCase = clientStatus?.toLowerCase();

    let shouldRedirect;
    let matchedLink;

    if (isMmb) {
        shouldRedirect = isAvailableForCurrentChannel;
        matchedLink = LINKS_TITLE.find((link) => lowerMessage.includes(link));
    } else {
        shouldRedirect = Boolean(redirectLinkData?.redirectLink);
        matchedLink = redirectLinkData?.linkText;
    }

    const redirectText = matchedLink
        ? matchedLink.charAt(0).toUpperCase() + matchedLink.slice(1)
        : null;
    const isRedirectLinkExist = shouldRedirect && Boolean(matchedLink);

    const formattedDate = useMemo(
        () =>
            createDt
                ? dateToCustomFormat(currentTime, fromUnixTime(Number(createDt)), DATE_FORMAT)
                : null,
        [createDt, currentTime],
    );

    const clientStatusColor = clientStatusLowerCase
        ? ECreditDocumentStatusesColors[
              clientStatusLowerCase as keyof typeof ECreditDocumentStatusesColors
          ]
        : undefined;

    let clientStatusText = '';

    if (clientStatusLowerCase) {
        clientStatusText =
            documentStatuses[clientStatusLowerCase] ||
            clientStatusLowerCase.charAt(0).toUpperCase() + clientStatusLowerCase.slice(1);
    }

    if (!id) {
        return null;
    }

    return (
        <BackgroundPlate
            className={cn('row')}
            onClick={() => handleRowClick(id)}
            view={BackgroundPlateView.Secondary}
        >
            <div className={cn('row-header')}>
                {clientStatus && (
                    <Status
                        view='muted-alt'
                        color={clientStatusColor}
                        shape='rounded'
                        className={cn('status-content')}
                        uppercase={false}
                    >
                        {clientStatusText}
                    </Status>
                )}
                {isMmb && <StatusBadge size={20} view='neutral-information' />}
            </div>

            <div className={cn('row-content')}>
                <Typography.TitleMobile
                    view='xsmall'
                    font='system'
                    tag='div'
                    className={cn('product-content')}
                >
                    {productName || ECreditProductsNames.BUSINESS_CREDIT}
                </Typography.TitleMobile>
                {description && (
                    <Typography.Text tag='div' view='primary-small'>
                        {description}
                    </Typography.Text>
                )}
                <div className={cn('amount-wrapper', { flexible: !!limit })}>
                    {limit && (
                        <Typography.Text view='component-primary' color='primary'>
                            <AmountPure
                                value={limit}
                                view='withZeroMinorPart'
                                className={cn('amount')}
                                bold='none'
                            />
                        </Typography.Text>
                    )}
                    <Typography.Text view='primary-small' tag='div' color='secondary'>
                        {formattedDate}
                    </Typography.Text>
                </div>
            </div>

            {isRedirectLinkExist && (
                <div className={cn('row-footer')}>
                    <Button
                        block={true}
                        size={48}
                        view='secondary'
                        onClick={(event: React.MouseEvent) => {
                            event.stopPropagation();
                            handleDescriptionClick(id);
                        }}
                    >
                        {redirectText}
                    </Button>
                </div>
            )}
        </BackgroundPlate>
    );
};
