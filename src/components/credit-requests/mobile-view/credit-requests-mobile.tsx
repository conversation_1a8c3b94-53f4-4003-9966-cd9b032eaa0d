import React, { useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { Button } from '@alfalab/core-components/button';
import { SelectResponsive } from '@alfalab/core-components/select/Component.responsive';
import { type OptionShape } from '@alfalab/core-components/select/shared';
import { Typography } from '@alfalab/core-components/typography';

import { CustomField } from '#/src/components/ui/custom-field/custom-field';
import { CustomOption } from '#/src/components/ui/custom-option-with-radio/custom-option-with-radio';
import { ButtonMarkers } from '#/src/constants/button-markers';
import { REQUESTS_STATUS_SORT_OPTIONS, Sort } from '#/src/constants/credit-document-circulation';
import { NBSP } from '#/src/constants/unicode-symbols';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { useGetCreditRequests } from '#/src/utils/hooks/use-get-credit-requests-logic';

import { type CreditRequestProps } from '../credit-requests';
import { StatusModalMobile } from '../status-modal';

import { CreditRequestsGroupRowMobile } from './credit-request-group-row-mobile';

import './credit-requests-mobile.css';

const cn = createCn('credit-requests-mobile');
const COUNT_STEP = 10;

export const CreditRequestsMobile: React.FC<CreditRequestProps> = ({
    creditRequestsList = [],
    documentStatuses,
    statusScreen,
    onButtonClick,
}) => {
    const {
        isModalVisible,
        selectedRow,
        handleRowClick,
        handleCancelModal,
        handleContinueModal,
        handleDeleteWelcome,
        handleDescriptionClick,
    } = useGetCreditRequests({ creditRequestsList });

    const currentTime = useSelector(currentTimeSelector);

    const [listCount, setListCount] = useState<number>(COUNT_STEP);
    const [selectedStatus, setSelectedStatus] = useState<OptionShape | null>(null);

    const totalCount = creditRequestsList.length;

    const handleSortOnChange = useCallback((value) => {
        setSelectedStatus(value.selected);
        setListCount(COUNT_STEP);
    }, []);

    const handleLoadMore = useCallback(() => {
        setListCount((prevCount) => prevCount + COUNT_STEP);
    }, []);

    const requestList = useMemo(
        () =>
            selectedStatus
                ? [...creditRequestsList].sort((a, b) =>
                      selectedStatus.key === Sort.Old
                          ? (a?.createDt ?? 0) - (b?.createDt ?? 0)
                          : (b?.createDt ?? 0) - (a?.createDt ?? 0),
                  )
                : creditRequestsList,
        [creditRequestsList, selectedStatus],
    );

    const isLoadMoreVisible = listCount < totalCount;

    return (
        <div className={cn()}>
            <div className={cn('topbar')}>
                <div className={cn('count_wrapper')}>
                    <Typography.Text
                        className={cn('count')}
                        tag='p'
                        defaultMargins={false}
                        view='primary-small'
                        color='primary'
                    >
                        {`Найдено${NBSP}`}
                        <Typography.Text tag='span' view='primary-small' weight='bold'>
                            {totalCount}
                        </Typography.Text>
                    </Typography.Text>
                </div>
                <div className={cn('toolbar-container')}>
                    <SelectResponsive
                        label='Сортировка'
                        popoverPosition='bottom-start'
                        allowUnselect={true}
                        optionsListWidth='content'
                        options={REQUESTS_STATUS_SORT_OPTIONS}
                        Field={CustomField}
                        onChange={handleSortOnChange}
                        selected={selectedStatus}
                        Option={CustomOption}
                        dataTestId='request-filter-sort-select'
                        fieldProps={{
                            size: 32,
                            hasNoLabel: true,
                            showClear: false,
                            labelType: 'uppercase',
                        }}
                    />
                </div>
            </div>
            {statusScreen ||
                requestList
                    .slice(0, listCount)
                    .map((data) => (
                        <CreditRequestsGroupRowMobile
                            key={data.id}
                            data={data}
                            currentTime={currentTime}
                            documentStatuses={documentStatuses}
                            handleRowClick={handleRowClick}
                            handleDescriptionClick={handleDescriptionClick}
                        />
                    ))}

            {isLoadMoreVisible ? (
                <div className={cn('load-more')}>
                    <Button block={true} size={40} view='secondary' onClick={handleLoadMore}>
                        Показать еще
                    </Button>
                </div>
            ) : null}

            <div className={cn('redirect-wrapper')}>
                <Button
                    block={true}
                    size={56}
                    view='primary'
                    onClick={() => onButtonClick(ButtonMarkers.BUTTON_3)}
                    dataTestId='credit-requests__new-request'
                >
                    Новая заявка
                </Button>
            </div>

            {isModalVisible && (
                <StatusModalMobile
                    isModalVisible={isModalVisible}
                    onCancel={handleCancelModal}
                    onContinue={handleContinueModal}
                    onDeleteWelcome={handleDeleteWelcome}
                    onNewRequestClick={() => onButtonClick(ButtonMarkers.BUTTON_3)}
                    data={selectedRow}
                    currentTime={currentTime}
                    documentStatuses={documentStatuses}
                />
            )}
        </div>
    );
};
