import { type OptionShape as GroupingOption } from '@alfalab/core-components/select/typings';
import { SortingStatuses, type TableColumn } from 'arui-private/table';

import { ECreditProductsNames } from '#/src/constants/credit-document-circulation';

export const GROUPING_OPTIONS: GroupingOption[] = [
    { key: 'none', content: 'Без группировки', value: 'none' },
    { key: 'productName', content: 'Продукт', value: ECreditProductsNames.BUSINESS_CREDIT },
    { key: 'createDt', content: 'Дата', value: 'Без даты' },
    { key: 'clientStatus', content: 'Статус', value: 'Без названия' },
];

export const DEFAULT_COLUMNS: TableColumn[] = [
    {
        field: 'createDt',
        title: 'Дата',
        width: 110,
        isLineBreakEnabled: false,
        align: 'left',
        sortStatus: SortingStatuses.UNSET,
        sortOptions: [
            { title: 'По убыванию', status: SortingStatuses.DESCENDING },
            { title: 'По возрастанию', status: SortingStatuses.ASCENDING },
        ],
        mode: 'wide',
    },
    {
        field: 'productName',
        title: 'Продукт',
        width: 205,
        isLineBreakEnabled: true,
        align: 'left',
        mode: 'wide',
    },
    {
        field: 'clientStatus',
        title: 'Статус',
        width: 220,
        isLineBreakEnabled: false,
        align: 'left',
        mode: 'wide',
    },
    {
        field: 'description',
        title: 'Описание',
        width: 345,
        isLineBreakEnabled: true,
        align: 'left',
        textWrapperProps: {
            view: 'primary-small',
            tag: 'div',
            color: 'secondary',
        },
        mode: 'wide',
    },
    {
        field: 'sum',
        title: 'Сумма',
        width: 160,
        isLineBreakEnabled: false,
        align: 'right',
        className: 'sum__content',
        mode: 'wide',
    },
];
