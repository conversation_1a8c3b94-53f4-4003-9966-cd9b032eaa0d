import React, { useMemo } from 'react';
import { createCn } from 'bem-react-classname';
import { LINKS_TITLE } from 'src/constants/credit-requests';

import { Link, type LinkProps } from '@alfalab/core-components/link';

import './request-description.css';

const cn = createCn('request-description');

type RequestDescriptionProps = {
    message: string;
    isLinkExist: boolean;
    onClick: () => void;
    linkText?: string;
    linkProps?: Omit<LinkProps, 'onClick'>;
};

export const RequestDescription: React.FC<RequestDescriptionProps> = ({
    message,
    isLinkExist,
    linkText,
    onClick,
    linkProps,
}) => {
    const renderMessageWithLink = useMemo(() => {
        const lowerMessage = message.toLowerCase();
        const normalizedLinkText = linkText?.toLowerCase();
        const matchedLink =
            normalizedLinkText || LINKS_TITLE.find((link) => lowerMessage.includes(link));

        if (!matchedLink) {
            return message;
        }

        const indexOfLink = lowerMessage.indexOf(matchedLink);
        const beforeLink = message.slice(0, indexOfLink);
        const afterLink = message.slice(indexOfLink + matchedLink.length);
        const linkContent = message.substring(indexOfLink, indexOfLink + matchedLink.length);

        return (
            <React.Fragment>
                {beforeLink}
                <Link
                    className={cn('link')}
                    view='primary'
                    {...linkProps}
                    onClick={(event: React.MouseEvent) => {
                        event.stopPropagation();
                        onClick();
                    }}
                >
                    {linkContent}
                </Link>
                {afterLink}
            </React.Fragment>
        );
    }, [message, linkText, linkProps, onClick]);

    return <React.Fragment>{isLinkExist ? renderMessageWithLink : message}</React.Fragment>;
};
