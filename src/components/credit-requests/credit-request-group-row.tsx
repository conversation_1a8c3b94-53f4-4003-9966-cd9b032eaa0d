import React from 'react';
import { createCn } from 'bem-react-classname';
import {
    CommonCreditRequestType,
    RedirectListItemType,
} from 'corp-credit-request-api-typescript-services';
import { fromUnixTime } from 'date-fns';

import { Status } from '@alfalab/core-components/status';
import { Typography } from '@alfalab/core-components/typography';

import AmountPure from '#/src/components/ui/amount-pure/amount-pure';
import {
    ECreditDocumentStatusesColors,
    ECreditProductsNames,
} from '#/src/constants/credit-document-circulation';
import { DATE_FORMAT } from '#/src/constants/date';
import { type TDocumentStatuses } from '#/src/containers/documents-list/documents-list';
import { type TCreditRequestListItem } from '#/src/ducks/credit-requests/types';
import { getShortRequestNumber } from '#/src/utils/credit-request';
import { dateToCustomFormat } from '#/src/utils/date';

import { RequestDescription } from './request-description';

const cn = createCn('credit-requests-table');

type Props = {
    data: TCreditRequestListItem;
    currentTime: Date;
    documentStatuses: TDocumentStatuses;
    handleRowClick: (elem: React.MouseEvent<HTMLElement>, id: string) => void;
    handleDescriptionClick: (id: string | undefined) => void;
};

export const getRowFromData = ({
    data,
    currentTime,
    documentStatuses,
    handleRowClick,
    handleDescriptionClick,
}: Props) => {
    const {
        id,
        type,
        createDt,
        description,
        isAvailableForCurrentChannel,
        productName,
        limit,
        clientStatus,
        redirect,
    } = data;

    const redirectLinkData = redirect?.find(
        (redirectItem) => redirectItem.type === RedirectListItemType.Main,
    );
    const isMmb = type === CommonCreditRequestType.Mmb;
    const shouldRedirect = isMmb ? isAvailableForCurrentChannel : redirectLinkData?.redirectLink;
    const isRedirectLinkExist = Boolean(shouldRedirect);

    const clientStatusColor = clientStatus
        ? ECreditDocumentStatusesColors[
              clientStatus.toLowerCase() as keyof typeof ECreditDocumentStatusesColors
          ]
        : undefined;
    const clientStatusText =
        clientStatus && documentStatuses[clientStatus.toLowerCase()]
            ? documentStatuses[clientStatus.toLowerCase()]
            : clientStatus;

    const shortRequestNumber = getShortRequestNumber(id, type);

    const dateContent = (
        <Typography.Text view='primary-small' tag='div'>
            {createDt
                ? dateToCustomFormat(currentTime, fromUnixTime(Number(data?.createDt)), DATE_FORMAT)
                : null}
        </Typography.Text>
    );

    const productContent = (
        <div className={cn('product-content')}>
            <Typography.Text
                view='primary-small'
                tag='div'
                weight='regular'
                className={cn('product-title')}
            >
                {productName || ECreditProductsNames.BUSINESS_CREDIT}
            </Typography.Text>
            {shortRequestNumber && (
                <Typography.Text view='secondary-large' tag='div' color='secondary'>
                    {shortRequestNumber}
                </Typography.Text>
            )}
        </div>
    );

    const statusContent = clientStatus && (
        <Status
            view='muted-alt'
            color={clientStatusColor}
            shape='rounded'
            className={cn('status-content')}
        >
            {clientStatusText}
        </Status>
    );

    const textContent = description ? (
        <RequestDescription
            message={description}
            isLinkExist={isRedirectLinkExist}
            linkText={redirectLinkData?.linkText}
            onClick={() => handleDescriptionClick(id)}
        />
    ) : (
        ''
    );

    const sumContent = limit ? (
        <AmountPure
            value={limit}
            view='default'
            className={cn('row', {
                amount: true,
            })}
            bold='full'
        />
    ) : (
        <div>&nbsp;&mdash;&nbsp;</div>
    );

    if (!id) {
        return null;
    }

    return {
        id,
        cells: {
            createDt: {
                content: dateContent,
            },
            productName: {
                content: productContent,
            },
            clientStatus: {
                content: statusContent,
            },
            description: {
                content: textContent,
            },
            sum: {
                content: sumContent,
            },
        },
        onRowClick: handleRowClick,
    };
};
