import React from 'react';
import { createCn } from 'bem-react-classname';

import { Status } from '@alfalab/core-components/status';
import { Steps } from '@alfalab/core-components/steps';
import { Typography } from '@alfalab/core-components/typography';

import { ECreditDocumentStatusesColors } from '#/src/constants/credit-document-circulation';
import { type TDocumentStatuses } from '#/src/containers/documents-list/documents-list';
import {
    type TCreditRequestProgressStagesResponse,
    type TCreditRequestSingleProgressStageResponse,
} from '#/src/ducks/credit-requests/types';

import { ProgressBarItem } from './progress-bar-item';
import { ProgressBarSkeleton } from './progress-bar-skeleton';

import './progress-bar.css';

type Props = {
    isFetching: boolean;
    isAvailableProgressStages: boolean;
    singleProgressStage: TCreditRequestSingleProgressStageResponse | null;
    progressStages: TCreditRequestProgressStagesResponse;
    activeStep: number;
    handleStepPositive: (stepNumber: number) => boolean;
    descriptionInfo?: string;
    clientStatus?: string;
    currentTime: Date;
    documentStatuses: TDocumentStatuses;
};

const cn = createCn('progress-bar');

export const ProgressBar: React.FC<Props> = ({
    clientStatus,
    descriptionInfo,
    currentTime,
    documentStatuses,
    isFetching,
    isAvailableProgressStages,
    singleProgressStage,
    progressStages,
    activeStep,
    handleStepPositive,
}) => {
    if (isFetching) {
        return <ProgressBarSkeleton />;
    }

    if (!isAvailableProgressStages) {
        const status = singleProgressStage?.status?.clientStatusName || clientStatus;
        const description = singleProgressStage?.status?.clientDescription || descriptionInfo;

        const statusLowerCase = status?.toLowerCase();

        const statusColor = status
            ? ECreditDocumentStatusesColors[
                  statusLowerCase as keyof typeof ECreditDocumentStatusesColors
              ]
            : undefined;

        let statusText = '';

        if (statusLowerCase) {
            statusText =
                documentStatuses[statusLowerCase] ||
                statusLowerCase.charAt(0).toUpperCase() + statusLowerCase.slice(1);
        }

        return (
            <React.Fragment>
                {!!statusText && (
                    <Status
                        view='muted-alt'
                        color={statusColor}
                        className={cn('status-content')}
                        shape='rounded'
                        uppercase={false}
                    >
                        {statusText}
                    </Status>
                )}
                <Typography.Text tag='div' view='primary-medium'>
                    {description}
                </Typography.Text>
            </React.Fragment>
        );
    }

    return (
        <Steps
            activeStep={activeStep}
            ordered={false}
            isVerticalAlign={true}
            interactive={false}
            minSpaceBetweenSteps={8}
            checkIsStepPositive={handleStepPositive}
            dataTestId='progress-bar'
        >
            {progressStages.map(({ stageOrder, stageName, sla, progressStage, status }) => (
                <ProgressBarItem
                    key={stageOrder}
                    currentTime={currentTime}
                    documentStatuses={documentStatuses}
                    stageOrder={stageOrder}
                    stageName={stageName}
                    sla={sla}
                    progressStage={progressStage}
                    status={status}
                />
            ))}
        </Steps>
    );
};
