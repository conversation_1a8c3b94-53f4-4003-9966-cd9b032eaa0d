import React from 'react';
import { createCn } from 'bem-react-classname';

import { Skeleton } from '@alfalab/core-components/skeleton';

import './progress-bar.css';

const cn = createCn('progress-bar');

export const ProgressBarSkeleton = () => (
    <React.Fragment>
        <Skeleton visible={true} className={cn('skeleton-row')} />
        <Skeleton visible={true} className={cn('skeleton-row')} />
        <Skeleton visible={true} className={cn('skeleton-row')} />
        <Skeleton visible={true} className={cn('skeleton-row')} />
        <Skeleton visible={true} className={cn('skeleton-row')} />
        <Skeleton visible={true} className={cn('skeleton-row')} />
    </React.Fragment>
);
