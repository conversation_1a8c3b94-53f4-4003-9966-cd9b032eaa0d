import React from 'react';
import { createCn } from 'bem-react-classname';

import { PureCell } from '@alfalab/core-components/pure-cell';
import { Status } from '@alfalab/core-components/status';
import { Typography } from '@alfalab/core-components/typography';
import { NBSP } from 'arui-private/lib/formatters';

import {
    ECreditDocumentStatusesColors,
    EProgressStageType,
} from '#/src/constants/credit-document-circulation';
import { DATE_FORMAT } from '#/src/constants/date';
import { type TDocumentStatuses } from '#/src/containers/documents-list/documents-list';
import {
    type TCreditRequestProgressStatusResponse,
    type TCreditRequestSingleProgressStageResponse,
} from '#/src/ducks/credit-requests/types';
import { dateToCustomFormat } from '#/src/utils/date';

import './progress-bar.css';

type Props = {
    stageOrder: number;
    stageName: string;
    progressStage: TCreditRequestSingleProgressStageResponse['progressStage'];
    sla?: Date;
    status?: TCreditRequestProgressStatusResponse;
    currentTime: Date;
    documentStatuses: TDocumentStatuses;
};

const cn = createCn('progress-bar');

export const ProgressBarItem: React.FC<Props> = ({
    documentStatuses,
    currentTime,
    stageOrder,
    stageName,
    sla,
    progressStage,
    status,
}) => {
    const clientStatusColor = status
        ? ECreditDocumentStatusesColors[
              status.clientStatusName?.toLowerCase() as keyof typeof ECreditDocumentStatusesColors
          ]
        : undefined;

    const clientStatusText =
        status && status.clientStatusName && documentStatuses[status.clientStatusName.toLowerCase()]
            ? documentStatuses[status.clientStatusName.toLowerCase()]
            : status?.clientStatusName || '';

    const title = stageName.charAt(0) + stageName.slice(1).toLowerCase();
    const clientStatus = clientStatusText.charAt(0) + clientStatusText.slice(1).toLowerCase();

    const formattedSla = sla ? dateToCustomFormat(currentTime, sla, DATE_FORMAT) : null;

    return (
        <PureCell
            key={stageOrder}
            verticalPadding='none'
            horizontalPadding='none'
            dataTestId='stage-step'
        >
            <PureCell.Content>
                <PureCell.Main>
                    <Typography.Text
                        tag='div'
                        view='primary-large'
                        weight='medium'
                        color='primary'
                        className={cn('step-title')}
                    >
                        {title}
                    </Typography.Text>
                    {progressStage === EProgressStageType.CURRENT ? (
                        <React.Fragment>
                            <Status
                                view='muted-alt'
                                color={clientStatusColor}
                                className={cn('step-status')}
                                shape='rounded'
                                uppercase={false}
                            >
                                {clientStatus}
                            </Status>
                            <Typography.Text
                                tag='div'
                                view='primary-medium'
                                color='secondary'
                                className={cn('step-description')}
                            >
                                {status?.clientDescription}
                            </Typography.Text>
                            {formattedSla && (
                                <div className={cn('step-date')}>
                                    <Typography.Text
                                        view='primary-medium'
                                        tag='span'
                                        color='secondary'
                                    >
                                        {`Срок:${NBSP}`}
                                    </Typography.Text>
                                    <Typography.Text
                                        view='primary-medium'
                                        tag='span'
                                        color='primary'
                                    >
                                        {`до${NBSP}${formattedSla}`}
                                    </Typography.Text>
                                </div>
                            )}
                        </React.Fragment>
                    ) : null}
                </PureCell.Main>
            </PureCell.Content>
        </PureCell>
    );
};
