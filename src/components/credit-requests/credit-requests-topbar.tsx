import React from 'react';

import { CorporateTopbar } from 'arui-private/corporate-topbar';

import { cn } from './credit-requests';
import { GROUPING_OPTIONS } from './credit-requests.constants';

import './credit-requests.css';

export type TProps = React.ComponentProps<typeof CorporateTopbar>;

export const CreditRequestsTopBar: React.FC<TProps> = (props) => (
    <CorporateTopbar
        countModeText='Найдено'
        groupingOptions={GROUPING_OPTIONS}
        toolbar={true}
        className={cn('topbar')}
        data-test-id='credit-requests-topbar'
        {...props}
    />
);
