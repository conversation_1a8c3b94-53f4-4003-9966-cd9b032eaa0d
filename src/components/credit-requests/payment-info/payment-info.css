@import '@alfalab/core-components/vars';

.request-payment-info {
    &__container {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        flex-direction: column;
        gap: var(--gap-12);
        height: fit-content;
        padding: var(--gap-16);
        margin-top: var(--gap-8);
    }

    &__condition-item {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    &__condition-item-text {
        flex: 1;
    }

    &__skeleton {
        width: 100%;
        height: 152px;
    }

    @media (--tablet-s) {
        &__container {
            margin-top: var(--gap-0);
        }
    }

    @media (--tablet-m) {
        &__container {
            flex: 1;
            padding: var(--gap-24);
            gap: var(--gap-16);
        }
    }
}
