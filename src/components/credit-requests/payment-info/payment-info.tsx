import React from 'react';
import { createCn } from 'bem-react-classname';

import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';

import {
    ECreditProductsNames,
    ECreditRequestInterestRate,
} from '#/src/constants/credit-document-circulation';
import { type TCreditRequestListItem } from '#/src/ducks/credit-requests/types';
import { formatMonthsToString } from '#/src/utils/formatters';

import AmountPure from '../../ui/amount-pure';

import './payment-info.css';

type Props = {
    data: TCreditRequestListItem;
    isFetching: boolean;
};

const cn = createCn('request-payment-info');

export const PaymentInfo: React.FC<Props> = ({ data, isFetching }) => {
    const { limit, productName, productInfo } = data;
    const { loanTerm, interestRate } = productInfo || {};
    const { type, rate } = interestRate || {};

    const requestInterestRateText =
        type && rate
            ? ECreditRequestInterestRate[type as keyof typeof ECreditRequestInterestRate]
            : null;

    if (isFetching) {
        return <Skeleton visible={true} className={cn('skeleton')} />;
    }

    if (!loanTerm && !requestInterestRateText) {
        return null;
    }

    return (
        <BackgroundPlate
            data-test-id='request-payment-info'
            view={BackgroundPlateView.Secondary}
            className={cn('container')}
        >
            <Typography.TitleResponsive
                view='xsmall'
                tag='h5'
                font='system'
                color='primary'
                breakpoint={768}
            >
                {productName || ECreditProductsNames.BUSINESS_CREDIT}
            </Typography.TitleResponsive>
            {!!loanTerm && (
                <div className={cn('condition-item')} data-test-id='to-date'>
                    <Typography.Text
                        tag='div'
                        className={cn('condition-item-text')}
                        view='primary-medium'
                        color='secondary'
                    >
                        Срок
                    </Typography.Text>
                    <Typography.Text
                        tag='div'
                        className={cn('condition-item-text')}
                        view='primary-medium'
                        weight='medium'
                        color='primary'
                    >
                        {formatMonthsToString(Number(loanTerm))}
                    </Typography.Text>
                </div>
            )}
            <div className={cn('condition-item')} data-test-id='sum'>
                <Typography.Text
                    tag='div'
                    className={cn('condition-item-text')}
                    view='primary-medium'
                    color='secondary'
                >
                    Сумма
                </Typography.Text>
                <Typography.Text
                    tag='div'
                    className={cn('condition-item-text')}
                    view='primary-medium'
                    weight='medium'
                    color='primary'
                >
                    <AmountPure value={limit} transparentMinor={false} />
                </Typography.Text>
            </div>
            {!!requestInterestRateText && (
                <div className={cn('condition-item')} data-test-id='rate'>
                    <Typography.Text
                        tag='div'
                        className={cn('condition-item-text')}
                        view='primary-medium'
                        color='secondary'
                    >
                        Ставка
                    </Typography.Text>
                    <Typography.Text
                        tag='div'
                        className={cn('condition-item-text')}
                        view='primary-medium'
                        weight='medium'
                        color='primary'
                    >
                        {`${rate}% ${requestInterestRateText}`}
                    </Typography.Text>
                </div>
            )}
        </BackgroundPlate>
    );
};
