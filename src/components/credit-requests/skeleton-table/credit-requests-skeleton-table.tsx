import React from 'react';
import { createCn } from 'bem-react-classname';

import { Skeleton } from '@alfalab/core-components/skeleton';
import { Table } from '@alfalab/core-components/table';

import { ProductPaneSkeleton } from '#/src/components/skeletons/product-pane-skeleton';

import './credit-requests-skeleton-table.css';

const cn = createCn('skeleton-table');

const SkeletonTableRow = () => (
    <Table.TRow>
        <Table.TCell>
            <Skeleton className={cn('item')} animate={true} visible={true}>
                ~
            </Skeleton>
        </Table.TCell>
        <Table.TCell>
            <Skeleton className={cn('item')} animate={true} visible={true}>
                ~
            </Skeleton>
        </Table.TCell>
        <Table.TCell>
            <Skeleton className={cn('item')} animate={true} visible={true}>
                ~
            </Skeleton>
        </Table.TCell>
        <Table.TCell>
            <Skeleton className={cn('item')} animate={true} visible={true}>
                ~
            </Skeleton>
        </Table.TCell>
        <Table.TCell>
            <Skeleton className={cn('item')} animate={true} visible={true}>
                ~
            </Skeleton>
        </Table.TCell>
    </Table.TRow>
);

export const CreditRequestSkeletonTable = ({ isMobile }: { isMobile: boolean }) =>
    isMobile ? (
        <React.Fragment>
            <ProductPaneSkeleton />
            <ProductPaneSkeleton />
            <ProductPaneSkeleton />
            <ProductPaneSkeleton />
        </React.Fragment>
    ) : (
        <Table key='table'>
            <Table.THead>
                <Table.THeadCell>
                    <Skeleton className={cn('item')} animate={true} visible={true}>
                        ~
                    </Skeleton>
                </Table.THeadCell>
                <Table.THeadCell>
                    <Skeleton className={cn('item')} animate={true} visible={true}>
                        ~
                    </Skeleton>
                </Table.THeadCell>
                <Table.THeadCell>
                    <Skeleton className={cn('item')} animate={true} visible={true}>
                        ~
                    </Skeleton>
                </Table.THeadCell>
                <Table.THeadCell>
                    <Skeleton className={cn('item')} animate={true} visible={true}>
                        ~
                    </Skeleton>
                </Table.THeadCell>
                <Table.THeadCell>
                    <Skeleton className={cn('item')} animate={true} visible={true}>
                        ~
                    </Skeleton>
                </Table.THeadCell>
            </Table.THead>
            <Table.TBody>
                <SkeletonTableRow />
                <SkeletonTableRow />
                <SkeletonTableRow />
                <SkeletonTableRow />
                <SkeletonTableRow />
                <SkeletonTableRow />
            </Table.TBody>
        </Table>
    );
