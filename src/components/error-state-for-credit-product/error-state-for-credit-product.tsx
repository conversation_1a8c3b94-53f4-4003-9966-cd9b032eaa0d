import React from 'react';
import { createCn } from 'bem-react-classname';

import ScrewdriverPaintBrushMIcon from '@alfalab/icons-glyph/ScrewdriverPaintBrushMIcon';

import ErrorState from '../ui/error-state';

import './error-state-for-credit-products.css';

const cn = createCn('error-state-for-credit-products');

const ErrorStateForCreditProduct = ({ heading }: { heading?: React.ReactNode }) => {
    const handleRefetchButtonClick = () => {
        window.location.reload();
    };

    return (
        <div className={cn()}>
            {heading}
            <ErrorState
                icon={<ScrewdriverPaintBrushMIcon />}
                title='Не получилось загрузить'
                text='Уже исправляем. Попробуйте ещё раз или зайдите позже'
                textForButton='Попробовать ещё раз'
                onButtonClick={handleRefetchButtonClick}
                height={350}
                isRedesigned={true}
            />
        </div>
    );
};

export { ErrorStateForCreditProduct };
