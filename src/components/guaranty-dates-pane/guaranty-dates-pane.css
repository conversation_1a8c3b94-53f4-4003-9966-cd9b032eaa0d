@import '@alfalab/core-components/vars';

.guaranty-dates-pane {
    display: flex;
    flex-direction: column;

    @media (--small-only) {
        width: 100%;
    }

    &__icon {
        color: var(--color-light-bg-primary-inverted);
    }

    &__date-pane {
        margin-right: var(--gap-24);
        background-color: var(--color-white);
        border: 1px solid var(--color-dark-indigo-15-flat);
        border-radius: var(--border-radius-8);
        transition:
            border 350ms,
            box-shadow 350ms;
        box-sizing: border-box;
        padding: var(--gap-16);

        @media (--small-only) {
            margin-right: var(--gap-8);
        }
    }

    .date-pane {
        background-color: var(--color-white);
        border: 1px solid var(--color-dark-indigo-15-flat);
        border-radius: var(--border-radius-8);
        transition:
            border 350ms,
            box-shadow 350ms;
        box-sizing: border-box;
        padding: var(--gap-16);

        &_overdue {
            background-color: var(--color-white);
            border-left: 4px solid var(--color-red-error);
        }
    }
}
