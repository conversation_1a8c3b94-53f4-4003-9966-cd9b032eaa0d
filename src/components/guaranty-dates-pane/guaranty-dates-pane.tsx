import * as React from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type Amount as TAmount } from 'thrift-services/entities';
import { type UnixEpoch } from 'thrift-services/utils';

import { currentTimeSelector } from '#/src/ducks/settings/selectors';

import { SPACE_DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';
import AmountPure from '../ui/amount-pure';
import { BillCard } from '../ui/bill-card/bill-card';

import './guaranty-dates-pane.css';

const cn = createCn('guaranty-dates-pane');

type TOwnProps = {
    feeToPay?: TAmount;
    payFeeTillDate?: UnixEpoch;
    demandToPay?: TAmount;
    payDemandTillDate?: UnixEpoch;
};

type TProps = TOwnProps;

const GuarantyDatesPane: React.FC<TProps> = ({
    feeToPay,
    payFeeTillDate,
    demandToPay,
    payDemandTillDate,
}) => {
    const currentTime = useSelector(currentTimeSelector);

    return (
        <div className={cn()}>
            {!!feeToPay?.amount && !!payFeeTillDate && (
                <BillCard
                    title='Комиссия'
                    text={<AmountPure value={feeToPay} />}
                    subText={dateToCustomFormat(currentTime, payFeeTillDate, SPACE_DATE_FORMAT)}
                />
            )}
            {!!demandToPay?.amount && !!payDemandTillDate && (
                <BillCard
                    title='Оплата платежа по гарантии'
                    text={<AmountPure value={demandToPay} />}
                    subText={dateToCustomFormat(currentTime, payDemandTillDate, SPACE_DATE_FORMAT)}
                />
            )}
        </div>
    );
};

export default GuarantyDatesPane;
