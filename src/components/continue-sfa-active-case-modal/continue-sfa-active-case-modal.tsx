import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Modal } from '@alfalab/core-components/modal';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Text } from '@alfalab/core-components/typography/text';
import { Title } from '@alfalab/core-components/typography/title';

import { NBSP } from '#/src/constants/unicode-symbols';
import {
    isOnlineSigningAvailableSelector,
    isTotalOfferSelector,
    loanAmountSelector,
    loanMBIdSelector,
    productCodeSelector,
    scoringExpiryDateSelector,
    stageActiveCreditCaseSelector,
} from '#/src/ducks/attach-documents/selectors';
import {
    setContinueSFAActiveCaseModalVisible,
    setOnlineSigningChannel,
} from '#/src/ducks/credit-processing/actions';
import { isContinueSFAActiveCaseModalVisibleSelector } from '#/src/ducks/credit-processing/selectors';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { currentOrganizationTypeSelector } from '#/src/ducks/organization/selectors';
import { START_PAGE_METRICS } from '#/src/metrics';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import './continue-sfa-active-case-modal.css';

const cn = createCn('continue-sfa-active-case-modal');

export const ContinueSfaActiveCaseModal: React.FC = () => {
    const dispatch = useDispatch();
    const trackAlfaMetrics = useTrackAlfaMetrics();
    const [isDesktop] = useMatchMedia('--desktop-s');

    const isModalVisible = useSelector(isContinueSFAActiveCaseModalVisibleSelector);
    const loanMBId = useSelector(loanMBIdSelector);
    const isTotalOffer = useSelector(isTotalOfferSelector);
    const stage = useSelector(stageActiveCreditCaseSelector);
    const productCode = useSelector(productCodeSelector);
    const loanAmount = useSelector(loanAmountSelector);
    const isOnlineSigningAvailable = useSelector(isOnlineSigningAvailableSelector);
    const scoringExpiryDate = useSelector(scoringExpiryDateSelector);
    const organizationId = useSelector(currentHeaderOrganizationEqIdSelector);
    const currentOrganizationType = useSelector(currentOrganizationTypeSelector);

    const handleCloseModal = useCallback(() => {
        dispatch(setContinueSFAActiveCaseModalVisible(false));
    }, [dispatch]);

    const handleContinueActiveCase = () => {
        trackAlfaMetrics(START_PAGE_METRICS.clickContinueActiveCaseOnModal, {
            loanMBId,
            stage,
            productCode,
            loanAmount,
            isOnlineSigningAvailable,
            scoringExpiryDate,
            isTotalOffer,
            borrowerType: currentOrganizationType,
        });
        dispatch(setOnlineSigningChannel({ organizationId }));
        dispatch(setContinueSFAActiveCaseModalVisible(false));
    };

    if (isDesktop) {
        return (
            <Modal open={isModalVisible} hasCloser={true} onClose={handleCloseModal} size={500}>
                <Modal.Header>
                    <Title tag='div' color='primary' view='small' font='system'>
                        Вы уже начали оформлять другой кредит
                    </Title>
                </Modal.Header>
                <Modal.Content>
                    <Text color='primary'>
                        Продолжите его оформление или дождитесь звонка менеджера, чтобы закрыть
                        заявку. Условия по новому кредиту могут измениться
                    </Text>
                </Modal.Content>
                <Modal.Footer>
                    <Button size='s' view='primary' onClick={handleContinueActiveCase}>
                        Продолжить оформление
                    </Button>
                </Modal.Footer>
            </Modal>
        );
    }

    return (
        <BottomSheet
            open={isModalVisible}
            hasCloser={true}
            onClose={handleCloseModal}
            title='Вы уже начали оформлять другой кредит'
            className={cn()}
        >
            <Text className={cn('text')}>
                Продолжите его оформление или{NBSP}дождитесь звонка менеджера, чтобы{NBSP}закрыть
                заявку. Условия по{NBSP}новому кредиту могут измениться
            </Text>

            <Button size='m' view='primary' onClick={handleContinueActiveCase}>
                Продолжить оформление
            </Button>
        </BottomSheet>
    );
};
