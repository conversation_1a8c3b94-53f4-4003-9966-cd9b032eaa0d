import React, { useCallback, useMemo } from 'react';

import { Button } from '@alfalab/core-components/button';

import PaneItem from '#/src/components/ui/pane-item';
import { ECreditOffers } from '#/src/constants/credit-offers';
import { getIsCanApplyCreditOffer } from '#/src/constants/credit-products';
import { EOL } from '#/src/constants/unicode-symbols';
import { type TCreditOfferFeatures } from '#/src/types/credit-offers';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';

import { CreditOfferApplyButton } from './credit-offer-apply-button';
import { creditOfferItemRowsCn } from './credit-offer-item-rows-cn';

type Props = {
    featuresRow: TCreditOfferFeatures;
    offer: TMappedCreditOffer;
    onApplyClick(
        event: React.MouseEvent,
        offer: TMappedCreditOffer,
        isPreApprovedCreditLimit?: boolean,
    ): void;
    onDetailedButtonClick?: (event: React.MouseEvent) => void;
    applyDisabled?: boolean;
    colSpan?: number;
    isAdditionalRow?: boolean;
};

export const CreditOfferItemRow: React.FC<Props> = ({
    featuresRow,
    offer,
    onApplyClick,
    applyDisabled,
    colSpan = 1,
    isAdditionalRow,
    onDetailedButtonClick,
}) => {
    const applyClickHandler = useCallback(
        (event: React.MouseEvent, isPreApprovedCreditLimit?: boolean) => {
            onApplyClick(event, offer, isPreApprovedCreditLimit);
        },
        [onApplyClick, offer],
    );

    const bigText = useMemo(
        () => featuresRow.some(({ text }) => typeof text === 'string' && text.includes(EOL)),
        [featuresRow],
    );

    const actionsByOfferType = useMemo(() => {
        switch (offer.type) {
            case ECreditOffers.CREDIT_LIMIT:
                return (
                    <React.Fragment>
                        <Button
                            className={creditOfferItemRowsCn('credit-limit-button')}
                            onClick={onDetailedButtonClick}
                            size='s'
                        >
                            Подробнее
                        </Button>
                        <CreditOfferApplyButton
                            onClick={(e) => applyClickHandler(e, true)}
                            size='s'
                        >
                            Перейти к анкете
                        </CreditOfferApplyButton>
                    </React.Fragment>
                );
            default:
                if (!getIsCanApplyCreditOffer(offer.type)) return null;

                return (
                    <CreditOfferApplyButton
                        onClick={applyClickHandler}
                        isAdditionalRow={isAdditionalRow}
                        disabled={applyDisabled}
                    >
                        Оформить кредит
                    </CreditOfferApplyButton>
                );
        }
    }, [applyClickHandler, applyDisabled, isAdditionalRow, offer.type, onDetailedButtonClick]);

    return (
        <tr className={creditOfferItemRowsCn('tr')}>
            {featuresRow.map((feature, index) => (
                <td
                    className={creditOfferItemRowsCn('td', {
                        last: featuresRow.length === index + 1,
                    })}
                    key={`${feature.title}_${index}`}
                >
                    <PaneItem
                        className={creditOfferItemRowsCn('pane-item', {
                            'big-text': bigText,
                        })}
                        title={feature.title}
                        text={feature.text}
                        textWeight='bold'
                        withTooltip={!!feature.tooltipText}
                        textForTooltip={feature.tooltipText}
                    />
                </td>
            ))}
            <td className={creditOfferItemRowsCn('td')} colSpan={colSpan}>
                {actionsByOfferType}
            </td>
        </tr>
    );
};
