import React, { useState } from 'react';

import { Button, type ButtonProps } from '@alfalab/core-components/button';
import Tooltip from 'arui-private/tooltip';
import { Direction } from 'arui-private/tooltip/utils';
import BlockingContent from 'arui-private/tooltip/view/blocking';

import { creditOfferItemRowsCn } from '#/src/components/credit-offer-item-rows/credit-offer-item-rows-cn';

type Props = {
    onClick?(event: React.MouseEvent): void;
    disabled?: boolean;
    className?: string;
    children: React.ReactNode;
    isAdditionalRow?: boolean;
    size?: ButtonProps['size'];
};

export const CreditOfferApplyButton: React.FC<Props> = ({
    className,
    children,
    onClick,
    disabled,
    isAdditionalRow = false,
    size = 'xs',
}) => {
    const [tooltipBlockedButtonRef, setTooltipBlockedButtonRef] = useState<HTMLDivElement | null>(
        null,
    );

    if (!onClick) return null;

    return (
        <React.Fragment>
            <div ref={setTooltipBlockedButtonRef} className={creditOfferItemRowsCn('tooltip-aim')}>
                <Button
                    className={className}
                    onClick={onClick}
                    view='primary'
                    size={size}
                    disabled={disabled}
                >
                    {children}
                </Button>
            </div>

            {tooltipBlockedButtonRef && disabled && (
                <Tooltip
                    reference={tooltipBlockedButtonRef}
                    content={BlockingContent}
                    direction={isAdditionalRow ? Direction.BOTTOM : Direction.TOP}
                    trigger='hover'
                />
            )}
        </React.Fragment>
    );
};
