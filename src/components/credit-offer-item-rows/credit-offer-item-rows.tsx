import React, { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { useMatchMedia } from '@alfalab/core-components/mq';
import { Typography } from '@alfalab/core-components/typography';

import { ECreditOffers } from '#/src/constants/credit-offers';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { type TCreditOfferFeatures } from '#/src/types/credit-offers';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';
import { getCreditOffersFeaturesRow } from '#/src/view-utils/credit-offers';

import { CreditOfferApplyButton } from './credit-offer-apply-button';
import { CreditOfferItemRow } from './credit-offer-item-row';
import { creditOfferItemRowsCn } from './credit-offer-item-rows-cn';

type Props = {
    productInfo: TMappedCreditOffer;
    isCommonCheckBlockedOverdraft?: boolean;
    onGoToQuestionnaireButtonClick(
        event: React.MouseEvent,
        offer: TMappedCreditOffer,
        isPreApprovedCreditLimit?: boolean,
    ): void;
    onDetailedButtonClick(event: React.MouseEvent): void;
};

export const CreditOfferItemRows: React.FC<Props> = ({
    productInfo,
    isCommonCheckBlockedOverdraft,
    onGoToQuestionnaireButtonClick,
    onDetailedButtonClick,
}) => {
    const { type, description, additionalOffer } = productInfo;
    const currentTime = useSelector(currentTimeSelector);
    const [matches] = useMatchMedia('--tablet-m');

    // NOTE: Скрываем заголовки столбцов второго ряда, если они одинаковы и это большой экран
    const hideSameColTitlesInAdditionalOffer = useCallback(
        (
            additionalOfferFeatures: TCreditOfferFeatures,
            mainOfferFeatures: TCreditOfferFeatures,
        ): TCreditOfferFeatures =>
            additionalOfferFeatures.map((feature, index) => ({
                ...feature,
                title:
                    feature.title !== mainOfferFeatures[index]?.title || !matches
                        ? feature?.title
                        : '',
            })),
        [matches],
    );

    const OFFER_FEATURES_ROW: TCreditOfferFeatures = getCreditOffersFeaturesRow(
        productInfo,
        currentTime,
    );
    const OFFER_FEATURES_ADDITIONAL_ROW: TCreditOfferFeatures | null = additionalOffer
        ? hideSameColTitlesInAdditionalOffer(
              getCreditOffersFeaturesRow(additionalOffer, currentTime),
              OFFER_FEATURES_ROW,
          )
        : null;

    const colSpans = useMemo(() => {
        if (!OFFER_FEATURES_ADDITIONAL_ROW) return null;

        const difference = OFFER_FEATURES_ROW.length - OFFER_FEATURES_ADDITIONAL_ROW.length;

        return {
            mainRow: difference >= 0 ? 1 : Math.abs(difference) + 1,
            additionalRow: difference <= 0 ? 1 : difference + 1,
        };
    }, [OFFER_FEATURES_ROW, OFFER_FEATURES_ADDITIONAL_ROW]);

    if (type === ECreditOffers.BUSINESS_CREDIT_WITH_STATE_SUPPORT) {
        return (
            <div className={creditOfferItemRowsCn('gossuport-wrapper')}>
                <Typography.Text tag='div' view='primary-medium'>
                    {description}
                </Typography.Text>

                <CreditOfferApplyButton
                    className={creditOfferItemRowsCn('gossuport-button')}
                    onClick={(event) => onGoToQuestionnaireButtonClick(event, productInfo)}
                    disabled={isCommonCheckBlockedOverdraft}
                >
                    Перейти к анкете
                </CreditOfferApplyButton>
            </div>
        );
    }

    return (
        <div className={creditOfferItemRowsCn()}>
            <table
                className={creditOfferItemRowsCn('table', {
                    'credit-limit': type === ECreditOffers.CREDIT_LIMIT,
                })}
            >
                <CreditOfferItemRow
                    featuresRow={OFFER_FEATURES_ROW}
                    offer={productInfo}
                    onApplyClick={onGoToQuestionnaireButtonClick}
                    applyDisabled={isCommonCheckBlockedOverdraft}
                    colSpan={colSpans?.mainRow}
                    onDetailedButtonClick={onDetailedButtonClick}
                />

                {!!additionalOffer && (
                    <CreditOfferItemRow
                        featuresRow={OFFER_FEATURES_ADDITIONAL_ROW}
                        offer={additionalOffer}
                        onApplyClick={onGoToQuestionnaireButtonClick}
                        applyDisabled={isCommonCheckBlockedOverdraft}
                        colSpan={colSpans?.additionalRow}
                        isAdditionalRow={true}
                    />
                )}
            </table>
        </div>
    );
};
