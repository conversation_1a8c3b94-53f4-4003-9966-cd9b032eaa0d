@import '@alfalab/core-components/vars';

.credit-offer-item-rows {
    width: 100%;

    &__table {
        display: block;

        @media (--medium) {
            display: table;
            width: 100%;
            border-spacing: 0;
        }

        &_credit-limit {
            display: block;
        }
    }

    &__table_credit-limit & {
        &__tr {
            display: flex;
            flex-direction: row;
            align-items: center;
            flex-wrap: wrap;

            @media (--tablet-s) {
                justify-content: space-between;
            }
        }

        &__td {
            width: 50%;
            padding-bottom: var(--gap-24);

            @media (--tablet-s) {
                width: auto;
                padding-bottom: var(--gap-8);
            }

            &:last-child {
                @media (--small-only) {
                    width: 100%;
                    display: flex;
                    flex-direction: column-reverse;
                    padding-bottom: 0;

                    & > * {
                        margin-bottom: 0;

                        &:last-child {
                            margin-bottom: var(--gap-16);
                        }
                    }
                }

                @media (--tablet-s) and (--small-only) {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    grid-auto-flow: dense;
                    grid-gap: var(--gap-m);
                    margin-top: var(--gap-12);

                    & > * {
                        &:first-child {
                            grid-column: 2;
                        }

                        &:last-child {
                            grid-column: 1;
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
    }

    &__tr {
        display: block;

        @media (--medium) {
            display: table-row;
            vertical-align: top;
        }

        &:not(:last-child) {
            padding-bottom: var(--gap-8);

            @media (--medium) {
                padding-bottom: 0;
            }
        }
    }

    &__td {
        display: block;
        margin: 0;
        padding: 0 0 var(--gap-8);

        @media (--medium) {
            display: table-cell;
            margin: 0;
            padding: 0 var(--gap-8) 0 0;
            width: 1%;
            white-space: nowrap;

            &_last {
                width: auto;
                white-space: normal;
            }
        }

        &:last-child {
            width: auto;
            padding-top: var(--gap-4);
            text-align: end;

            @media (--medium) {
                padding: var(--gap-12) 0 0 0;
                width: auto;
                text-align: end;
            }
        }
    }

    &__pane-item {
        @media (--medium) {
            margin-right: 0;
        }

        @media (--desktop-m) {
            margin-right: var(--gap-48);
        }

        .pane-item__head {
            margin-bottom: 0;

            @media (--medium) {
                margin-bottom: var(--gap-4);
            }
        }

        &_big-text {
            .pane-item__text {
                @media (--medium) {
                    white-space: pre;
                    display: inline-block;
                    min-height: 3rem;
                }

                @media (--desktop-l) {
                    white-space: nowrap;
                    display: inline;
                }
            }
        }
    }

    &__tooltip-aim {
        display: block;

        button {
            width: 100%;
        }

        @media (--medium) {
            display: inline-block;

            button {
                width: auto;
            }
        }
    }

    &__gossuport-wrapper {
        display: flex;
        flex-direction: column;
        align-items: stretch;

        @media (--medium) {
            flex-direction: row;
            justify-content: space-between;
        }
    }

    &__gossuport-button {
        width: 100%;
        margin-top: var(--gap-16);

        @media (--medium) {
            align-self: flex-start;
            width: auto;
            min-width: 185px;
            margin-top: 0;
            margin-left: var(--gap-24);
        }
    }

    &__credit-limit-button {
        margin-right: 0;
        width: 100%;
        margin-bottom: var(--gap-16);

        @media (--tablet-m) {
            margin-right: var(--gap-16);
            margin-bottom: 0;
            width: auto;
        }
    }
}
