import React from 'react';
import { createCn } from 'bem-react-classname';

import { Button } from '@alfalab/core-components/button';
import { Typography } from '@alfalab/core-components/typography';
import { DocumentPdfMIcon } from '@alfalab/icons-glyph/DocumentPdfMIcon';
import { DownloadMIcon } from '@alfalab/icons-glyph/DownloadMIcon';

import { type SignedDocument } from '#/src/types/signed-document';

import './signed-credit-documents-item.css';

type Props = {
    name: string;
    content: SignedDocument['body'];
    onDownloadButtonClick: (content: SignedDocument['body'], name: string) => void;
};

const cn = createCn('signed-credit-documents-item');

const SignedCreditDocumentsItem: React.FC<Props> = ({ name, content, onDownloadButtonClick }) => (
    <div className={cn()}>
        <DocumentPdfMIcon className={cn('icon')} />
        <Typography.Text className={cn('name')}>{name}</Typography.Text>
        <div className={cn('download-button-wrapper')}>
            <Button view='ghost' onClick={() => onDownloadButtonClick(content, name)}>
                <DownloadMIcon className={cn('downloan-icon')} />
            </Button>
        </div>
    </div>
);

export default SignedCreditDocumentsItem;
