import React, { type MouseEvent } from 'react';
import { useSelector } from 'react-redux';
import { getEventBus, NIB_EVENT_BUS_KEY } from '@corp-front/client-event-bus';

import { Link, type LinkProps } from '@alfalab/core-components/link';

import { type EAdditionalDigitalSalesLandingsKeys } from '#/src/constants/credit-products';
import { DIGITAL_SALES_LANDING_MODAL_SET_LANDING_DATA_EVENT_NAME } from '#/src/constants/digital-sales';
import { digitalSalesLandingsSelector } from '#/src/ducks/settings/selectors';

type Props = {
    landingKey: EAdditionalDigitalSalesLandingsKeys;
} & Omit<LinkProps, 'onClick' | 'href'>;

export const DigitalLandingsLink: React.FC<Props> = ({ landingKey, children, ...linkProps }) => {
    const digitalSalesLandings = useSelector(digitalSalesLandingsSelector);

    const onClick = (e: MouseEvent) => {
        e?.stopPropagation();
        const landingId = digitalSalesLandings[landingKey];

        if (landingId) {
            const eventBus = getEventBus(NIB_EVENT_BUS_KEY);

            eventBus?.dispatchEvent(DIGITAL_SALES_LANDING_MODAL_SET_LANDING_DATA_EVENT_NAME, {
                landingId,
            });
        }
    };

    return (
        <Link view='default' {...linkProps} onClick={onClick}>
            {children}
        </Link>
    );
};
