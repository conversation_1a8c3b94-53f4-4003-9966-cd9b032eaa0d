import * as React from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { type TLoanPayment } from 'thrift-services/services/credit_products';
import { type UnixEpoch } from 'thrift-services/utils';

import SkeletonLabel from 'arui-private/skeleton-label';

import { currentTimeSelector } from '#/src/ducks/settings/selectors';

import GuarantyScheduleRow from '../guaranty-schedule-row';
import PaymentSchedule from '../payment-schedule';
import ScheduleCommonHeading from '../ui/schedule-common-heading';

import { GuarantySchedulePayment } from './guaranty-schedule-payment';

import './guaranty-schedule.css';

export const cn = createCn('guaranty-schedule');

type TOwnProps = {
    payments: TLoanPayment[];
    onTogglePopup?: (popupName?: string) => void;
    todayDate?: UnixEpoch | null;
    isFetching: boolean;
};

type TProps = TOwnProps;

const renderSkeleton = () =>
    Array.from(Array(7).keys()).map((key) => (
        <GuarantyScheduleRow
            key={key}
            first={
                <div className={cn('skeleton-row')}>
                    <SkeletonLabel size='xl' />
                </div>
            }
            second={
                <div className={cn('skeleton-row')}>
                    <SkeletonLabel size='xl' />
                </div>
            }
        />
    ));

const GuarantySchedule: React.FC<TProps> = ({ todayDate, onTogglePopup, payments, isFetching }) => {
    const currentTime = useSelector(currentTimeSelector);

    return (
        <PaymentSchedule
            heading={
                <ScheduleCommonHeading
                    paymentsCount={payments.length}
                    onTogglePopup={onTogglePopup}
                />
            }
            todayDate={todayDate}
            payments={<GuarantySchedulePayment currentTime={currentTime} payments={payments} />}
            skeleton={renderSkeleton()}
            tableHeaders={['Дата платежа', 'Сумма платежа']}
            isFetching={isFetching}
        />
    );
};

export default GuarantySchedule;
