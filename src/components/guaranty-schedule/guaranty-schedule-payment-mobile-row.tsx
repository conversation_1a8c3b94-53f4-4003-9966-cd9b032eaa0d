import * as React from 'react';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { Typography } from '@alfalab/core-components/typography';
import CorporateAmount from 'arui-private/corporate-amount';

import { DATE_FORMAT } from '../../constants/date';
import { dateToCustomFormat } from '../../utils/date';

import { cn } from './guaranty-schedule';

import './guaranty-schedule.css';

type TProps = {
    payment: TLoanPayment;
    currentTime: Date;
};

export const GuarantySchedulePaymentMobileRow: React.FC<TProps> = ({
    payment: { paymentDate, paymentAmount },
    currentTime,
}) => (
    <div className={cn('pane-group-header')}>
        {!!paymentDate && (
            <Typography.Text view='primary-medium' color='primary'>
                {dateToCustomFormat(currentTime, paymentDate, DATE_FORMAT)}
            </Typography.Text>
        )}
        {!!paymentAmount && <CorporateAmount bold='full' amount={paymentAmount} />}
    </div>
);
