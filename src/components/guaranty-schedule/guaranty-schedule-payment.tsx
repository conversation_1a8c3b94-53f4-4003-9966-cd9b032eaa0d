import * as React from 'react';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { Divider } from '@alfalab/core-components/divider';
import { useMatchMedia } from '@alfalab/core-components/mq';

import { GuarantySchedulePaymentDesktopRow } from './guaranty-schedule-payment-desktop-row';
import { GuarantySchedulePaymentMobileRow } from './guaranty-schedule-payment-mobile-row';

import './guaranty-schedule.css';

export type TGuarantySchedulePaymentProps = {
    payments: TLoanPayment[];
    currentTime: Date;
};

export const GuarantySchedulePayment: React.FC<TGuarantySchedulePaymentProps> = ({
    payments,
    currentTime,
}) => {
    const [isTablet] = useMatchMedia('--tablet-m');

    if (!payments.length) return null;

    return (
        <React.Fragment>
            {payments.map((payment, index) =>
                isTablet ? (
                    <GuarantySchedulePaymentDesktopRow
                        key={`guaranty-schedule-payment__${index}`}
                        payment={payment}
                        index={index}
                        currentTime={currentTime}
                    />
                ) : (
                    <React.Fragment key={`guaranty-schedule-payment__${index}`}>
                        <GuarantySchedulePaymentMobileRow
                            payment={payment}
                            currentTime={currentTime}
                        />
                        {index !== payments.length - 1 && <Divider />}
                    </React.Fragment>
                ),
            )}
        </React.Fragment>
    );
};
